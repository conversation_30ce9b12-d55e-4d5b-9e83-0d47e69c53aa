#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug script for auto scoring functionality
"""

import sys
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from gui_main import MainWindow

def debug_auto_scoring():
    """Debug auto scoring functionality"""
    app = QApplication(sys.argv)
    
    # Create main window
    print("Creating MainWindow...")
    window = MainWindow()
    window.show()
    
    print("\n=== Auto Scoring Debug Test ===")
    
    # Get references
    checkbox = window.auto_scoring_checkbox
    status_label = window.auto_scoring_status
    video_thread = window.video_thread
    
    print(f"Checkbox object: {checkbox}")
    print(f"Status label object: {status_label}")
    print(f"Video thread object: {video_thread}")
    
    print(f"\nInitial state:")
    print(f"  Checkbox checked: {checkbox.isChecked()}")
    print(f"  Status text: '{status_label.text()}'")
    print(f"  VideoThread enabled: {video_thread.auto_scoring_enabled}")
    
    # Test direct method call
    print(f"\nTesting direct method call with Qt.Checked ({Qt.Checked})...")
    window.on_auto_scoring_toggled(Qt.Checked)
    print(f"  Status text: '{status_label.text()}'")
    print(f"  VideoThread enabled: {video_thread.auto_scoring_enabled}")
    
    # Test direct method call with Qt.Unchecked
    print(f"\nTesting direct method call with Qt.Unchecked ({Qt.Unchecked})...")
    window.on_auto_scoring_toggled(Qt.Unchecked)
    print(f"  Status text: '{status_label.text()}'")
    print(f"  VideoThread enabled: {video_thread.auto_scoring_enabled}")
    
    print("\n=== Manual Testing ===")
    print("Please manually click the 'Enable Auto Scoring' checkbox in the GUI")
    print("and observe the console output and status changes.")
    print("Close the window when done testing.")
    
    # Run the application
    return app.exec()

if __name__ == "__main__":
    debug_auto_scoring()
