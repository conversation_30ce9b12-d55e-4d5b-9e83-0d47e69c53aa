<?xml version="1.0"?>
<!--
// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtPdfWidgets"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE"
            doc-package="PySide6.QtPdf">
    <load-typesystem name="typesystem_core.xml" generate="no"/>
    <load-typesystem name="typesystem_gui.xml" generate="no"/>
    <load-typesystem name="typesystem_widgets.xml" generate="no"/>
    <load-typesystem name="typesystem_pdf.xml" generate="no"/>
    <object-type name="QPdfPageSelector" since="6.6"/>
    <object-type name="QPdfView">
        <enum-type name="PageMode"/>
        <enum-type name="ZoomMode"/>
    </object-type>
</typesystem>
