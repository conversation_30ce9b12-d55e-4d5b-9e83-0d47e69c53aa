# 自動評分流程控制邏輯

## 問題描述

原本的自動評分系統在人物離開畫面並顯示總分對話框後，會立即重置評分狀態，導致可以立即開始下一次評分流程。這不符合用戶的期望，用戶希望等到對話框關閉且畫面中沒有人物時才能重新開始評分。

## 解決方案

### 新增狀態變數

在 `VideoThread` 類中新增 `dialog_showing` 狀態變數來追蹤對話框是否正在顯示：

```python
self.dialog_showing = False  # Track if result dialog is showing
```

### 新增信號

新增 `dialog_closed` 信號來通知對話框關閉事件：

```python
dialog_closed = Signal()  # Signal for when dialog is closed
```

### 修改評分流程邏輯

#### 1. 開始評分會話的條件

修改 `handle_auto_scoring` 方法，在開始新的評分會話時檢查對話框狀態：

```python
# Don't start new session if dialog is showing
if (not self.scoring_session_active and 
    not self.dialog_showing and 
    self.person_detected_frames >= 5):
    self.start_scoring_session()
```

#### 2. 結束評分會話的處理

修改 `end_scoring_session` 方法，不立即重置狀態，而是標記對話框正在顯示：

```python
self.dialog_showing = True  # Mark dialog as showing
self.auto_scoring_result.emit(result)
# Don't reset session here - wait for dialog to close
```

#### 3. 對話框關閉後的處理

新增 `on_dialog_closed` 方法來處理對話框關閉事件：

```python
def on_dialog_closed(self):
    self.dialog_showing = False
    
    # If no person is detected, reset and ready for next session
    if self.person_absent_frames >= 5:
        self.reset_scoring_session()
    else:
        # Keep waiting for person to leave
        pass
```

#### 4. 人物離開的額外檢查

在 `handle_auto_scoring` 方法中添加額外的檢查，處理對話框關閉後人物才離開的情況：

```python
# Check if dialog was closed but person still present, now person left
elif (not self.scoring_session_active and 
      not self.dialog_showing and 
      self.person_absent_frames >= 5 and
      (self.angle_scores or self.speed_scores)):  # Had previous session data
    self.reset_scoring_session()
```

### 信號連接

在 `MainWindow` 中連接對話框關閉信號：

```python
def show_auto_scoring_result(self, result):
    dialog = AutoScoringResultDialog(result, self)
    dialog.finished.connect(lambda: self.video_thread.dialog_closed.emit())
    dialog.exec()
```

## 流程狀態圖

```
[等待人物] → [檢測到人物] → [開始評分會話] → [收集數據]
                                                    ↓
[準備下次評分] ← [重置狀態] ← [對話框關閉且無人物] ← [人物離開]
                                                    ↓
[等待對話框關閉] ← [對話框顯示中] ← [計算並顯示結果]
       ↓
[檢查是否有人物] → [有人物：等待離開] → [無人物：重置狀態]
```

## 測試場景

### 場景1：正常流程
1. 人物進入畫面 → 開始評分
2. 人物離開畫面 → 顯示結果對話框
3. 對話框自動關閉 → 準備下次評分

### 場景2：對話框顯示期間有新人物
1. 人物A離開 → 顯示結果對話框
2. 人物B進入畫面 → 不開始新評分（對話框仍顯示）
3. 對話框關閉 → 檢測到人物B仍在 → 等待人物B離開
4. 人物B離開 → 準備下次評分

### 場景3：手動關閉對話框
1. 人物離開 → 顯示結果對話框
2. 用戶點擊"Close Now" → 對話框立即關閉
3. 檢查畫面中是否有人物 → 相應處理

## 調試信息

系統會在控制台輸出詳細的調試信息：

```
Auto scoring: Session started - collecting gait data...
Auto scoring: Person left - calculating final score...
Auto scoring: Final score 75.3 (good) - showing result dialog
Auto scoring: Dialog closed
Auto scoring: No person detected, ready for next session
```

或者：

```
Auto scoring: Dialog closed
Auto scoring: Person still detected, waiting for person to leave
Auto scoring: Person left after dialog closed - ready for next session
```

## 配置參數

相關的閾值參數在 `settings.yaml` 中配置：

```yaml
auto_scoring:
  detection:
    enter_threshold: 5      # 確認進入的幀數
    exit_threshold: 10      # 確認離開的幀數
    min_session_duration: 3.0  # 最小會話時間
```

## 注意事項

1. 對話框顯示期間，系統不會開始新的評分會話
2. 對話框關閉後，系統會檢查當前畫面狀態決定下一步動作
3. 只有在對話框關閉且畫面中沒有人物時，才會重置狀態準備下次評分
4. 這確保了每次評分流程的完整性和用戶體驗的一致性
