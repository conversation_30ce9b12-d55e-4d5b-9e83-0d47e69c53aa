[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "AbstractGraph3D"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: AbstractGraph3D."}], "className": "AbstractDeclarative", "enums": [{"isClass": false, "isFlag": true, "name": "SelectionFlag", "values": ["SelectionNone", "SelectionItem", "SelectionRow", "SelectionItemAndRow", "SelectionColumn", "SelectionItemAndColumn", "SelectionRowAndColumn", "SelectionItemRowAndColumn", "SelectionSlice", "SelectionMultiSeries"]}, {"alias": "SelectionFlag", "isClass": false, "isFlag": true, "name": "SelectionFlags", "values": ["SelectionNone", "SelectionItem", "SelectionRow", "SelectionItemAndRow", "SelectionColumn", "SelectionItemAndColumn", "SelectionRowAndColumn", "SelectionItemRowAndColumn", "SelectionSlice", "SelectionMultiSeries"]}, {"isClass": false, "isFlag": false, "name": "ShadowQuality", "values": ["ShadowQualityNone", "ShadowQualityLow", "ShadowQualityMedium", "ShadowQualityHigh", "ShadowQualitySoftLow", "ShadowQualitySoftMedium", "ShadowQualitySoftHigh"]}, {"isClass": false, "isFlag": false, "name": "ElementType", "values": ["ElementNone", "ElementSeries", "ElementAxisXLabel", "ElementAxisYLabel", "ElementAxisZLabel", "ElementCustomItem"]}, {"isClass": false, "isFlag": false, "name": "RenderingMode", "values": ["RenderDirectToBackground", "RenderDirectToBackground_NoClear", "RenderIndirect"]}, {"isClass": false, "isFlag": true, "name": "OptimizationHint", "values": ["OptimizationDefault", "OptimizationStatic"]}, {"alias": "OptimizationHint", "isClass": false, "isFlag": true, "name": "OptimizationHints", "values": ["OptimizationDefault", "OptimizationStatic"]}], "lineNumber": 33, "methods": [{"access": "public", "index": 27, "name": "clearSelection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstract3DSeries*"}], "index": 28, "name": "hasSeries", "returnType": "bool", "revision": 1539}, {"access": "public", "arguments": [{"name": "item", "type": "QCustom3DItem*"}], "index": 29, "name": "addCustomItem", "returnType": "int", "revision": 257}, {"access": "public", "index": 30, "name": "removeCustomItems", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "item", "type": "QCustom3DItem*"}], "index": 31, "name": "removeCustomItem", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "position", "type": "QVector3D"}], "index": 32, "name": "removeCustomItemAt", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "item", "type": "QCustom3DItem*"}], "index": 33, "name": "releaseCustomItem", "returnType": "void", "revision": 257}, {"access": "public", "index": 34, "isConst": true, "name": "selectedLabelIndex", "returnType": "int", "revision": 257}, {"access": "public", "index": 35, "isConst": true, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "QAbstract3DAxis*", "revision": 257}, {"access": "public", "index": 36, "isConst": true, "name": "selectedCustomItemIndex", "returnType": "int", "revision": 257}, {"access": "public", "index": 37, "isConst": true, "name": "selectedCustomItem", "returnType": "QCustom3DItem*", "revision": 257}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "selectionMode", "notify": "selectionMode<PERSON>hanged", "read": "selectionMode", "required": false, "scriptable": true, "stored": true, "type": "SelectionFlags", "user": false, "write": "setSelectionMode"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "shadowQuality", "notify": "shadowQualityChanged", "read": "shadowQuality", "required": false, "scriptable": true, "stored": true, "type": "ShadowQuality", "user": false, "write": "setShadowQuality"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "shadowsSupported", "notify": "shadowsSupportedChanged", "read": "shadowsSupported", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "msaaSamples", "notify": "msaaSamplesChanged", "read": "msaaSamples", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMsaaSamples"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "scene", "notify": "sceneChanged", "read": "scene", "required": false, "scriptable": true, "stored": true, "type": "Declarative3DScene*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "inputHandler", "notify": "inputHandlerChanged", "read": "inputHandler", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DInputHandler*", "user": false, "write": "setInputHandler"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "theme", "notify": "themeChanged", "read": "theme", "required": false, "scriptable": true, "stored": true, "type": "Q3DTheme*", "user": false, "write": "setTheme"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "renderingMode", "notify": "renderingModeChanged", "read": "renderingMode", "required": false, "scriptable": true, "stored": true, "type": "RenderingMode", "user": false, "write": "setRenderingMode"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "measureFps", "notify": "measureFpsChanged", "read": "measureFps", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMeasureFps"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "currentFps", "notify": "currentFpsChanged", "read": "currentFps", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "customItemList", "read": "customItemList", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QQmlListProperty<QCustom3DItem>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "orthoProjection", "notify": "orthoProjectionChanged", "read": "isOrthoProjection", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setOrthoProjection"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "selectedElement", "notify": "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "selectedElement", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "ElementType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "aspectRatio", "notify": "aspectRatioChanged", "read": "aspectRatio", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAspectRatio"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "optimizationHints", "notify": "optimizationHintsChanged", "read": "optimizationHints", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "OptimizationHints", "user": false, "write": "setOptimizationHints"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "polar", "notify": "polarChanged", "read": "isPolar", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPolar"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "radialLabelOffset", "notify": "radialLabelOffsetChanged", "read": "radialLabelOffset", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadialLabelOffset"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "horizontalAspectRatio", "notify": "horizontalAspectRatioChanged", "read": "horizontalAspectRatio", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setHorizontalAspectRatio"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "reflection", "notify": "reflectionChanged", "read": "isReflection", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReflection"}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "reflectivity", "notify": "reflectivityChanged", "read": "reflectivity", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setReflectivity"}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "locale", "notify": "localeChanged", "read": "locale", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QLocale", "user": false, "write": "setLocale"}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "queriedGraphPosition", "notify": "queriedGraphPositionChanged", "read": "queriedGraphPosition", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QVector3D", "user": false}, {"constant": false, "designable": true, "final": false, "index": 22, "name": "margin", "notify": "marginChanged", "read": "margin", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "AbstractDeclarative", "signals": [{"access": "public", "arguments": [{"name": "mode", "type": "AbstractDeclarative::SelectionFlags"}], "index": 0, "name": "selectionMode<PERSON>hanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "quality", "type": "AbstractDeclarative::ShadowQuality"}], "index": 1, "name": "shadowQualityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "supported", "type": "bool"}], "index": 2, "name": "shadowsSupportedChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "samples", "type": "int"}], "index": 3, "name": "msaaSamplesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scene", "type": "Q3DScene*"}], "index": 4, "name": "sceneChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "inputHandler", "type": "QAbstract3DInputHandler*"}], "index": 5, "name": "inputHandlerChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "theme", "type": "Q3DTheme*"}], "index": 6, "name": "themeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "AbstractDeclarative::RenderingMode"}], "index": 7, "name": "renderingModeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 8, "name": "measureFpsChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "fps", "type": "qreal"}], "index": 9, "name": "currentFpsChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "type", "type": "AbstractDeclarative::ElementType"}], "index": 10, "name": "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 11, "name": "orthoProjectionChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "ratio", "type": "qreal"}], "index": 12, "name": "aspectRatioChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "hints", "type": "AbstractDeclarative::OptimizationHints"}], "index": 13, "name": "optimizationHintsChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 14, "name": "polarChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "offset", "type": "float"}], "index": 15, "name": "radialLabelOffsetChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "ratio", "type": "qreal"}], "index": 16, "name": "horizontalAspectRatioChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 17, "name": "reflectionChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "reflectivity", "type": "qreal"}], "index": 18, "name": "reflectivityChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "locale", "type": "QLocale"}], "index": 19, "name": "localeChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "data", "type": "QVector3D"}], "index": 20, "name": "queriedGraphPositionChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "margin", "type": "qreal"}], "index": 21, "name": "marginChanged", "returnType": "void", "revision": 258}], "slots": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 22, "name": "handleAxisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 23, "name": "handleAxisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 24, "name": "handleAxisZChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "obj", "type": "QObject*"}], "index": 25, "name": "windowDestroyed", "returnType": "void"}, {"access": "public", "index": 26, "name": "destroyContext", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}, {"access": "public", "name": "AbstractDeclarativeInterface"}]}], "inputFile": "abstractdeclarative_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "ColorGradientStop", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}], "qualifiedClassName": "ColorGradientStop", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "qreal"}], "index": 0, "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 1, "name": "colorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "stops"}, {"name": "QML.Element", "value": "auto"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "ColorGradient", "lineNumber": 54, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "stops", "read": "stops", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<ColorGradientStop>", "user": false}], "qualifiedClassName": "ColorGradient", "signals": [{"access": "public", "index": 0, "name": "updated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "colorgradient_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "seriesList"}, {"name": "QML.Element", "value": "Bars3D"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "DeclarativeBars", "lineNumber": 23, "methods": [{"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "index": 15, "name": "addSeries", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "index": 16, "name": "removeSeries", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "series", "type": "QBar3DSeries*"}], "index": 17, "name": "insertSeries", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rowAxis", "notify": "rowAxisChanged", "read": "rowAxis", "required": false, "scriptable": true, "stored": true, "type": "QCategory3DAxis*", "user": false, "write": "setRowAxis"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "valueAxis", "notify": "valueAxisChanged", "read": "valueAxis", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setValueAxis"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "columnAxis", "notify": "columnAxisChanged", "read": "columnAxis", "required": false, "scriptable": true, "stored": true, "type": "QCategory3DAxis*", "user": false, "write": "setColumnAxis"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "multiSeriesUniform", "notify": "multiSeriesUniformChanged", "read": "isMultiSeriesUniform", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMultiSeriesUniform"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "barThickness", "notify": "barThicknessChanged", "read": "barThickness", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBarThickness"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "barSpacing", "notify": "barSpacingChanged", "read": "barSpacing", "required": false, "scriptable": true, "stored": true, "type": "QSizeF", "user": false, "write": "setBarSpacing"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "barSpacingRelative", "notify": "barSpacingRelativeChanged", "read": "isBarSpacingRelative", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBarSpacingRelative"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "barSeriesMargin", "notify": "barSeriesMarginChanged", "read": "barSeriesMargin", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "QSizeF", "user": false, "write": "setBarSeriesMargin"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "seriesList", "read": "seriesList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QBar3DSeries>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "selectedSeries", "notify": "selectedSeriesChanged", "read": "selectedSeries", "required": false, "scriptable": true, "stored": true, "type": "QBar3DSeries*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "primarySeries", "notify": "primarySeriesChanged", "read": "primarySeries", "required": false, "scriptable": true, "stored": true, "type": "QBar3DSeries*", "user": false, "write": "setPrimarySeries"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "floorLevel", "notify": "floorLevelChanged", "read": "floorLevel", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setFloorLevel"}], "qualifiedClassName": "DeclarativeBars", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QCategory3DAxis*"}], "index": 0, "name": "rowAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 1, "name": "valueAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QCategory3DAxis*"}], "index": 2, "name": "columnAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "uniform", "type": "bool"}], "index": 3, "name": "multiSeriesUniformChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "thicknessRatio", "type": "float"}], "index": 4, "name": "barThicknessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "spacing", "type": "QSizeF"}], "index": 5, "name": "barSpacingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "relative", "type": "bool"}], "index": 6, "name": "barSpacingRelativeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "margin", "type": "QSizeF"}], "index": 7, "name": "barSeriesMarginChanged", "returnType": "void", "revision": 1539}, {"access": "public", "arguments": [{"name": "filename", "type": "QString"}], "index": 8, "name": "meshFileNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "index": 9, "name": "primarySeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "index": 10, "name": "selectedSeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "level", "type": "float"}], "index": 11, "name": "floorLevelChanged", "returnType": "void", "revision": 258}], "slots": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 12, "name": "handleAxisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 13, "name": "handleAxisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 14, "name": "handleAxisZChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractDeclarative"}]}], "inputFile": "declarativebars_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ThemeColor"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "DeclarativeColor", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}], "qualifiedClassName": "DeclarativeColor", "signals": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 0, "name": "colorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "declarativecolor_p.h", "outputRevision": 69}, {"classes": [{"className": "DeclarativeRenderNode", "lineNumber": 31, "object": true, "qualifiedClassName": "DeclarativeRenderNode", "slots": [{"access": "public", "index": 0, "name": "handleControllerDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QSGGeometryNode"}]}], "inputFile": "declarativerendernode_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "seriesList"}, {"name": "QML.Element", "value": "Scatter3D"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "DeclarativeScatter", "lineNumber": 25, "methods": [{"access": "public", "arguments": [{"name": "series", "type": "QScatter3DSeries*"}], "index": 7, "name": "addSeries", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QScatter3DSeries*"}], "index": 8, "name": "removeSeries", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisZ", "notify": "axisZChanged", "read": "axisZ", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisZ"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "selectedSeries", "notify": "selectedSeriesChanged", "read": "selectedSeries", "required": false, "scriptable": true, "stored": true, "type": "QScatter3DSeries*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "seriesList", "read": "seriesList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QScatter3DSeries>", "user": false}], "qualifiedClassName": "DeclarativeScatter", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 2, "name": "axisZChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QScatter3DSeries*"}], "index": 3, "name": "selectedSeriesChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 4, "name": "handleAxisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 5, "name": "handleAxisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 6, "name": "handleAxisZChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractDeclarative"}]}], "inputFile": "declarativescatter_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Scene3D"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: Scene3D."}], "className": "Declarative3DScene", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "selectionQueryPosition", "notify": "selectionQueryPositionChanged", "read": "selectionQueryPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false, "write": "setSelectionQueryPosition"}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "invalidSelectionPoint", "read": "invalidSelectionPoint", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false}], "qualifiedClassName": "Declarative3DScene", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "QPointF"}], "index": 0, "name": "selectionQueryPositionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Q3DScene"}]}], "inputFile": "declarativescene_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "Bar3DSeries"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "DeclarativeBar3DSeries", "lineNumber": 34, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "<PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false, "write": "setSelectedBar"}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "invalidSelectionPosition", "read": "invalidSelectionPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "baseGradient", "notify": "baseGradientChanged", "read": "baseGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setBaseGradient"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "singleHighlightGradient", "notify": "singleHighlightGradientChanged", "read": "singleHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setSingleHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "multiHighlightGradient", "notify": "multiHighlightGradientChanged", "read": "multiHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setMultiHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "rowColors", "read": "rowColors", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "QQmlListProperty<DeclarativeColor>", "user": false}], "qualifiedClassName": "DeclarativeBar3DSeries", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "QPointF"}], "index": 0, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "index": 1, "name": "baseGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "index": 2, "name": "singleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "index": 3, "name": "multiHighlightGradientChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 4, "name": "handleBaseGradientUpdate", "returnType": "void"}, {"access": "public", "index": 5, "name": "handleSingleHighlightGradientUpdate", "returnType": "void"}, {"access": "public", "index": 6, "name": "handleMultiHighlightGradientUpdate", "returnType": "void"}, {"access": "public", "index": 7, "name": "handleRowColorUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QBar3DSeries"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "Scatter3DSeries"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "DeclarativeScatter3DSeries", "lineNumber": 104, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "baseGradient", "notify": "baseGradientChanged", "read": "baseGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setBaseGradient"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "singleHighlightGradient", "notify": "singleHighlightGradientChanged", "read": "singleHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setSingleHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "multiHighlightGradient", "notify": "multiHighlightGradientChanged", "read": "multiHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setMultiHighlightGradient"}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "invalidSelectionIndex", "read": "invalidSelectionIndex", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "DeclarativeScatter3DSeries", "signals": [{"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "index": 0, "name": "baseGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "index": 1, "name": "singleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "index": 2, "name": "multiHighlightGradientChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 3, "name": "handleBaseGradientUpdate", "returnType": "void"}, {"access": "public", "index": 4, "name": "handleSingleHighlightGradientUpdate", "returnType": "void"}, {"access": "public", "index": 5, "name": "handleMultiHighlightGradientUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QScatter3DSeries"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "Surface3DSeries"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "DeclarativeSurface3DSeries", "lineNumber": 150, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "selectedPoint", "notify": "selectedPointChanged", "read": "selectedPoint", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false, "write": "setSelectedPoint"}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "invalidSelectionPosition", "read": "invalidSelectionPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "baseGradient", "notify": "baseGradientChanged", "read": "baseGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setBaseGradient"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "singleHighlightGradient", "notify": "singleHighlightGradientChanged", "read": "singleHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setSingleHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "multiHighlightGradient", "notify": "multiHighlightGradientChanged", "read": "multiHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setMultiHighlightGradient"}], "qualifiedClassName": "DeclarativeSurface3DSeries", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "QPointF"}], "index": 0, "name": "selectedPointChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "index": 1, "name": "baseGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "index": 2, "name": "singleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "index": 3, "name": "multiHighlightGradientChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 4, "name": "handleBaseGradientUpdate", "returnType": "void"}, {"access": "public", "index": 5, "name": "handleSingleHighlightGradientUpdate", "returnType": "void"}, {"access": "public", "index": 6, "name": "handleMultiHighlightGradientUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSurface3DSeries"}]}], "inputFile": "declarativeseries_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "seriesList"}, {"name": "QML.Element", "value": "Surface3D"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "DeclarativeSurface", "lineNumber": 25, "methods": [{"access": "public", "arguments": [{"name": "series", "type": "QSurface3DSeries*"}], "index": 8, "name": "addSeries", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QSurface3DSeries*"}], "index": 9, "name": "removeSeries", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisZ", "notify": "axisZChanged", "read": "axisZ", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisZ"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "selectedSeries", "notify": "selectedSeriesChanged", "read": "selectedSeries", "required": false, "scriptable": true, "stored": true, "type": "QSurface3DSeries*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "seriesList", "read": "seriesList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QSurface3DSeries>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "flipHorizontalGrid", "notify": "flipHorizontalGridChanged", "read": "flipHorizontalGrid", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFlipHorizontalGrid"}], "qualifiedClassName": "DeclarativeSurface", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 2, "name": "axisZChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QSurface3DSeries*"}], "index": 3, "name": "selectedSeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "flip", "type": "bool"}], "index": 4, "name": "flipHorizontalGridChanged", "returnType": "void", "revision": 258}], "slots": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 5, "name": "handleAxisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 6, "name": "handleAxisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 7, "name": "handleAxisZChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractDeclarative"}]}], "inputFile": "declarativesurface_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "themeChildren"}, {"name": "QML.Element", "value": "Theme3D"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "DeclarativeTheme3D", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "themeChildren", "read": "themeChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "baseColors", "read": "baseColors", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<DeclarativeColor>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "baseGradients", "read": "baseGradients", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<ColorGradient>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "singleHighlightGradient", "notify": "singleHighlightGradientChanged", "read": "singleHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setSingleHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "multiHighlightGradient", "notify": "multiHighlightGradientChanged", "read": "multiHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setMultiHighlightGradient"}], "qualifiedClassName": "DeclarativeTheme3D", "signals": [{"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "index": 0, "name": "singleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "index": 1, "name": "multiHighlightGradientChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Q3DTheme"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "declarativetheme_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Foreign", "value": "Q3DScene"}], "className": "Q3DSceneForeign", "gadget": true, "lineNumber": 79, "qualifiedClassName": "Q3DSceneForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Camera3D"}, {"name": "QML.Foreign", "value": "Q3DCamera"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "Q3DCameraDataVisForeign", "gadget": true, "lineNumber": 87, "qualifiedClassName": "Q3DCameraDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Light3D"}, {"name": "QML.Foreign", "value": "Q3DLight"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "Q3DLightDataVisForeign", "gadget": true, "lineNumber": 88, "qualifiedClassName": "Q3DLightDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "CategoryAxis3D"}, {"name": "QML.Foreign", "value": "QCategory3DAxis"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QCategory3DAxisDataVisForeign", "gadget": true, "lineNumber": 89, "qualifiedClassName": "QCategory3DAxisDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "HeightMapSurfaceDataProxy"}, {"name": "QML.Foreign", "value": "QHeightMapSurfaceDataProxy"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QHeightMapSurfaceDataProxyDataVisForeign", "gadget": true, "lineNumber": 90, "qualifiedClassName": "QHeightMapSurfaceDataProxyDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ItemModelBarDataProxy"}, {"name": "QML.Foreign", "value": "QItemModelBarDataProxy"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QItemModelBarDataProxyDataVisForeign", "gadget": true, "lineNumber": 91, "qualifiedClassName": "QItemModelBarDataProxyDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ItemModelScatterDataProxy"}, {"name": "QML.Foreign", "value": "QItemModelScatterDataProxy"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QItemModelScatterDataProxyDataVisForeign", "gadget": true, "lineNumber": 92, "qualifiedClassName": "QItemModelScatterDataProxyDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ItemModelSurfaceDataProxy"}, {"name": "QML.Foreign", "value": "QItemModelSurfaceDataProxy"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QItemModelSurfaceDataProxyDataVisForeign", "gadget": true, "lineNumber": 93, "qualifiedClassName": "QItemModelSurfaceDataProxyDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ValueAxis3D"}, {"name": "QML.Foreign", "value": "QValue3DAxis"}, {"name": "QML.AddedInVersion", "value": "256"}], "className": "QValue3DAxisDataVisForeign", "gadget": true, "lineNumber": 94, "qualifiedClassName": "QValue3DAxisDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Custom3DItem"}, {"name": "QML.Foreign", "value": "QCustom3DItem"}, {"name": "QML.AddedInVersion", "value": "257"}], "className": "QCustom3DItemDataVisForeign", "gadget": true, "lineNumber": 96, "qualifiedClassName": "QCustom3DItemDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Custom3DLabel"}, {"name": "QML.Foreign", "value": "QCustom3DLabel"}, {"name": "QML.AddedInVersion", "value": "257"}], "className": "QCustom3DLabelDataVisForeign", "gadget": true, "lineNumber": 97, "qualifiedClassName": "QCustom3DLabelDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "LogValueAxis3DFormatter"}, {"name": "QML.Foreign", "value": "QLogValue3DAxisFormatter"}, {"name": "QML.AddedInVersion", "value": "257"}], "className": "QLogValue3DAxisFormatterDataVisForeign", "gadget": true, "lineNumber": 98, "qualifiedClassName": "QLogValue3DAxisFormatterDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ValueAxis3DFormatter"}, {"name": "QML.Foreign", "value": "QValue3DAxisFormatter"}, {"name": "QML.AddedInVersion", "value": "257"}], "className": "QValue3DAxisFormatterDataVisForeign", "gadget": true, "lineNumber": 99, "qualifiedClassName": "QValue3DAxisFormatterDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "InputHandler3D"}, {"name": "QML.Foreign", "value": "Q3DInputHandler"}, {"name": "QML.AddedInVersion", "value": "258"}], "className": "Q3DInputHandlerDataVisForeign", "gadget": true, "lineNumber": 101, "qualifiedClassName": "Q3DInputHandlerDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Custom3DVolume"}, {"name": "QML.Foreign", "value": "QCustom3DVolume"}, {"name": "QML.AddedInVersion", "value": "258"}], "className": "QCustom3DVolumeDataVisForeign", "gadget": true, "lineNumber": 102, "qualifiedClassName": "QCustom3DVolumeDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "TouchInputHandler3D"}, {"name": "QML.Foreign", "value": "QTouch3DInputHandler"}, {"name": "QML.AddedInVersion", "value": "258"}], "className": "QTouch3DInputHandlerDataVisForeign", "gadget": true, "lineNumber": 103, "qualifiedClassName": "QTouch3DInputHandlerDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Q3DTheme"}, {"name": "QML.Foreign", "value": "Q3DTheme"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: Q3DTheme, use Theme3D instead."}], "className": "Q3DThemeDataVisForeign", "gadget": true, "lineNumber": 105, "qualifiedClassName": "Q3DThemeDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "QBar3DSeries"}, {"name": "QML.Foreign", "value": "QBar3DSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: QBar3DSeries, use Bar3DSeries instead."}], "className": "QBar3DSeriesDataVisForeign", "gadget": true, "lineNumber": 106, "qualifiedClassName": "QBar3DSeriesDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "QScatter3DSeries"}, {"name": "QML.Foreign", "value": "QScatter3DSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: QScatter3DSeries, use Scatter3DSeries instead."}], "className": "QScatter3DSeriesDataVisForeign", "gadget": true, "lineNumber": 107, "qualifiedClassName": "QScatter3DSeriesDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "QSurface3DSeries"}, {"name": "QML.Foreign", "value": "QSurface3DSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: QSurface3DSeries, use Surface3DSeries instead."}], "className": "QSurface3DSeriesDataVisForeign", "gadget": true, "lineNumber": 108, "qualifiedClassName": "QSurface3DSeriesDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Object3D"}, {"name": "QML.Foreign", "value": "Q3DObject"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: Object3D."}], "className": "Q3DObjectDataVisForeign", "gadget": true, "lineNumber": 110, "qualifiedClassName": "Q3DObjectDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AbstractAxis3D"}, {"name": "QML.Foreign", "value": "QAbstract3DAxis"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: AbstractAxis3D."}], "className": "QAbstract3DAxisDataVisForeign", "gadget": true, "lineNumber": 111, "qualifiedClassName": "QAbstract3DAxisDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AbstractInputHandler3D"}, {"name": "QML.Foreign", "value": "QAbstract3DInputHandler"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: AbstractInputHandler3D."}], "className": "QAbstract3DInputHandlerDataVisForeign", "gadget": true, "lineNumber": 112, "qualifiedClassName": "QAbstract3DInputHandlerDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Abstract3DSeries"}, {"name": "QML.Foreign", "value": "QAbstract3DSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: Abstract3DSeries."}], "className": "QAbstract3DSeriesDataVisForeign", "gadget": true, "lineNumber": 113, "qualifiedClassName": "QAbstract3DSeriesDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AbstractDataProxy"}, {"name": "QML.Foreign", "value": "QAbstractDataProxy"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: AbstractDataProxy."}], "className": "QAbstractDataProxyDataVisForeign", "gadget": true, "lineNumber": 114, "qualifiedClassName": "QAbstractDataProxyDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AbstractItemModel"}, {"name": "QML.Foreign", "value": "QAbstractItemModel"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: AbstractItemModel."}], "className": "QAbstractItemModelDataVisForeign", "gadget": true, "lineNumber": 115, "qualifiedClassName": "QAbstractItemModelDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "BarDataProxy"}, {"name": "QML.Foreign", "value": "QBarDataProxy"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: BarDataProxy."}], "className": "QBarDataProxyDataVisForeign", "gadget": true, "lineNumber": 116, "qualifiedClassName": "QBarDataProxyDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ScatterDataProxy"}, {"name": "QML.Foreign", "value": "QScatterDataProxy"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: ScatterDataProxy."}], "className": "QScatterDataProxyDataVisForeign", "gadget": true, "lineNumber": 117, "qualifiedClassName": "QScatterDataProxyDataVisForeign"}, {"classInfos": [{"name": "QML.Element", "value": "SurfaceDataProxy"}, {"name": "QML.Foreign", "value": "QSurfaceDataProxy"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: SurfaceDataProxy."}], "className": "QSurfaceDataProxyDataVisForeign", "gadget": true, "lineNumber": 118, "qualifiedClassName": "QSurfaceDataProxyDataVisForeign"}], "inputFile": "foreigntypes_p.h", "outputRevision": 69}, {"classes": [{"className": "GLStateStore", "lineNumber": 21, "object": true, "qualifiedClassName": "GLStateStore", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "protected", "name": "QOpenGLFunctions"}]}], "inputFile": "glstatestore_p.h", "outputRevision": 69}]