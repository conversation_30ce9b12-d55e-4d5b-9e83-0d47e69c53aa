#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for auto scoring flow control logic
"""

import sys
import time
from unittest.mock import Mock

# Add the code directory to the path
sys.path.append('code')

from gui_main import VideoThread

def test_flow_control():
    """Test the auto scoring flow control logic"""
    
    print("Testing auto scoring flow control logic...")
    
    # Create a mock video thread
    thread = VideoThread()
    thread.auto_scoring_enabled = True
    
    # Mock the emit method
    thread.auto_scoring_result = Mock()
    thread.auto_scoring_result.emit = Mock()
    
    print("\n=== Test Case 1: Normal flow ===")
    
    # Simulate person entering
    print("1. Person enters scene")
    for i in range(6):
        thread.handle_auto_scoring(True, 75, 80)
        print(f"   Frame {i+1}: person_detected_frames={thread.person_detected_frames}, session_active={thread.scoring_session_active}")
    
    # Simulate person leaving
    print("2. Person leaves scene")
    for i in range(11):
        thread.handle_auto_scoring(False, 0, 0)
        print(f"   Frame {i+1}: person_absent_frames={thread.person_absent_frames}, dialog_showing={thread.dialog_showing}")
    
    # Check if dialog would be shown
    if thread.auto_scoring_result.emit.called:
        print("3. Dialog would be shown")
        print(f"   dialog_showing={thread.dialog_showing}")
        
        # Simulate dialog closing with no person
        print("4. Dialog closes with no person present")
        thread.on_dialog_closed()
        print(f"   After dialog close: dialog_showing={thread.dialog_showing}, session_active={thread.scoring_session_active}")
    
    print("\n=== Test Case 2: Person enters while dialog showing ===")
    
    # Reset for next test
    thread.reset_scoring_session()
    thread.auto_scoring_result.emit.reset_mock()
    
    # Simulate first person session
    print("1. First person completes session")
    for i in range(6):
        thread.handle_auto_scoring(True, 75, 80)
    for i in range(11):
        thread.handle_auto_scoring(False, 0, 0)
    
    print(f"   Dialog showing: {thread.dialog_showing}")
    
    # Simulate second person entering while dialog is showing
    print("2. Second person enters while dialog is showing")
    for i in range(6):
        thread.handle_auto_scoring(True, 85, 90)
        print(f"   Frame {i+1}: Can start new session? {not thread.scoring_session_active and not thread.dialog_showing}")
    
    # Dialog closes with person still present
    print("3. Dialog closes with person still present")
    thread.on_dialog_closed()
    print(f"   After dialog close: dialog_showing={thread.dialog_showing}")
    
    # Person leaves
    print("4. Person leaves after dialog closed")
    for i in range(6):
        thread.handle_auto_scoring(False, 0, 0)
        print(f"   Frame {i+1}: person_absent_frames={thread.person_absent_frames}")
    
    print(f"   Final state: session_active={thread.scoring_session_active}, dialog_showing={thread.dialog_showing}")
    
    print("\n=== Test completed ===")

if __name__ == "__main__":
    test_flow_control()
