import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QAbstractActionInput"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Input/AbstractActionInput 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512]
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QAbstractAxisInput"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Input/AbstractAxisInput 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512]
        Property {
            name: "sourceDevice"
            type: "Qt3DInput::QAbstractPhysicalDevice"
            isPointer: true
            read: "sourceDevice"
            write: "setSourceDevice"
            notify: "sourceDeviceChanged"
            index: 0
        }
        Signal {
            name: "sourceDeviceChanged"
            Parameter { name: "sourceDevice"; type: "QAbstractPhysicalDevice"; isPointer: true }
        }
        Method {
            name: "setSourceDevice"
            Parameter { name: "sourceDevice"; type: "QAbstractPhysicalDevice"; isPointer: true }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QAbstractPhysicalDevice"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        extension: "Qt3DInput::Input::Quick::Quick3DPhysicalDevice"
        exports: ["Qt3D.Input/AbstractPhysicalDevice 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512]
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QAction"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        extension: "Qt3DInput::Input::Quick::Quick3DAction"
        exports: ["Qt3D.Input/Action 2.0"]
        exportMetaObjectRevisions: [512]
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            notify: "activeChanged"
            index: 0
            isReadonly: true
        }
        Signal {
            name: "activeChanged"
            Parameter { name: "isActive"; type: "bool" }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QActionInput"
        accessSemantics: "reference"
        prototype: "Qt3DInput::QAbstractActionInput"
        exports: ["Qt3D.Input/ActionInput 2.0"]
        exportMetaObjectRevisions: [512]
        Property {
            name: "sourceDevice"
            type: "Qt3DInput::QAbstractPhysicalDevice"
            isPointer: true
            read: "sourceDevice"
            write: "setSourceDevice"
            notify: "sourceDeviceChanged"
            index: 0
        }
        Property {
            name: "buttons"
            type: "int"
            isList: true
            read: "buttons"
            write: "setButtons"
            notify: "buttonsChanged"
            index: 1
        }
        Signal {
            name: "sourceDeviceChanged"
            Parameter { name: "sourceDevice"; type: "QAbstractPhysicalDevice"; isPointer: true }
        }
        Signal {
            name: "buttonsChanged"
            Parameter { name: "buttons"; type: "int"; isList: true }
        }
        Method {
            name: "setSourceDevice"
            Parameter { name: "sourceDevice"; type: "QAbstractPhysicalDevice"; isPointer: true }
        }
        Method {
            name: "setButtons"
            Parameter { name: "buttons"; type: "int"; isList: true }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QAnalogAxisInput"
        accessSemantics: "reference"
        prototype: "Qt3DInput::QAbstractAxisInput"
        exports: ["Qt3D.Input/AnalogAxisInput 2.0"]
        exportMetaObjectRevisions: [512]
        Property {
            name: "axis"
            type: "int"
            read: "axis"
            write: "setAxis"
            notify: "axisChanged"
            index: 0
        }
        Signal {
            name: "axisChanged"
            Parameter { name: "axis"; type: "int" }
        }
        Method {
            name: "setAxis"
            Parameter { name: "axis"; type: "int" }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QAxisAccumulator"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Input/AxisAccumulator 2.1"]
        exportMetaObjectRevisions: [513]
        Enum {
            name: "SourceAxisType"
            values: ["Velocity", "Acceleration"]
        }
        Property {
            name: "sourceAxis"
            type: "Qt3DInput::QAxis"
            isPointer: true
            read: "sourceAxis"
            write: "setSourceAxis"
            notify: "sourceAxisChanged"
            index: 0
        }
        Property {
            name: "sourceAxisType"
            type: "SourceAxisType"
            read: "sourceAxisType"
            write: "setSourceAxisType"
            notify: "sourceAxisTypeChanged"
            index: 1
        }
        Property {
            name: "scale"
            type: "float"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 2
        }
        Property {
            name: "value"
            type: "float"
            read: "value"
            notify: "valueChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "velocity"
            type: "float"
            read: "velocity"
            notify: "velocityChanged"
            index: 4
            isReadonly: true
        }
        Signal {
            name: "sourceAxisChanged"
            Parameter { name: "sourceAxis"; type: "Qt3DInput::QAxis"; isPointer: true }
        }
        Signal {
            name: "sourceAxisTypeChanged"
            Parameter { name: "sourceAxisType"; type: "QAxisAccumulator::SourceAxisType" }
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "velocityChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "scaleChanged"
            Parameter { name: "scale"; type: "float" }
        }
        Method {
            name: "setSourceAxis"
            Parameter { name: "sourceAxis"; type: "Qt3DInput::QAxis"; isPointer: true }
        }
        Method {
            name: "setSourceAxisType"
            Parameter { name: "sourceAxisType"; type: "QAxisAccumulator::SourceAxisType" }
        }
        Method {
            name: "setScale"
            Parameter { name: "scale"; type: "float" }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QAxis"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        extension: "Qt3DInput::Input::Quick::Quick3DAxis"
        exports: ["Qt3D.Input/Axis 2.0"]
        exportMetaObjectRevisions: [512]
        Property {
            name: "value"
            type: "float"
            read: "value"
            notify: "valueChanged"
            index: 0
            isReadonly: true
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "float" }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QAxisSetting"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Input/AxisSetting 2.0"]
        exportMetaObjectRevisions: [512]
        Property {
            name: "deadZoneRadius"
            type: "float"
            read: "deadZoneRadius"
            write: "setDeadZoneRadius"
            notify: "deadZoneRadiusChanged"
            index: 0
        }
        Property {
            name: "axes"
            type: "int"
            isList: true
            read: "axes"
            write: "setAxes"
            notify: "axesChanged"
            index: 1
        }
        Property {
            name: "smooth"
            type: "bool"
            read: "isSmoothEnabled"
            write: "setSmoothEnabled"
            notify: "smoothChanged"
            index: 2
        }
        Signal {
            name: "deadZoneRadiusChanged"
            Parameter { name: "deadZoneRadius"; type: "float" }
        }
        Signal {
            name: "axesChanged"
            Parameter { name: "axes"; type: "int"; isList: true }
        }
        Signal {
            name: "smoothChanged"
            Parameter { name: "smooth"; type: "bool" }
        }
        Method {
            name: "setDeadZoneRadius"
            Parameter { name: "deadZoneRadius"; type: "float" }
        }
        Method {
            name: "setAxes"
            Parameter { name: "axes"; type: "int"; isList: true }
        }
        Method {
            name: "setSmoothEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QButtonAxisInput"
        accessSemantics: "reference"
        prototype: "Qt3DInput::QAbstractAxisInput"
        exports: ["Qt3D.Input/ButtonAxisInput 2.0"]
        exportMetaObjectRevisions: [512]
        Property {
            name: "scale"
            type: "float"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 0
        }
        Property {
            name: "buttons"
            type: "int"
            isList: true
            read: "buttons"
            write: "setButtons"
            notify: "buttonsChanged"
            index: 1
        }
        Property {
            name: "acceleration"
            type: "float"
            read: "acceleration"
            write: "setAcceleration"
            notify: "accelerationChanged"
            index: 2
        }
        Property {
            name: "deceleration"
            type: "float"
            read: "deceleration"
            write: "setDeceleration"
            notify: "decelerationChanged"
            index: 3
        }
        Signal {
            name: "scaleChanged"
            Parameter { name: "scale"; type: "float" }
        }
        Signal {
            name: "buttonsChanged"
            Parameter { name: "buttons"; type: "int"; isList: true }
        }
        Signal {
            name: "accelerationChanged"
            Parameter { name: "acceleration"; type: "float" }
        }
        Signal {
            name: "decelerationChanged"
            Parameter { name: "deceleration"; type: "float" }
        }
        Method {
            name: "setScale"
            Parameter { name: "scale"; type: "float" }
        }
        Method {
            name: "setButtons"
            Parameter { name: "buttons"; type: "int"; isList: true }
        }
        Method {
            name: "setAcceleration"
            Parameter { name: "acceleration"; type: "float" }
        }
        Method {
            name: "setDeceleration"
            Parameter { name: "deceleration"; type: "float" }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QInputChord"
        accessSemantics: "reference"
        prototype: "Qt3DInput::QAbstractActionInput"
        extension: "Qt3DInput::Input::Quick::Quick3DInputChord"
        exports: ["Qt3D.Input/InputChord 2.0"]
        exportMetaObjectRevisions: [512]
        Property {
            name: "timeout"
            type: "int"
            read: "timeout"
            write: "setTimeout"
            notify: "timeoutChanged"
            index: 0
        }
        Signal {
            name: "timeoutChanged"
            Parameter { name: "timeout"; type: "int" }
        }
        Method {
            name: "setTimeout"
            Parameter { name: "timeout"; type: "int" }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QInputSequence"
        accessSemantics: "reference"
        prototype: "Qt3DInput::QAbstractActionInput"
        extension: "Qt3DInput::Input::Quick::Quick3DInputSequence"
        exports: ["Qt3D.Input/InputSequence 2.0"]
        exportMetaObjectRevisions: [512]
        Property {
            name: "timeout"
            type: "int"
            read: "timeout"
            write: "setTimeout"
            notify: "timeoutChanged"
            index: 0
        }
        Property {
            name: "buttonInterval"
            type: "int"
            read: "buttonInterval"
            write: "setButtonInterval"
            notify: "buttonIntervalChanged"
            index: 1
        }
        Signal {
            name: "timeoutChanged"
            Parameter { name: "timeout"; type: "int" }
        }
        Signal {
            name: "buttonIntervalChanged"
            Parameter { name: "buttonInterval"; type: "int" }
        }
        Method {
            name: "setTimeout"
            Parameter { name: "timeout"; type: "int" }
        }
        Method {
            name: "setButtonInterval"
            Parameter { name: "buttonInterval"; type: "int" }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QInputSettings"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Input/InputSettings 2.0"]
        exportMetaObjectRevisions: [512]
        Property {
            name: "eventSource"
            type: "QObject"
            isPointer: true
            read: "eventSource"
            write: "setEventSource"
            notify: "eventSourceChanged"
            index: 0
        }
        Signal {
            name: "eventSourceChanged"
            Parameter { type: "QObject"; isPointer: true }
        }
        Method {
            name: "setEventSource"
            Parameter { name: "eventSource"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QKeyEvent"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["Qt3D.Input/KeyEvent 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512]
        Property {
            name: "key"
            type: "int"
            read: "key"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "modifiers"
            type: "int"
            read: "modifiers"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "isAutoRepeat"
            type: "bool"
            read: "isAutoRepeat"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "nativeScanCode"
            type: "uint"
            read: "nativeScanCode"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property { name: "accepted"; type: "bool"; read: "isAccepted"; write: "setAccepted"; index: 6 }
        Method {
            name: "matches"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "key_"; type: "QKeySequence::StandardKey" }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QKeyboardDevice"
        accessSemantics: "reference"
        prototype: "Qt3DInput::QAbstractPhysicalDevice"
        exports: ["Qt3D.Input/KeyboardDevice 2.0"]
        exportMetaObjectRevisions: [512]
        Property {
            name: "activeInput"
            type: "Qt3DInput::QKeyboardHandler"
            isPointer: true
            read: "activeInput"
            notify: "activeInputChanged"
            index: 0
            isReadonly: true
        }
        Signal {
            name: "activeInputChanged"
            Parameter { name: "activeInput"; type: "QKeyboardHandler"; isPointer: true }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QKeyboardHandler"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Input/KeyboardHandler 2.0"]
        exportMetaObjectRevisions: [512]
        Property {
            name: "sourceDevice"
            type: "Qt3DInput::QKeyboardDevice"
            isPointer: true
            read: "sourceDevice"
            write: "setSourceDevice"
            notify: "sourceDeviceChanged"
            index: 0
        }
        Property {
            name: "focus"
            type: "bool"
            read: "focus"
            write: "setFocus"
            notify: "focusChanged"
            index: 1
        }
        Signal {
            name: "sourceDeviceChanged"
            Parameter { name: "keyboardDevice"; type: "QKeyboardDevice"; isPointer: true }
        }
        Signal {
            name: "focusChanged"
            Parameter { name: "focus"; type: "bool" }
        }
        Signal {
            name: "digit0Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit1Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit2Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit3Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit4Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit5Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit6Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit7Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit8Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit9Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "leftPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "rightPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "upPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "downPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "tabPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "backtabPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "asteriskPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "numberSignPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "escapePressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "returnPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "enterPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "deletePressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "spacePressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "backPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "cancelPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "selectPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "yesPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "noPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context1Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context2Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context3Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context4Pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "callPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "hangupPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "flipPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "menuPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "volumeUpPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "volumeDownPressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "pressed"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "event"; type: "Qt3DInput::QKeyEvent"; isPointer: true }
        }
        Method {
            name: "setSourceDevice"
            Parameter { name: "keyboardDevice"; type: "Qt3DInput::QKeyboardDevice"; isPointer: true }
        }
        Method {
            name: "setFocus"
            Parameter { name: "focus"; type: "bool" }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QLogicalDevice"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        extension: "Qt3DInput::Input::Quick::Quick3DLogicalDevice"
        exports: ["Qt3D.Input/LogicalDevice 2.0"]
        exportMetaObjectRevisions: [512]
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QMouseDevice"
        accessSemantics: "reference"
        prototype: "Qt3DInput::QAbstractPhysicalDevice"
        exports: ["Qt3D.Input/MouseDevice 2.0"]
        exportMetaObjectRevisions: [512]
        Enum {
            name: "Axis"
            values: ["X", "Y", "WheelX", "WheelY"]
        }
        Property {
            name: "sensitivity"
            type: "float"
            read: "sensitivity"
            write: "setSensitivity"
            notify: "sensitivityChanged"
            index: 0
        }
        Property {
            name: "updateAxesContinuously"
            revision: 65295
            type: "bool"
            read: "updateAxesContinuously"
            write: "setUpdateAxesContinuously"
            notify: "updateAxesContinuouslyChanged"
            index: 1
        }
        Signal {
            name: "sensitivityChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "updateAxesContinuouslyChanged"
            Parameter { name: "updateAxesContinuously"; type: "bool" }
        }
        Method {
            name: "setSensitivity"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setUpdateAxesContinuously"
            Parameter { name: "updateAxesContinuously"; type: "bool" }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QMouseHandler"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Input/MouseHandler 2.0"]
        exportMetaObjectRevisions: [512]
        Property {
            name: "sourceDevice"
            type: "Qt3DInput::QMouseDevice"
            isPointer: true
            read: "sourceDevice"
            write: "setSourceDevice"
            notify: "sourceDeviceChanged"
            index: 0
        }
        Property {
            name: "containsMouse"
            type: "bool"
            read: "containsMouse"
            notify: "containsMouseChanged"
            index: 1
            isReadonly: true
        }
        Signal {
            name: "sourceDeviceChanged"
            Parameter { name: "mouseDevice"; type: "QMouseDevice"; isPointer: true }
        }
        Signal {
            name: "containsMouseChanged"
            Parameter { name: "containsMouse"; type: "bool" }
        }
        Signal {
            name: "clicked"
            Parameter { name: "mouse"; type: "Qt3DInput::QMouseEvent"; isPointer: true }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "mouse"; type: "Qt3DInput::QMouseEvent"; isPointer: true }
        }
        Signal { name: "entered" }
        Signal { name: "exited" }
        Signal {
            name: "pressed"
            Parameter { name: "mouse"; type: "Qt3DInput::QMouseEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "mouse"; type: "Qt3DInput::QMouseEvent"; isPointer: true }
        }
        Signal {
            name: "pressAndHold"
            Parameter { name: "mouse"; type: "Qt3DInput::QMouseEvent"; isPointer: true }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "mouse"; type: "Qt3DInput::QMouseEvent"; isPointer: true }
        }
        Signal {
            name: "wheel"
            Parameter { name: "wheel"; type: "Qt3DInput::QWheelEvent"; isPointer: true }
        }
        Method {
            name: "setSourceDevice"
            Parameter { name: "mouseDevice"; type: "QMouseDevice"; isPointer: true }
        }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QMouseEvent"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["Qt3D.Input/MouseEvent 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512]
        Enum {
            name: "Buttons"
            values: [
                "LeftButton",
                "RightButton",
                "MiddleButton",
                "BackButton",
                "NoButton"
            ]
        }
        Enum {
            name: "Modifiers"
            values: [
                "NoModifier",
                "ShiftModifier",
                "ControlModifier",
                "AltModifier",
                "MetaModifier",
                "KeypadModifier"
            ]
        }
        Property { name: "x"; type: "int"; read: "x"; index: 0; isReadonly: true; isPropertyConstant: true }
        Property { name: "y"; type: "int"; read: "y"; index: 1; isReadonly: true; isPropertyConstant: true }
        Property {
            name: "wasHeld"
            type: "bool"
            read: "wasHeld"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "button"
            type: "Qt3DInput::QMouseEvent::Buttons"
            read: "button"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "buttons"
            type: "int"
            read: "buttons"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "modifiers"
            type: "Qt3DInput::QMouseEvent::Modifiers"
            read: "modifiers"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property { name: "accepted"; type: "bool"; read: "isAccepted"; write: "setAccepted"; index: 6 }
    }
    Component {
        file: "qt3dquick3dinputforeign_p.h"
        name: "Qt3DInput::QWheelEvent"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["Qt3D.Input/WheelEvent 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512]
        Enum {
            name: "Buttons"
            values: [
                "LeftButton",
                "RightButton",
                "MiddleButton",
                "BackButton",
                "NoButton"
            ]
        }
        Enum {
            name: "Modifiers"
            values: [
                "NoModifier",
                "ShiftModifier",
                "ControlModifier",
                "AltModifier",
                "MetaModifier",
                "KeypadModifier"
            ]
        }
        Property { name: "x"; type: "int"; read: "x"; index: 0; isReadonly: true; isPropertyConstant: true }
        Property { name: "y"; type: "int"; read: "y"; index: 1; isReadonly: true; isPropertyConstant: true }
        Property {
            name: "angleDelta"
            type: "QPoint"
            read: "angleDelta"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "buttons"
            type: "int"
            read: "buttons"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "modifiers"
            type: "Qt3DInput::QWheelEvent::Modifiers"
            read: "modifiers"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property { name: "accepted"; type: "bool"; read: "isAccepted"; write: "setAccepted"; index: 5 }
    }
    Component {
        file: "quick3daction_p.h"
        name: "Qt3DInput::Input::Quick::Quick3DAction"
        accessSemantics: "reference"
        defaultProperty: "inputs"
        prototype: "QObject"
        Property {
            name: "inputs"
            type: "Qt3DInput::QAbstractActionInput"
            isList: true
            read: "qmlActionInputs"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "quick3daxis_p.h"
        name: "Qt3DInput::Input::Quick::Quick3DAxis"
        accessSemantics: "reference"
        defaultProperty: "inputs"
        prototype: "QObject"
        Property {
            name: "inputs"
            type: "Qt3DInput::QAbstractAxisInput"
            isList: true
            read: "qmlAxisInputs"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "quick3dinputchord_p.h"
        name: "Qt3DInput::Input::Quick::Quick3DInputChord"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "chords"
            type: "Qt3DInput::QAbstractActionInput"
            isList: true
            read: "qmlActionInputs"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "quick3dinputsequence_p.h"
        name: "Qt3DInput::Input::Quick::Quick3DInputSequence"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "sequences"
            type: "Qt3DInput::QAbstractActionInput"
            isList: true
            read: "qmlActionInputs"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "quick3dlogicaldevice_p.h"
        name: "Qt3DInput::Input::Quick::Quick3DLogicalDevice"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "axes"
            type: "Qt3DInput::QAxis"
            isList: true
            read: "qmlAxes"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "actions"
            type: "Qt3DInput::QAction"
            isList: true
            read: "qmlActions"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "quick3dphysicaldevice_p.h"
        name: "Qt3DInput::Input::Quick::Quick3DPhysicalDevice"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "axisSettings"
            type: "Qt3DInput::QAxisSetting"
            isList: true
            read: "axisSettings"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
    }
}
