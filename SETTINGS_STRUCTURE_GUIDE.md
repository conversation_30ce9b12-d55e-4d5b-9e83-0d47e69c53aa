# Settings.yaml 配置結構說明

## 配置區塊概覽

settings.yaml 文件分為以下主要區塊：

### 1. `gait_analysis` - 步態分析核心配置
```yaml
gait_analysis:
  angle_thresholds:     # 角度評分閾值
  speed_thresholds:     # 速度評分閾值
  scoring:              # 評分權重（全域使用）
  smoothing:            # 平滑處理參數
```

### 2. `auto_scoring` - 自動評分系統配置
```yaml
auto_scoring:
  enabled:              # 是否啟用自動評分
  detection:            # 人物檢測參數
  calculation:          # 計算參數（不包含權重）
  result_display:       # 結果顯示設定
```

### 3. `visualization` - 視覺化設定
```yaml
visualization:
  progress_bar:         # 進度條設定
```

### 4. `gui` - 圖形界面設定
```yaml
gui:
  window:               # 視窗設定
  colors:               # 顏色主題
```

### 5. `system` - 系統參數
```yaml
system:
  pixel_to_meter:       # 像素轉換比例
  fps_target:           # 目標幀率
```

## 重要配置說明

### 評分權重統一管理

**所有評分權重都在 `gait_analysis.scoring` 中設定：**

```yaml
gait_analysis:
  scoring:
    angle_weight: 0.3    # 角度權重（30%）
    speed_weight: 0.7    # 速度權重（70%）
```

**使用場景：**
- 即時步態評分（`gait_analyzer.py`）
- 自動評分系統（`gui_main.py`）
- 所有需要綜合評分的地方

**為什麼統一？**
- 避免配置重複和不一致
- 確保所有評分使用相同的權重標準
- 簡化配置管理

### 速度標準設定

```yaml
gait_analysis:
  speed_thresholds:
    excellent: 1.1      # 優良：≥1.1 m/s
    good: 0.8           # 一般：0.8-1.1 m/s
    # 不佳：<0.8 m/s（自動判斷）
```

### 自動評分專用參數

```yaml
auto_scoring:
  detection:
    enter_threshold: 5              # 確認進入的幀數
    exit_threshold: 10              # 確認離開的幀數
    min_session_duration: 3.0       # 最小會話時間
  calculation:
    min_samples: 5                  # 最小樣本數
```

## 參數使用對照表

| 參數 | 位置 | 使用者 | 用途 |
|------|------|--------|------|
| `angle_weight` | `gait_analysis.scoring` | 所有評分功能 | 角度在總分中的權重 |
| `speed_weight` | `gait_analysis.scoring` | 所有評分功能 | 速度在總分中的權重 |
| `excellent/good` | `gait_analysis.speed_thresholds` | 速度評分 | 速度等級判斷閾值 |
| `min_samples` | `auto_scoring.calculation` | 自動評分 | 有效評分最小樣本數 |
| `enter_threshold` | `auto_scoring.detection` | 自動評分 | 確認人物進入的幀數 |
| `exit_threshold` | `auto_scoring.detection` | 自動評分 | 確認人物離開的幀數 |

## 配置最佳實踐

### 1. 權重設定原則
```yaml
# 確保權重總和為 1.0
gait_analysis:
  scoring:
    angle_weight: 0.3    # 30%
    speed_weight: 0.7    # 70%
    # 0.3 + 0.7 = 1.0 ✓
```

### 2. 速度閾值設定
```yaml
# 確保 excellent > good
gait_analysis:
  speed_thresholds:
    excellent: 1.1       # 必須大於 good
    good: 0.8           # 基準速度
```

### 3. 檢測參數調整
```yaml
auto_scoring:
  detection:
    enter_threshold: 5   # 建議 3-10 幀
    exit_threshold: 10   # 建議 5-15 幀
    min_session_duration: 3.0  # 建議 2-5 秒
```

## 常見配置錯誤

### ❌ 錯誤：權重不等於 1.0
```yaml
gait_analysis:
  scoring:
    angle_weight: 0.6
    speed_weight: 0.5    # 0.6 + 0.5 = 1.1 ≠ 1.0
```

### ✅ 正確：權重總和為 1.0
```yaml
gait_analysis:
  scoring:
    angle_weight: 0.3
    speed_weight: 0.7    # 0.3 + 0.7 = 1.0
```

### ❌ 錯誤：速度閾值順序錯誤
```yaml
gait_analysis:
  speed_thresholds:
    excellent: 0.8       # 應該大於 good
    good: 1.1
```

### ✅ 正確：速度閾值遞增
```yaml
gait_analysis:
  speed_thresholds:
    excellent: 1.1       # 大於 good
    good: 0.8
```

## 配置驗證

使用測試腳本驗證配置：

```bash
python test_speed_thresholds.py
```

檢查項目：
- ✓ 權重總和是否為 1.0
- ✓ 速度閾值是否合理
- ✓ 配置是否正確載入
- ✓ 評分結果是否符合預期

## 配置修改建議

### 1. 調整評分重點
```yaml
# 更重視速度（適合康復評估）
gait_analysis:
  scoring:
    angle_weight: 0.2
    speed_weight: 0.8

# 更重視角度（適合姿態分析）
gait_analysis:
  scoring:
    angle_weight: 0.8
    speed_weight: 0.2
```

### 2. 調整速度標準
```yaml
# 較嚴格的標準
gait_analysis:
  speed_thresholds:
    excellent: 1.3
    good: 1.0

# 較寬鬆的標準
gait_analysis:
  speed_thresholds:
    excellent: 0.9
    good: 0.6
```

### 3. 調整檢測靈敏度
```yaml
# 更敏感（快速反應）
auto_scoring:
  detection:
    enter_threshold: 3
    exit_threshold: 5

# 更穩定（減少誤判）
auto_scoring:
  detection:
    enter_threshold: 8
    exit_threshold: 15
```

## 更新記錄

- **2024-07-30**：移除 `auto_scoring.calculation` 中重複的權重參數
- 統一使用 `gait_analysis.scoring` 中的權重設定
- 更新所有相關文件以反映新的配置結構
