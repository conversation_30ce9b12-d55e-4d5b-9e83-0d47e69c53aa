# 自動評分對話框樣式修復

## 問題分析

### 原因
主窗口的全域樣式表影響了對話框的顯示：

```css
QFrame {
    background-color: white;
    border-radius: 8px;
    padding: 10px;
}
QLabel {
    color: #2C3E50;
}
```

這些樣式被繼承到對話框中，導致每個QLabel都有白色背景和圓角邊框。

### 症狀
- `test_dialog.py` 單獨運行正常
- `gui_main.py` 中執行時每個文字元素都被包在白色框框裡
- 文字擠在一起，可讀性差

## 修復方案

### 1. 對話框獨立樣式表
在 `AutoScoringResultDialog` 中設定獨立的樣式表：

```css
QDialog {
    background-color: #2C3E50;
    color: white;
}
QLabel {
    background-color: transparent;
    border: none;
    padding: 0px;
    margin: 0px;
    color: white;
}
QPushButton {
    background-color: #3498DB;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 4px;
    min-height: 30px;
}
```

### 2. 深色主題設計
- 背景色：深藍灰色 (#2C3E50)
- 主要文字：白色/淺灰色
- 按鈕：藍色主題
- 詳細信息：較淺的灰色 (#BDC3C7)

### 3. 文字顏色調整
- 標題：淺色 (#ECF0F1)
- 得分數字：根據等級動態變化（綠色/橙色/紅色）
- 等級標籤：與得分數字相同顏色
- 詳細信息：淺灰色 (#BDC3C7)
- 倒數計時：中等灰色 (#95A5A6)

## 測試方法

### 1. 單獨測試對話框
```bash
python test_dialog.py
```
應該顯示原始的淺色主題。

### 2. 主窗口環境測試
```bash
python test_dialog_with_main.py
```
應該顯示新的深色主題，不受主窗口樣式影響。

### 3. 完整流程測試
```bash
python gui_main.py
```
進行完整的自動評分流程，檢查對話框顯示。

## 預期結果

修復後的對話框應該：
- 有深色背景
- 文字清晰可見
- 沒有白色框框
- 佈局整齊不擠壓
- 按鈕樣式一致

## 技術細節

### 樣式繼承問題
Qt中子窗口會繼承父窗口的樣式表，除非明確覆蓋。

### 解決方法
1. 在對話框中使用 `setStyleSheet()` 覆蓋繼承的樣式
2. 明確設定每個元素的樣式屬性
3. 使用 `transparent` 背景移除不需要的邊框

### 顏色選擇
選擇與主窗口不同的深色主題，確保：
- 高對比度，易於閱讀
- 專業外觀
- 與評分等級顏色協調

## 故障排除

如果對話框仍有問題：
1. 檢查是否有其他全域樣式影響
2. 確認樣式表語法正確
3. 測試不同的顏色組合
4. 檢查字體是否可用
