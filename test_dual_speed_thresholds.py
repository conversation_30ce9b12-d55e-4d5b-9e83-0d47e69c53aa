#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for dual speed threshold system
"""

import sys
import os
import yaml

# Add the code directory to the path
sys.path.append('code')

def test_dual_thresholds():
    """Test the dual speed threshold system"""
    
    print("=== 雙速度閾值系統測試 ===")
    print()
    
    # Test 1: Load settings and check structure
    print("1. 檢查 settings.yaml 結構")
    try:
        with open('settings.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        speed_thresholds = config['gait_analysis']['speed_thresholds']
        
        print("✓ settings.yaml 載入成功")
        print("速度閾值結構：")
        
        if 'meters_per_second' in speed_thresholds:
            mps = speed_thresholds['meters_per_second']
            print(f"  實際速度閾值 (m/s):")
            print(f"    excellent: {mps['excellent']}")
            print(f"    good: {mps['good']}")
        else:
            print("  ❌ 缺少 meters_per_second 閾值")
        
        if 'pixels_per_second' in speed_thresholds:
            pps = speed_thresholds['pixels_per_second']
            print(f"  相對速度閾值 (pixels/s):")
            print(f"    excellent: {pps['excellent']}")
            print(f"    good: {pps['good']}")
        else:
            print("  ❌ 缺少 pixels_per_second 閾值")
            
    except Exception as e:
        print(f"❌ 載入 settings.yaml 失敗: {e}")
        return
    
    print()
    
    # Test 2: Test GaitAnalyzer with different pixel_to_meter settings
    print("2. 測試 GaitAnalyzer 閾值選擇")
    
    try:
        from gait_analyzer import GaitAnalyzer
        
        # Test with pixel_to_meter = None (relative speed)
        print("測試相對速度模式 (pixel_to_meter = null):")
        analyzer_relative = GaitAnalyzer()
        
        if hasattr(analyzer_relative, 'get_speed_unit'):
            unit = analyzer_relative.get_speed_unit()
            thresholds = analyzer_relative.get_speed_thresholds()
            print(f"  速度單位: {unit}")
            print(f"  使用的閾值: {thresholds}")
        else:
            print("  ❌ GaitAnalyzer 缺少 get_speed_unit 方法")
        
        print()
        
        # Test with pixel_to_meter set (actual speed)
        print("測試實際速度模式 (pixel_to_meter = 0.01):")
        analyzer_actual = GaitAnalyzer()
        analyzer_actual.pixel_to_meter = 0.01
        analyzer_actual._update_speed_thresholds()
        
        if hasattr(analyzer_actual, 'get_speed_unit'):
            unit = analyzer_actual.get_speed_unit()
            thresholds = analyzer_actual.get_speed_thresholds()
            print(f"  速度單位: {unit}")
            print(f"  使用的閾值: {thresholds}")
        else:
            print("  ❌ GaitAnalyzer 缺少 get_speed_unit 方法")
            
    except Exception as e:
        print(f"❌ GaitAnalyzer 測試失敗: {e}")
        return
    
    print()
    
    # Test 3: Test speed evaluation with both modes
    print("3. 測試速度評分")
    
    try:
        # Test relative speed evaluation
        print("相對速度評分測試:")
        test_speeds_relative = [10, 20, 30, 50, 80]
        for speed in test_speeds_relative:
            score = analyzer_relative._evaluate_speed(speed)
            print(f"  {speed} pixels/s → {score:.0f} 分")
        
        print()
        
        # Test actual speed evaluation
        print("實際速度評分測試:")
        test_speeds_actual = [0.5, 0.8, 1.0, 1.1, 1.5]
        for speed in test_speeds_actual:
            score = analyzer_actual._evaluate_speed(speed)
            print(f"  {speed} m/s → {score:.0f} 分")
            
    except Exception as e:
        print(f"❌ 速度評分測試失敗: {e}")
        return
    
    print()
    
    # Test 4: Validate threshold logic
    print("4. 驗證閾值邏輯")
    
    try:
        # Check relative speed thresholds
        rel_thresholds = analyzer_relative.get_speed_thresholds()
        if rel_thresholds['excellent'] > rel_thresholds['good']:
            print("✓ 相對速度閾值順序正確")
        else:
            print("❌ 相對速度閾值順序錯誤")
        
        # Check actual speed thresholds
        act_thresholds = analyzer_actual.get_speed_thresholds()
        if act_thresholds['excellent'] > act_thresholds['good']:
            print("✓ 實際速度閾值順序正確")
        else:
            print("❌ 實際速度閾值順序錯誤")
            
    except Exception as e:
        print(f"❌ 閾值邏輯驗證失敗: {e}")
        return
    
    print()
    print("=== 測試完成 ===")
    print()
    print("使用說明：")
    print("- 當 pixel_to_meter 為 null 時，系統使用相對速度閾值 (pixels/s)")
    print("- 當 pixel_to_meter 有數值時，系統使用實際速度閾值 (m/s)")
    print("- 可以在 settings.yaml 中分別調整兩組閾值")
    print("- GUI 會自動顯示對應的速度單位")

if __name__ == "__main__":
    test_dual_thresholds()
