import numpy as np
from collections import deque

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("Warning: PyYAML not installed, using default configuration")

class GaitAnalyzer:
    def __init__(self, config_path='../settings.yaml'):
        # 載入配置檔案
        self.config = self._load_config(config_path)

        # 從配置中獲取參數
        gait_config = self.config['gait_analysis']
        self.angle_thresholds = gait_config['angle_thresholds']
        self.speed_thresholds_config = gait_config['speed_thresholds']
        self.scoring = gait_config['scoring']

        # 平滑處理參數
        smoothing = gait_config['smoothing']
        self.speed_window = smoothing['speed_window']
        self.score_window = smoothing['score_window']

        # 系統參數
        self.pixel_to_meter = self.config['system']['pixel_to_meter']

        # 根據 pixel_to_meter 設定選擇適當的速度閾值
        self._update_speed_thresholds()

        # 初始化數據緩存
        self.prev_positions = deque(maxlen=self.speed_window)
        self.score_history = deque(maxlen=self.score_window)

    def _update_speed_thresholds(self):
        """根據 pixel_to_meter 設定選擇適當的速度閾值"""
        if self.pixel_to_meter is not None:
            # 使用實際速度閾值（公尺/秒）
            self.speed_thresholds = self.speed_thresholds_config['meters_per_second']
            self.speed_unit = "m/s"
            print(f"使用實際速度閾值: excellent={self.speed_thresholds['excellent']} m/s, good={self.speed_thresholds['good']} m/s")
        else:
            # 使用相對速度閾值（像素/秒）
            self.speed_thresholds = self.speed_thresholds_config['pixels_per_second']
            self.speed_unit = "pixels/s"
            print(f"使用相對速度閾值: excellent={self.speed_thresholds['excellent']} pixels/s, good={self.speed_thresholds['good']} pixels/s")

    def get_speed_unit(self):
        """獲取當前使用的速度單位"""
        return self.speed_unit

    def get_speed_thresholds(self):
        """獲取當前使用的速度閾值"""
        return self.speed_thresholds

    def _load_config(self, config_path):
        """Load YAML configuration file"""
        if not YAML_AVAILABLE:
            print("PyYAML not installed, using default configuration")
            return self._get_default_config()

        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            print(f"Configuration file {config_path} not found, using default values")
            return self._get_default_config()
        except Exception as e:
            print(f"Configuration file parsing error: {e}, using default values")
            return self._get_default_config()

    def _get_default_config(self):
        """Return default configuration"""
        return {
            'gait_analysis': {
                'angle_thresholds': {
                    'excellent_ranges': [[60, 100], [160, 180]],
                    'good_ranges': [[100, 130], [130, 160]],
                    'poor_range': [130, 160]
                },
                'speed_thresholds': {
                    'meters_per_second': {
                        'excellent': 1.1,
                        'good': 0.8
                    },
                    'pixels_per_second': {
                        'excellent': 50,
                        'good': 20
                    }
                },
                'scoring': {
                    'angle_weight': 0.6,
                    'speed_weight': 0.4
                },
                'smoothing': {
                    'speed_window': 5,
                    'score_window': 3
                }
            },
            'system': {
                'pixel_to_meter': None
            }
        }

    def evaluate_gait(self, joint_angles, speed):
        """
        Evaluate gait quality, returns three levels: excellent, good, poor
        Returns: (level, score, main_reason, angle_score, speed_score)
        """
        # 計算角度評分
        angle_score = self._evaluate_angles(joint_angles)

        # 計算速度評分
        speed_score = self._evaluate_speed(speed)

        # 綜合評分
        total_score = (angle_score * self.scoring['angle_weight'] +
                      speed_score * self.scoring['speed_weight'])

        # 平滑處理評分
        self.score_history.append(total_score)
        smoothed_score = np.mean(list(self.score_history))

        # 判斷等級
        if smoothed_score >= 80:
            level = 'excellent'
            reason = ''
        elif smoothed_score >= 50:
            level = 'good'
            reason = self._get_main_issue(angle_score, speed_score)
        else:
            level = 'poor'
            reason = self._get_main_issue(angle_score, speed_score)

        return level, smoothed_score, reason, angle_score, speed_score

    def _evaluate_angles(self, joint_angles):
        """Evaluate joint angles, returns 0-100 score"""
        left_knee = joint_angles.get('left_knee', None)
        right_knee = joint_angles.get('right_knee', None)

        # 如果沒有檢測到關鍵點，返回0分
        if left_knee is None and right_knee is None:
            return 0
        if left_knee == 0 and right_knee == 0:  # MediaPipe未檢測到時通常返回0
            return 0

        # 如果只檢測到一條腿，使用該腿的數據
        if left_knee is None:
            left_knee = right_knee
        if right_knee is None:
            right_knee = left_knee

        # 線性化評分系統
        excellent_ranges = self.angle_thresholds['excellent_ranges']
        good_ranges = self.angle_thresholds['good_ranges']
        poor_range = self.angle_thresholds['poor_range']

        def evaluate_single_knee(angle):
            # 檢查是否在優良範圍
            for r in excellent_ranges:
                if r[0] <= angle <= r[1]:
                    # 在優良範圍內，線性映射到80-100分
                    range_size = r[1] - r[0]
                    position = (angle - r[0]) / range_size
                    return 80 + position * 20

            # 檢查是否在良好範圍
            for r in good_ranges:
                if r[0] <= angle <= r[1]:
                    # 在良好範圍內，線性映射到50-80分
                    range_size = r[1] - r[0]
                    position = (angle - r[0]) / range_size
                    return 50 + position * 30

            # 檢查是否在不佳範圍
            if poor_range[0] <= angle <= poor_range[1]:
                # 在不佳範圍內，線性映射到0-30分
                range_size = poor_range[1] - poor_range[0]
                position = (angle - poor_range[0]) / range_size
                return position * 30

            # 超出所有範圍，根據距離最近範圍的遠近給分
            return 20  # 基礎分數

        left_score = evaluate_single_knee(left_knee)
        right_score = evaluate_single_knee(right_knee)

        # 取兩腿的平均分
        return int((left_score + right_score) / 2)

    def _evaluate_speed(self, speed):
        """Evaluate speed, returns 0-100 score"""
        # 如果速度為0或接近0，返回0分
        if speed <= 0.01:  # 考慮浮點數精度
            return 0

        excellent_threshold = self.speed_thresholds['excellent']
        good_threshold = self.speed_thresholds['good']

        if speed >= excellent_threshold:
            # 優良範圍：線性映射到80-100分
            # 超過優良閾值的部分也給予獎勵，但有上限
            max_speed = excellent_threshold * 1.5  # 設定最大期望速度
            if speed >= max_speed:
                return 100
            else:
                ratio = (speed - excellent_threshold) / (max_speed - excellent_threshold)
                return 80 + ratio * 20
        elif speed >= good_threshold:
            # 良好範圍：線性映射到50-80分
            ratio = (speed - good_threshold) / (excellent_threshold - good_threshold)
            return 50 + ratio * 30
        else:
            # 低速範圍：線性映射到0-50分
            # 假設最低有意義的速度是good_threshold的1/3
            min_speed = good_threshold / 3
            if speed <= min_speed:
                return 10  # 給予最低分數而不是0，因為至少有移動
            else:
                ratio = (speed - min_speed) / (good_threshold - min_speed)
                return 10 + ratio * 40

    def _get_main_issue(self, angle_score, speed_score):
        """Determine main issue source"""
        if angle_score < speed_score:
            return 'Angle'
        elif speed_score < angle_score:
            return 'Speed'
        else:
            return 'Both'

    def calculate_speed(self, pose_landmarks, dt):
        """Calculate walking speed"""
        # Use left hip as example
        LEFT_HIP = 23
        if pose_landmarks is None:
            return 0.0

        lm = pose_landmarks.landmark
        x = lm[LEFT_HIP].x
        y = lm[LEFT_HIP].y
        self.prev_positions.append((x, y))

        if len(self.prev_positions) < 2:
            return 0.0

        # 計算移動距離
        dx = self.prev_positions[-1][0] - self.prev_positions[0][0]
        dy = self.prev_positions[-1][1] - self.prev_positions[0][1]
        dist = np.sqrt(dx**2 + dy**2)

        # 轉換為實際距離（如果有標定）
        if self.pixel_to_meter:
            dist = dist * self.pixel_to_meter

        # 計算速度
        time_span = dt * (len(self.prev_positions) - 1)
        speed = dist / time_span if time_span > 0 else 0.0

        return speed

    # Backward compatibility method
    def is_normal_gait(self, joint_angles, speed):
        """Backward compatibility method, converts new three-level evaluation to binary result"""
        level, _, reason, _, _ = self.evaluate_gait(joint_angles, speed)
        is_normal = level in ['excellent', 'good']
        return is_normal, reason