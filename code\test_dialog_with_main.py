#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for auto scoring result dialog with main window styles
"""

import sys
from PySide6.QtWidgets import QApplication
from gui_main import MainWindow

def test_dialog_with_main():
    """Test the auto scoring result dialog with main window styles"""
    app = QApplication(sys.argv)
    
    # Create main window (this applies the global styles)
    main_window = MainWindow()
    main_window.show()
    
    # Create test result data
    test_result = {
        'final_score': 68.8,
        'avg_angle_score': 98.2,
        'avg_speed_score': 24.7,
        'level': 'good',
        'session_duration': 7.0,
        'sample_count': 210
    }
    
    print("Creating AutoScoringResultDialog with main window as parent...")
    print(f"Test result: {test_result}")
    
    # Show the dialog using the main window's method
    main_window.show_auto_scoring_result(test_result)
    
    print("Dialog should now display with correct styling")
    
    return app.exec()

if __name__ == "__main__":
    test_dialog_with_main()
