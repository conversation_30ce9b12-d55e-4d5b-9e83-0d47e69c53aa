import cv2
from pose_detection import Po<PERSON>Detector
from angle_calculator import AngleCalculator
from gait_analyzer import GaitAnalyzer
import visualizer as vis
import videocapture as vc
import time

WINDOW_NAME = 'Gait Detection'

if __name__ == '__main__':
    print("Starting gait detection. Press 'q' to quit.")
    cap = vc.open_camera()
    pose_detector = PoseDetector()
    angle_calculator = AngleCalculator()
    gait_analyzer = GaitAnalyzer()
    prev_time = time.time()

    # Create resizable window
    cv2.namedWindow(WINDOW_NAME, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(WINDOW_NAME, 960, 720)

    while True:
        frame = vc.get_camera_frame(cap)
        if frame is None:
            break
        # Mirror the frame horizontally
        frame = cv2.flip(frame, 1)
        pose_landmarks = pose_detector.detect(frame)
        if pose_landmarks is None:
            out = frame.copy()
            out = vis.draw_no_person(out)
        else:
            angles = angle_calculator.get_joint_angles(pose_landmarks)
            curr_time = time.time()
            dt = curr_time - prev_time
            prev_time = curr_time
            speed = gait_analyzer.calculate_speed(pose_landmarks, dt)

            # 使用新的三級評估系統
            level, score, reason, angle_score, speed_score = gait_analyzer.evaluate_gait(angles, speed)

            out = frame.copy()
            out = vis.draw_landmarks(out, pose_landmarks)
            out = vis.draw_angles(out, angles)

            # 繪製進度條
            out = vis.draw_progress_bar(out, score, level)

            # 繪製增強版步態結果
            out = vis.draw_gait_result_enhanced(out, level, score, speed, reason)

        # Get current window size
        try:
            _, _, win_w, win_h = cv2.getWindowImageRect(WINDOW_NAME)
            if win_w > 0 and win_h > 0:
                out = cv2.resize(out, (win_w, win_h), interpolation=cv2.INTER_LINEAR)
        except:
            pass

        cv2.imshow(WINDOW_NAME, out)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    cap.release()
    cv2.destroyAllWindows()
