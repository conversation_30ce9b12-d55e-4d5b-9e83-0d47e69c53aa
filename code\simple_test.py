#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("Starting simple test...")

try:
    import yaml
    print("OK PyYAML imported successfully")
except ImportError as e:
    print(f"WARNING PyYAML import failed: {e}")
    print("Continuing with default config...")

try:
    from gait_analyzer import GaitAnalyzer
    print("OK GaitAnalyzer imported successfully")

    analyzer = GaitAnalyzer()
    print("OK GaitAnalyzer initialized successfully")

    # Test evaluation
    test_angles = {'left_knee': 90, 'right_knee': 85}
    test_speed = 0.2

    level, score, reason, angle_score, speed_score = analyzer.evaluate_gait(test_angles, test_speed)
    print("OK Evaluation test:")
    print(f"  Level: {level}")
    print(f"  Score: {score:.1f}")
    print(f"  Reason: {reason}")

except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()

print("Test completed.")
