[{"classes": [{"className": "QAbstractAnimation", "enums": [{"isClass": false, "isFlag": false, "name": "AnimationType", "values": ["KeyframeAnimation", "MorphingAnimation", "VertexBlendAnimation"]}], "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "animationName", "notify": "animationNameChanged", "read": "animationName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setAnimationName"}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "animationType", "read": "animationType", "required": false, "scriptable": true, "stored": true, "type": "AnimationType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}], "qualifiedClassName": "Qt3DAnimation::QAbstractAnimation", "signals": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 0, "name": "animationNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "float"}], "index": 1, "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "float"}], "index": 2, "name": "durationChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 3, "name": "setAnimationName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "float"}], "index": 4, "name": "setPosition", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractanimation.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractAnimationClip", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}], "qualifiedClassName": "Qt3DAnimation::QAbstractAnimationClip", "signals": [{"access": "public", "arguments": [{"name": "duration", "type": "float"}], "index": 0, "name": "durationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qabstractanimationclip.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractChannelMapping", "lineNumber": 16, "object": true, "qualifiedClassName": "Qt3DAnimation::QAbstractChannelMapping", "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qabstractchannelmapping.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractClipAnimator", "enums": [{"isClass": false, "isFlag": false, "name": "Loops", "values": ["Infinite"]}], "lineNumber": 19, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "running", "notify": "running<PERSON><PERSON>ed", "read": "isRunning", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRunning"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "loops", "notify": "loopCountChanged", "read": "loopCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoopCount"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "channelMapper", "notify": "channelMapperChanged", "read": "channelMapper", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QChannelMapper*", "user": false, "write": "setChannelMapper"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "clock", "notify": "clockChanged", "read": "clock", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QClock*", "user": false, "write": "setClock"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "normalizedTime", "notify": "normalizedTimeChanged", "read": "normalizedTime", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setNormalizedTime"}], "qualifiedClassName": "Qt3DAnimation::QAbstractClipAnimator", "signals": [{"access": "public", "arguments": [{"name": "running", "type": "bool"}], "index": 0, "name": "running<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "channelMapper", "type": "Qt3DAnimation::QChannelMapper*"}], "index": 1, "name": "channelMapperChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "loops", "type": "int"}], "index": 2, "name": "loopCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clock", "type": "Qt3DAnimation::QClock*"}], "index": 3, "name": "clockChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "float"}], "index": 4, "name": "normalizedTimeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "running", "type": "bool"}], "index": 5, "name": "setRunning", "returnType": "void"}, {"access": "public", "arguments": [{"name": "channelMapper", "type": "Qt3DAnimation::QChannelMapper*"}], "index": 6, "name": "setChannelMapper", "returnType": "void"}, {"access": "public", "arguments": [{"name": "loops", "type": "int"}], "index": 7, "name": "setLoopCount", "returnType": "void"}, {"access": "public", "arguments": [{"name": "clock", "type": "Qt3DAnimation::QClock*"}], "index": 8, "name": "setClock", "returnType": "void"}, {"access": "public", "arguments": [{"name": "timeFraction", "type": "float"}], "index": 9, "name": "setNormalizedTime", "returnType": "void"}, {"access": "public", "index": 10, "name": "start", "returnType": "void"}, {"access": "public", "index": 11, "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QComponent"}]}], "inputFile": "qabstractclipanimator.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractClipBlendNode", "lineNumber": 17, "object": true, "qualifiedClassName": "Qt3DAnimation::QAbstractClipBlendNode", "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qabstractclipblendnode.h", "outputRevision": 69}, {"classes": [{"className": "QAdditiveClipBlend", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "baseClip", "notify": "baseClipChanged", "read": "baseClip", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractClipBlendNode*", "user": false, "write": "setBaseClip"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "additiveClip", "notify": "additiveClipChanged", "read": "additiveClip", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractClipBlendNode*", "user": false, "write": "setAdditiveClip"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "additiveFactor", "notify": "additiveFactorChanged", "read": "additiveFactor", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setAdditiveFactor"}], "qualifiedClassName": "Qt3DAnimation::QAdditiveClipBlend", "signals": [{"access": "public", "arguments": [{"name": "additiveFactor", "type": "float"}], "index": 0, "name": "additiveFactorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "baseClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "index": 1, "name": "baseClipChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "additiveClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "index": 2, "name": "additiveClipChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "additiveFactor", "type": "float"}], "index": 3, "name": "setAdditiveFactor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "baseClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "index": 4, "name": "setBaseClip", "returnType": "void"}, {"access": "public", "arguments": [{"name": "additiveClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "index": 5, "name": "setAdditiveClip", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DAnimation::QAbstractClipBlendNode", "name": "QAbstractClipBlendNode"}]}], "inputFile": "qadditiveclipblend.h", "outputRevision": 69}, {"classes": [{"className": "QAnimationAspect", "lineNumber": 16, "object": true, "qualifiedClassName": "Qt3DAnimation::QAnimationAspect", "superClasses": [{"access": "public", "name": "Qt3DCore::QAbstractAspect"}]}], "inputFile": "qanimationaspect.h", "outputRevision": 69}, {"classes": [{"className": "QAnimationClip", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "clipData", "notify": "clipDataChanged", "read": "clipData", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAnimationClipData", "user": false, "write": "setClipData"}], "qualifiedClassName": "Qt3DAnimation::QAnimationClip", "signals": [{"access": "public", "arguments": [{"name": "clipData", "type": "Qt3DAnimation::QAnimationClipData"}], "index": 0, "name": "clipDataChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "clipData", "type": "Qt3DAnimation::QAnimationClipData"}], "index": 1, "name": "setClipData", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DAnimation::QAbstractAnimationClip", "name": "QAbstractAnimationClip"}]}], "inputFile": "qanimationclip.h", "outputRevision": 69}, {"classes": [{"className": "QAnimationClipLoader", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["NotReady", "Ready", "Error"]}], "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "Qt3DAnimation::QAnimationClipLoader", "signals": [{"access": "public", "arguments": [{"name": "source", "type": "QUrl"}], "index": 0, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "Status"}], "index": 1, "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "source", "type": "QUrl"}], "index": 2, "name": "setSource", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DAnimation::QAbstractAnimationClip", "name": "QAbstractAnimationClip"}]}], "inputFile": "qanimationcliploader.h", "outputRevision": 69}, {"classes": [{"className": "QAnimationController", "lineNumber": 19, "methods": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 12, "isConst": true, "name": "getAnimationIndex", "returnType": "int"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 13, "isConst": true, "name": "getGroup", "returnType": "Qt3DAnimation::QAnimationGroup*"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "activeAnimationGroup", "notify": "activeAnimationGroupChanged", "read": "activeAnimationGroup", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setActiveAnimationGroup"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "positionScale", "notify": "positionScaleChanged", "read": "positionScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setPositionScale"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "positionOffset", "notify": "positionOffsetChanged", "read": "positionOffset", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setPositionOffset"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "entity", "notify": "entityChanged", "read": "entity", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QEntity*", "user": false, "write": "setEntity"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "recursive", "notify": "recursiveChanged", "read": "recursive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRecursive"}], "qualifiedClassName": "Qt3DAnimation::QAnimationController", "signals": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 0, "name": "activeAnimationGroupChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "float"}], "index": 1, "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scale", "type": "float"}], "index": 2, "name": "positionScaleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "offset", "type": "float"}], "index": 3, "name": "positionOffsetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "entity", "type": "Qt3DCore::QEntity*"}], "index": 4, "name": "entityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "recursive", "type": "bool"}], "index": 5, "name": "recursiveChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 6, "name": "setActiveAnimationGroup", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "float"}], "index": 7, "name": "setPosition", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scale", "type": "float"}], "index": 8, "name": "setPositionScale", "returnType": "void"}, {"access": "public", "arguments": [{"name": "offset", "type": "float"}], "index": 9, "name": "setPositionOffset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "entity", "type": "Qt3DCore::QEntity*"}], "index": 10, "name": "setEntity", "returnType": "void"}, {"access": "public", "arguments": [{"name": "recursive", "type": "bool"}], "index": 11, "name": "setRecursive", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qanimationcontroller.h", "outputRevision": 69}, {"classes": [{"className": "QAnimationGroup", "lineNumber": 19, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}], "qualifiedClassName": "Qt3DAnimation::QAnimationGroup", "signals": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 0, "name": "nameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "float"}], "index": 1, "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "float"}], "index": 2, "name": "durationChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 3, "name": "setName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "float"}], "index": 4, "name": "setPosition", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qanimationgroup.h", "outputRevision": 69}, {"classes": [{"className": "QBlendedClipAnimator", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "blendTree", "notify": "blendTreeChanged", "read": "blendTree", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractClipBlendNode*", "user": false, "write": "setBlendTree"}], "qualifiedClassName": "Qt3DAnimation::QBlendedClipAnimator", "signals": [{"access": "public", "arguments": [{"name": "blendTree", "type": "QAbstractClipBlendNode*"}], "index": 0, "name": "blendTreeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "blendTree", "type": "QAbstractClipBlendNode*"}], "index": 1, "name": "setBlendTree", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DAnimation::QAbstractClipAnimator"}]}], "inputFile": "qblendedclipanimator.h", "outputRevision": 69}, {"classes": [{"className": "QCallbackMapping", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "channelName", "notify": "channelNameChanged", "read": "channelName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setChannelName"}], "qualifiedClassName": "Qt3DAnimation::QCallbackMapping", "signals": [{"access": "public", "arguments": [{"name": "channelName", "type": "QString"}], "index": 0, "name": "channelNameChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "channelName", "type": "QString"}], "index": 1, "name": "setChannelName", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DAnimation::QAbstractChannelMapping", "name": "QAbstractChannelMapping"}]}], "inputFile": "qcallbackmapping.h", "outputRevision": 69}, {"classes": [{"className": "QChannelMapper", "lineNumber": 17, "object": true, "qualifiedClassName": "Qt3DAnimation::QChannelMapper", "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qchannelmapper.h", "outputRevision": 69}, {"classes": [{"className": "QChannelMapping", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "channelName", "notify": "channelNameChanged", "read": "channelName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setChannelName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QNode*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "property", "notify": "propertyChanged", "read": "property", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setProperty"}], "qualifiedClassName": "Qt3DAnimation::QChannelMapping", "signals": [{"access": "public", "arguments": [{"name": "channelName", "type": "QString"}], "index": 0, "name": "channelNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DCore::QNode*"}], "index": 1, "name": "targetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QString"}], "index": 2, "name": "propertyChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "channelName", "type": "QString"}], "index": 3, "name": "setChannelName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DCore::QNode*"}], "index": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "property", "type": "QString"}], "index": 5, "name": "setProperty", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DAnimation::QAbstractChannelMapping", "name": "QAbstractChannelMapping"}]}], "inputFile": "qchannelmapping.h", "outputRevision": 69}, {"classes": [{"className": "QClipAnimator", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "clip", "notify": "clipChanged", "read": "clip", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractAnimationClip*", "user": false, "write": "setClip"}], "qualifiedClassName": "Qt3DAnimation::QClipAnimator", "signals": [{"access": "public", "arguments": [{"name": "clip", "type": "Qt3DAnimation::QAbstractAnimationClip*"}], "index": 0, "name": "clipChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "clip", "type": "Qt3DAnimation::QAbstractAnimationClip*"}], "index": 1, "name": "setClip", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DAnimation::QAbstractClipAnimator"}]}], "inputFile": "qclipanimator.h", "outputRevision": 69}, {"classes": [{"className": "QClipBlendValue", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "clip", "notify": "clipChanged", "read": "clip", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractAnimationClip*", "user": false, "write": "setClip"}], "qualifiedClassName": "Qt3DAnimation::QClipBlendValue", "signals": [{"access": "public", "arguments": [{"name": "clip", "type": "Qt3DAnimation::QAbstractAnimationClip*"}], "index": 0, "name": "clipChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "clip", "type": "Qt3DAnimation::QAbstractAnimationClip*"}], "index": 1, "name": "setClip", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DAnimation::QAbstractClipBlendNode"}]}], "inputFile": "qclipblendvalue.h", "outputRevision": 69}, {"classes": [{"className": "QClock", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "playbackRate", "notify": "playbackRateChanged", "read": "playbackRate", "required": false, "scriptable": true, "stored": true, "type": "double", "user": false, "write": "setPlaybackRate"}], "qualifiedClassName": "Qt3DAnimation::QClock", "signals": [{"access": "public", "arguments": [{"name": "playbackRate", "type": "double"}], "index": 0, "name": "playbackRateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qclock.h", "outputRevision": 69}, {"classes": [{"className": "QKeyframeAnimation", "enums": [{"isClass": false, "isFlag": false, "name": "RepeatMode", "values": ["None", "Constant", "Repeat"]}], "lineNumber": 20, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "framePositions", "notify": "framePositionsChanged", "read": "framePositions", "required": false, "scriptable": true, "stored": true, "type": "QList<float>", "user": false, "write": "setFramePositions"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QTransform*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "easing", "notify": "easingChanged", "read": "easing", "required": false, "scriptable": true, "stored": true, "type": "QEasingCurve", "user": false, "write": "setEasing"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "targetName", "notify": "targetNameChanged", "read": "targetName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTargetName"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "startMode", "notify": "startModeChanged", "read": "startMode", "required": false, "scriptable": true, "stored": true, "type": "RepeatMode", "user": false, "write": "setStartMode"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "endMode", "notify": "endModeChanged", "read": "endMode", "required": false, "scriptable": true, "stored": true, "type": "RepeatMode", "user": false, "write": "setEndMode"}], "qualifiedClassName": "Qt3DAnimation::QKeyframeAnimation", "signals": [{"access": "public", "arguments": [{"name": "positions", "type": "QList<float>"}], "index": 0, "name": "framePositionsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DCore::QTransform*"}], "index": 1, "name": "targetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "easing", "type": "QEasingCurve"}], "index": 2, "name": "easingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 3, "name": "targetNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startMode", "type": "QKeyframeAnimation::RepeatMode"}], "index": 4, "name": "startModeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "endMode", "type": "QKeyframeAnimation::RepeatMode"}], "index": 5, "name": "endModeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "positions", "type": "QList<float>"}], "index": 6, "name": "setFramePositions", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DCore::QTransform*"}], "index": 7, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "easing", "type": "QEasingCurve"}], "index": 8, "name": "setEasing", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 9, "name": "setTargetName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "RepeatMode"}], "index": 10, "name": "setStartMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "RepeatMode"}], "index": 11, "name": "setEndMode", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DAnimation::QAbstractAnimation", "name": "QAbstractAnimation"}]}], "inputFile": "qkeyframeanimation.h", "outputRevision": 69}, {"classes": [{"className": "QLerpClipBlend", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "startClip", "notify": "startClipChanged", "read": "startClip", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractClipBlendNode*", "user": false, "write": "setStartClip"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "endClip", "notify": "endClipChanged", "read": "endClip", "required": false, "scriptable": true, "stored": true, "type": "Qt3DAnimation::QAbstractClipBlendNode*", "user": false, "write": "setEndClip"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "blendFactor", "notify": "blendFactorChanged", "read": "blendFactor", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBlendFactor"}], "qualifiedClassName": "Qt3DAnimation::QLerpClipBlend", "signals": [{"access": "public", "arguments": [{"name": "blendFactor", "type": "float"}], "index": 0, "name": "blendFactorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "index": 1, "name": "startClipChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "endClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "index": 2, "name": "endClipChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "blendFactor", "type": "float"}], "index": 3, "name": "setBlendFactor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "index": 4, "name": "setStartClip", "returnType": "void"}, {"access": "public", "arguments": [{"name": "endClip", "type": "Qt3DAnimation::QAbstractClipBlendNode*"}], "index": 5, "name": "setEndClip", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DAnimation::QAbstractClipBlendNode", "name": "QAbstractClipBlendNode"}]}], "inputFile": "qlerpclipblend.h", "outputRevision": 69}, {"classes": [{"className": "QMorphingAnimation", "enums": [{"isClass": false, "isFlag": false, "name": "Method", "values": ["Normalized", "Relative"]}], "lineNumber": 21, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "targetPositions", "notify": "targetPositionsChanged", "read": "targetPositions", "required": false, "scriptable": true, "stored": true, "type": "QList<float>", "user": false, "write": "setTargetPositions"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "interpolator", "notify": "interpolatorChanged", "read": "interpolator", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "targetName", "notify": "targetNameChanged", "read": "targetName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTargetName"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "method", "notify": "methodChanged", "read": "method", "required": false, "scriptable": true, "stored": true, "type": "Method", "user": false, "write": "set<PERSON>ethod"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "easing", "notify": "easingChanged", "read": "easing", "required": false, "scriptable": true, "stored": true, "type": "QEasingCurve", "user": false, "write": "setEasing"}], "qualifiedClassName": "Qt3DAnimation::QMorphingAnimation", "signals": [{"access": "public", "arguments": [{"name": "targetPositions", "type": "QList<float>"}], "index": 0, "name": "targetPositionsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "interpolator", "type": "float"}], "index": 1, "name": "interpolatorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>*"}], "index": 2, "name": "targetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 3, "name": "targetNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "method", "type": "QMorphingAnimation::Method"}], "index": 4, "name": "methodChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "easing", "type": "QEasingCurve"}], "index": 5, "name": "easingChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "targetPositions", "type": "QList<float>"}], "index": 6, "name": "setTargetPositions", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>*"}], "index": 7, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 8, "name": "setTargetName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "method", "type": "QMorphingAnimation::Method"}], "index": 9, "name": "set<PERSON>ethod", "returnType": "void"}, {"access": "public", "arguments": [{"name": "easing", "type": "QEasingCurve"}], "index": 10, "name": "setEasing", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DAnimation::QAbstractAnimation", "name": "QAbstractAnimation"}]}], "inputFile": "qmorphinganimation.h", "outputRevision": 69}, {"classes": [{"className": "QMorph<PERSON>arget", "lineNumber": 20, "methods": [{"access": "public", "arguments": [{"name": "geometry", "type": "Qt3DCore::QGeometry*"}, {"name": "attributes", "type": "QStringList"}], "index": 1, "name": "fromGeometry", "returnType": "QMorphTarget*"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "attributeNames", "notify": "attributeNamesChanged", "read": "attributeNames", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}], "qualifiedClassName": "Qt3DAnimation::QMorphTarget", "signals": [{"access": "public", "arguments": [{"name": "attributeNames", "type": "QStringList"}], "index": 0, "name": "attributeNamesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmorphtarget.h", "outputRevision": 69}, {"classes": [{"className": "QSkeletonMapping", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "skeleton", "notify": "skeletonChanged", "read": "skeleton", "required": false, "scriptable": true, "stored": true, "type": "Qt3DCore::QAbstractSkeleton*", "user": false, "write": "setSkeleton"}], "qualifiedClassName": "Qt3DAnimation::QSkeletonMapping", "signals": [{"access": "public", "arguments": [{"name": "skeleton", "type": "Qt3DCore::QAbstractSkeleton*"}], "index": 0, "name": "skeletonChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "skeleton", "type": "Qt3DCore::QAbstractSkeleton*"}], "index": 1, "name": "setSkeleton", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DAnimation::QAbstractChannelMapping", "name": "QAbstractChannelMapping"}]}], "inputFile": "qskeletonmapping.h", "outputRevision": 69}, {"classes": [{"className": "QVertexBlendAnimation", "lineNumber": 19, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "targetPositions", "notify": "targetPositionsChanged", "read": "targetPositions", "required": false, "scriptable": true, "stored": true, "type": "QList<float>", "user": false, "write": "setTargetPositions"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "interpolator", "notify": "interpolatorChanged", "read": "interpolator", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "scriptable": true, "stored": true, "type": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "targetName", "notify": "targetNameChanged", "read": "targetName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTargetName"}], "qualifiedClassName": "Qt3DAnimation::QVertexBlendAnimation", "signals": [{"access": "public", "arguments": [{"name": "targetPositions", "type": "QList<float>"}], "index": 0, "name": "targetPositionsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "interpolator", "type": "float"}], "index": 1, "name": "interpolatorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>*"}], "index": 2, "name": "targetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 3, "name": "targetNameChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "targetPositions", "type": "QList<float>"}], "index": 4, "name": "setTargetPositions", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "Qt3DRender::QGeo<PERSON><PERSON><PERSON><PERSON>*"}], "index": 5, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 6, "name": "setTargetName", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DAnimation::QAbstractAnimation", "name": "QAbstractAnimation"}]}], "inputFile": "qvertexblendanimation.h", "outputRevision": 69}]