#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Complete flow test for auto scoring functionality
"""

import sys
import time
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QTimer
from gui_main import MainWindow

class AutoScoringTester:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.window = MainWindow()
        self.window.show()
        
        # Setup timer for automated testing
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_test_sequence)
        self.test_step = 0
        
    def run_test_sequence(self):
        """Run automated test sequence"""
        if self.test_step == 0:
            print("Step 1: Testing auto scoring toggle...")
            self.test_auto_scoring_toggle()
            self.test_step += 1
            
        elif self.test_step == 1:
            print("Step 2: Starting camera...")
            self.start_camera()
            self.test_step += 1
            
        elif self.test_step == 2:
            print("Step 3: Enabling auto scoring...")
            self.enable_auto_scoring()
            self.test_step += 1
            
        elif self.test_step == 3:
            print("Step 4: Test complete. Manual testing can begin.")
            print("Please walk into and out of the camera view to test auto scoring.")
            self.timer.stop()
            
    def test_auto_scoring_toggle(self):
        """Test auto scoring toggle functionality"""
        checkbox = self.window.auto_scoring_checkbox
        status_label = self.window.auto_scoring_status
        video_thread = self.window.video_thread
        
        print(f"Initial state:")
        print(f"  Checkbox: {checkbox.isChecked()}")
        print(f"  Status: '{status_label.text()}'")
        print(f"  VideoThread enabled: {video_thread.auto_scoring_enabled}")
        
        # Test enabling
        print("Testing enable...")
        self.window.on_auto_scoring_toggled(Qt.Checked)
        print(f"  Status after enable: '{status_label.text()}'")
        print(f"  VideoThread enabled: {video_thread.auto_scoring_enabled}")
        
        # Test disabling
        print("Testing disable...")
        self.window.on_auto_scoring_toggled(Qt.Unchecked)
        print(f"  Status after disable: '{status_label.text()}'")
        print(f"  VideoThread enabled: {video_thread.auto_scoring_enabled}")
        
    def start_camera(self):
        """Start the camera"""
        if not self.window.video_thread.running:
            self.window.toggle_camera()
            print("Camera started")
        else:
            print("Camera already running")
            
    def enable_auto_scoring(self):
        """Enable auto scoring"""
        checkbox = self.window.auto_scoring_checkbox
        checkbox.setChecked(True)
        self.app.processEvents()
        print(f"Auto scoring enabled: {checkbox.isChecked()}")
        print(f"Status: '{self.window.auto_scoring_status.text()}'")
        
    def run(self):
        """Run the test"""
        print("=== Auto Scoring Complete Flow Test ===")
        print("Starting automated test sequence in 2 seconds...")
        
        # Start the test sequence after a short delay
        QTimer.singleShot(2000, lambda: self.timer.start(3000))  # 3 second intervals
        
        return self.app.exec()

if __name__ == "__main__":
    tester = AutoScoringTester()
    sys.exit(tester.run())
