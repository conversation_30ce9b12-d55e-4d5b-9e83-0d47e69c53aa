# 速度判斷標準設定指南

## 雙速度閾值系統

系統現在支援兩種不同的速度測量模式，每種模式都有對應的閾值設定：

### 1. 實際速度模式 (m/s)
當 `pixel_to_meter` 有設定值時使用：

| 等級 | 速度範圍 | 描述 |
|------|----------|------|
| **優良** | ≥ 1.1 m/s | 快速正常行走，表示良好的步態能力 |
| **一般** | 0.8 - 1.1 m/s | 中等速度行走，基本正常但有改善空間 |
| **不佳** | < 0.8 m/s | 緩慢行走，可能存在步態問題 |

### 2. 相對速度模式 (pixels/s)
當 `pixel_to_meter` 為 null 時使用：

| 等級 | 速度範圍 | 描述 |
|------|----------|------|
| **優良** | ≥ 50 pixels/s | 快速移動，表示良好的活動能力 |
| **一般** | 20 - 50 pixels/s | 中等移動速度，基本正常 |
| **不佳** | < 20 pixels/s | 緩慢移動，可能存在問題 |

### 評分機制

速度評分採用線性映射系統（0-100分）：

- **優良範圍 (≥1.1 m/s)**：映射到 80-100 分
  - 1.1 m/s = 80分
  - 1.65 m/s（1.1 × 1.5）= 100分
  - 超過1.65 m/s 仍為100分

- **一般範圍 (0.8-1.1 m/s)**：映射到 50-80 分
  - 0.8 m/s = 50分
  - 1.1 m/s = 80分

- **不佳範圍 (<0.8 m/s)**：映射到 10-50 分
  - 0.27 m/s（0.8/3）= 10分
  - 0.8 m/s = 50分
  - 低於0.27 m/s = 10分（最低分）

- **靜止或接近靜止 (≤0.01 m/s)**：0分

## 配置文件設定

### settings.yaml 中的相關參數

```yaml
gait_analysis:
  # 速度條件閾值設定 - 雙模式支援
  speed_thresholds:
    # 實際速度閾值（當 pixel_to_meter 不為 null 時使用）
    meters_per_second:
      excellent: 1.1    # 優良：每秒1.1公尺以上
      good: 0.8         # 一般：每秒0.8-1.1公尺

    # 相對速度閾值（當 pixel_to_meter 為 null 時使用）
    pixels_per_second:
      excellent: 50     # 優良：每秒50像素以上
      good: 20          # 一般：每秒20-50像素

  # 評分權重設定
  scoring:
    angle_weight: 0.3    # 角度條件權重（30%）
    speed_weight: 0.7    # 速度條件權重（70%）

  # 平滑處理參數
  smoothing:
    speed_window: 5      # 速度平滑視窗大小
    score_window: 3      # 評分平滑視窗大小

# 系統參數
system:
  pixel_to_meter: null    # 像素到公尺轉換比例
  # null = 使用相對速度模式
  # 數值 = 使用實際速度模式（例如：0.001 表示1像素=0.001公尺）
```

## 參數調整指南

### 1. 速度閾值調整

根據使用的測量模式調整對應的閾值：

**實際速度模式調整：**
```yaml
speed_thresholds:
  meters_per_second:
    excellent: 1.1    # 調整優良速度下限（m/s）
    good: 0.8         # 調整一般速度下限（m/s）
```

**相對速度模式調整：**
```yaml
speed_thresholds:
  pixels_per_second:
    excellent: 50     # 調整優良速度下限（pixels/s）
    good: 20          # 調整一般速度下限（pixels/s）
```

**調整建議：**
- **實際速度**：根據醫學標準或實際測試結果調整
- **相對速度**：根據攝影機解析度和距離調整
- **考慮因素**：測試對象的年齡、身體狀況、康復階段、攝影機設置

### 2. 評分權重調整

如果需要改變速度在總評分中的重要性：

```yaml
scoring:
  angle_weight: 0.6    # 角度重要性
  speed_weight: 0.4    # 速度重要性
```

**調整原則：**
- 兩個權重的總和必須等於 1.0
- 更重視速度：增加 `speed_weight`，減少 `angle_weight`
- 更重視角度：增加 `angle_weight`，減少 `speed_weight`

**範例配置：**
```yaml
# 更重視速度的配置
scoring:
  angle_weight: 0.4
  speed_weight: 0.6

# 更重視角度的配置
scoring:
  angle_weight: 0.7
  speed_weight: 0.3
```

### 3. 平滑參數調整

控制數據的平滑程度：

```yaml
smoothing:
  speed_window: 5      # 速度平滑視窗
  score_window: 3      # 評分平滑視窗
```

**調整效果：**
- **增加數值**：更平滑但反應較慢
- **減少數值**：反應較快但可能有波動

### 4. 像素轉換設定

如果要使用實際距離測量：

```yaml
system:
  pixel_to_meter: 0.001    # 例如：1像素 = 0.001公尺
```

**校準方法：**
1. 在攝影機畫面中放置已知長度的物體
2. 測量該物體在畫面中的像素長度
3. 計算比例：`pixel_to_meter = 實際長度(m) / 像素長度`

## 測試和驗證

### 使用測試腳本

運行測試腳本來驗證配置：

```bash
python test_speed_thresholds.py
```

### 測試項目

1. **速度閾值測試**：驗證不同速度的評分
2. **綜合評分測試**：測試角度和速度的組合評分
3. **權重配置測試**：確認權重設定正確
4. **配置載入測試**：驗證 settings.yaml 正確載入

### 實際測試建議

1. **基準測試**：讓正常人以不同速度行走，記錄評分
2. **對照測試**：與醫學標準或其他評估工具對比
3. **調整優化**：根據實際使用情況微調參數

## 常見問題

### Q: 為什麼沒有 poor 閾值？
A: 系統自動將低於 `good` 閾值的速度判定為不佳，無需額外設定。

### Q: 如何恢復預設設定？
A: 刪除或重命名 `settings.yaml` 文件，系統會使用內建的預設配置。

### Q: 權重設定錯誤會怎樣？
A: 如果權重總和不等於1.0，測試腳本會發出警告，但系統仍會運行。

### Q: 速度單位是什麼？
A: 預設使用公尺/秒(m/s)。如果 `pixel_to_meter` 為 null，則使用相對速度單位。

## 更新記錄

- **2024-07-30**：更新速度標準為 優良≥1.1m/s，一般0.8-1.1m/s，不佳<0.8m/s
- 修正硬編碼權重問題，改為使用 settings.yaml 配置
- 新增完整的中文註解和參數調整指南
