import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QAbstractLight"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Render/Light 2.0", "Qt3D.Render/Light 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "Type"
            values: ["PointLight", "DirectionalLight", "SpotLight"]
        }
        Property { name: "type"; type: "Type"; read: "type"; index: 0; isReadonly: true }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 1
        }
        Property {
            name: "intensity"
            type: "float"
            read: "intensity"
            write: "setIntensity"
            notify: "intensityChanged"
            index: 2
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "intensityChanged"
            Parameter { name: "intensity"; type: "float" }
        }
        Method {
            name: "setColor"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "setIntensity"
            Parameter { name: "intensity"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QAbstractTexture"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/Texture 2.0",
            "Qt3D.Render/Texture 2.13",
            "Qt3D.Render/Texture 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 525, 1536]
        Enum {
            name: "Status"
            values: ["None", "Loading", "Ready", "Error"]
        }
        Enum {
            name: "Target"
            values: [
                "TargetAutomatic",
                "Target1D",
                "Target1DArray",
                "Target2D",
                "Target2DArray",
                "Target3D",
                "TargetCubeMap",
                "TargetCubeMapArray",
                "Target2DMultisample",
                "Target2DMultisampleArray",
                "TargetRectangle",
                "TargetBuffer"
            ]
        }
        Enum {
            name: "TextureFormat"
            values: [
                "NoFormat",
                "Automatic",
                "R8_UNorm",
                "RG8_UNorm",
                "RGB8_UNorm",
                "RGBA8_UNorm",
                "R16_UNorm",
                "RG16_UNorm",
                "RGB16_UNorm",
                "RGBA16_UNorm",
                "R8_SNorm",
                "RG8_SNorm",
                "RGB8_SNorm",
                "RGBA8_SNorm",
                "R16_SNorm",
                "RG16_SNorm",
                "RGB16_SNorm",
                "RGBA16_SNorm",
                "R8U",
                "RG8U",
                "RGB8U",
                "RGBA8U",
                "R16U",
                "RG16U",
                "RGB16U",
                "RGBA16U",
                "R32U",
                "RG32U",
                "RGB32U",
                "RGBA32U",
                "R8I",
                "RG8I",
                "RGB8I",
                "RGBA8I",
                "R16I",
                "RG16I",
                "RGB16I",
                "RGBA16I",
                "R32I",
                "RG32I",
                "RGB32I",
                "RGBA32I",
                "R16F",
                "RG16F",
                "RGB16F",
                "RGBA16F",
                "R32F",
                "RG32F",
                "RGB32F",
                "RGBA32F",
                "RGB9E5",
                "RG11B10F",
                "RG3B2",
                "R5G6B5",
                "RGB5A1",
                "RGBA4",
                "RGB10A2",
                "RGB10A2U",
                "D16",
                "D24",
                "D24S8",
                "D32",
                "D32F",
                "D32FS8X24",
                "RGB_DXT1",
                "RGBA_DXT1",
                "RGBA_DXT3",
                "RGBA_DXT5",
                "R_ATI1N_UNorm",
                "R_ATI1N_SNorm",
                "RG_ATI2N_UNorm",
                "RG_ATI2N_SNorm",
                "RGB_BP_UNSIGNED_FLOAT",
                "RGB_BP_SIGNED_FLOAT",
                "RGB_BP_UNorm",
                "R11_EAC_UNorm",
                "R11_EAC_SNorm",
                "RG11_EAC_UNorm",
                "RG11_EAC_SNorm",
                "RGB8_ETC2",
                "SRGB8_ETC2",
                "RGB8_PunchThrough_Alpha1_ETC2",
                "SRGB8_PunchThrough_Alpha1_ETC2",
                "RGBA8_ETC2_EAC",
                "SRGB8_Alpha8_ETC2_EAC",
                "RGB8_ETC1",
                "SRGB8",
                "SRGB8_Alpha8",
                "SRGB_DXT1",
                "SRGB_Alpha_DXT1",
                "SRGB_Alpha_DXT3",
                "SRGB_Alpha_DXT5",
                "SRGB_BP_UNorm",
                "DepthFormat",
                "AlphaFormat",
                "RGBFormat",
                "RGBAFormat",
                "LuminanceFormat",
                "LuminanceAlphaFormat"
            ]
        }
        Enum {
            name: "Filter"
            values: [
                "Nearest",
                "Linear",
                "NearestMipMapNearest",
                "NearestMipMapLinear",
                "LinearMipMapNearest",
                "LinearMipMapLinear"
            ]
        }
        Enum {
            name: "CubeMapFace"
            values: [
                "CubeMapPositiveX",
                "CubeMapNegativeX",
                "CubeMapPositiveY",
                "CubeMapNegativeY",
                "CubeMapPositiveZ",
                "CubeMapNegativeZ",
                "AllFaces"
            ]
        }
        Enum {
            name: "ComparisonFunction"
            values: [
                "CompareLessEqual",
                "CompareGreaterEqual",
                "CompareLess",
                "CompareGreater",
                "CompareEqual",
                "CommpareNotEqual",
                "CompareAlways",
                "CompareNever"
            ]
        }
        Enum {
            name: "ComparisonMode"
            values: ["CompareRefToTexture", "CompareNone"]
        }
        Enum {
            name: "HandleType"
            values: ["NoHandle", "OpenGLTextureId", "RHITextureId"]
        }
        Property {
            name: "target"
            type: "Target"
            read: "target"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "format"
            type: "TextureFormat"
            read: "format"
            write: "setFormat"
            notify: "formatChanged"
            index: 1
        }
        Property {
            name: "generateMipMaps"
            type: "bool"
            read: "generateMipMaps"
            write: "setGenerateMipMaps"
            notify: "generateMipMapsChanged"
            index: 2
        }
        Property {
            name: "wrapMode"
            type: "Qt3DRender::QTextureWrapMode"
            isPointer: true
            read: "wrapMode"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "width"
            type: "int"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 5
        }
        Property {
            name: "height"
            type: "int"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 6
        }
        Property {
            name: "depth"
            type: "int"
            read: "depth"
            write: "setDepth"
            notify: "depthChanged"
            index: 7
        }
        Property {
            name: "mipLevels"
            type: "int"
            read: "mipLevels"
            write: "setMipLevels"
            notify: "mipLevelsChanged"
            index: 8
        }
        Property {
            name: "magnificationFilter"
            type: "Filter"
            read: "magnificationFilter"
            write: "setMagnificationFilter"
            notify: "magnificationFilterChanged"
            index: 9
        }
        Property {
            name: "minificationFilter"
            type: "Filter"
            read: "minificationFilter"
            write: "setMinificationFilter"
            notify: "minificationFilterChanged"
            index: 10
        }
        Property {
            name: "maximumAnisotropy"
            type: "float"
            read: "maximumAnisotropy"
            write: "setMaximumAnisotropy"
            notify: "maximumAnisotropyChanged"
            index: 11
        }
        Property {
            name: "comparisonFunction"
            type: "ComparisonFunction"
            read: "comparisonFunction"
            write: "setComparisonFunction"
            notify: "comparisonFunctionChanged"
            index: 12
        }
        Property {
            name: "comparisonMode"
            type: "ComparisonMode"
            read: "comparisonMode"
            write: "setComparisonMode"
            notify: "comparisonModeChanged"
            index: 13
        }
        Property {
            name: "layers"
            type: "int"
            read: "layers"
            write: "setLayers"
            notify: "layersChanged"
            index: 14
        }
        Property {
            name: "samples"
            type: "int"
            read: "samples"
            write: "setSamples"
            notify: "samplesChanged"
            index: 15
        }
        Property {
            name: "handleType"
            revision: 525
            type: "HandleType"
            read: "handleType"
            notify: "handleTypeChanged"
            index: 16
            isReadonly: true
        }
        Property {
            name: "handle"
            revision: 525
            type: "QVariant"
            read: "handle"
            notify: "handleChanged"
            index: 17
            isReadonly: true
        }
        Signal {
            name: "formatChanged"
            Parameter { name: "format"; type: "TextureFormat" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Signal {
            name: "generateMipMapsChanged"
            Parameter { name: "generateMipMaps"; type: "bool" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "int" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "int" }
        }
        Signal {
            name: "depthChanged"
            Parameter { name: "depth"; type: "int" }
        }
        Signal {
            name: "magnificationFilterChanged"
            Parameter { name: "magnificationFilter"; type: "Filter" }
        }
        Signal {
            name: "minificationFilterChanged"
            Parameter { name: "minificationFilter"; type: "Filter" }
        }
        Signal {
            name: "maximumAnisotropyChanged"
            Parameter { name: "maximumAnisotropy"; type: "float" }
        }
        Signal {
            name: "comparisonFunctionChanged"
            Parameter { name: "comparisonFunction"; type: "ComparisonFunction" }
        }
        Signal {
            name: "comparisonModeChanged"
            Parameter { name: "comparisonMode"; type: "ComparisonMode" }
        }
        Signal {
            name: "layersChanged"
            Parameter { name: "layers"; type: "int" }
        }
        Signal {
            name: "samplesChanged"
            Parameter { name: "samples"; type: "int" }
        }
        Signal {
            name: "handleTypeChanged"
            revision: 65293
            Parameter { name: "handleType"; type: "HandleType" }
        }
        Signal {
            name: "handleChanged"
            revision: 65293
            Parameter { name: "handle"; type: "QVariant" }
        }
        Signal {
            name: "mipLevelsChanged"
            Parameter { name: "mipLevels"; type: "int" }
        }
        Method {
            name: "setFormat"
            Parameter { name: "format"; type: "TextureFormat" }
        }
        Method {
            name: "setGenerateMipMaps"
            Parameter { name: "gen"; type: "bool" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "width"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "height"; type: "int" }
        }
        Method {
            name: "setDepth"
            Parameter { name: "depth"; type: "int" }
        }
        Method {
            name: "setMinificationFilter"
            Parameter { name: "f"; type: "Filter" }
        }
        Method {
            name: "setMagnificationFilter"
            Parameter { name: "f"; type: "Filter" }
        }
        Method {
            name: "setMaximumAnisotropy"
            Parameter { name: "anisotropy"; type: "float" }
        }
        Method {
            name: "setComparisonFunction"
            Parameter { name: "function"; type: "ComparisonFunction" }
        }
        Method {
            name: "setComparisonMode"
            Parameter { name: "mode"; type: "ComparisonMode" }
        }
        Method {
            name: "setLayers"
            Parameter { name: "layers"; type: "int" }
        }
        Method {
            name: "setSamples"
            Parameter { name: "samples"; type: "int" }
        }
        Method {
            name: "setMipLevels"
            Parameter { name: "mipLevels"; type: "int" }
        }
        Method {
            name: "updateData"
            Parameter { name: "update"; type: "QTextureDataUpdate" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QAbstractTextureImage"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/QAbstractTextureImage 2.0",
            "Qt3D.Render/QAbstractTextureImage 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "mipLevel"
            type: "int"
            read: "mipLevel"
            write: "setMipLevel"
            notify: "mipLevelChanged"
            index: 0
        }
        Property {
            name: "layer"
            type: "int"
            read: "layer"
            write: "setLayer"
            notify: "layerChanged"
            index: 1
        }
        Property {
            name: "face"
            type: "Qt3DRender::QAbstractTexture::CubeMapFace"
            read: "face"
            write: "setFace"
            notify: "faceChanged"
            index: 2
        }
        Signal {
            name: "mipLevelChanged"
            Parameter { name: "mipLevel"; type: "int" }
        }
        Signal {
            name: "layerChanged"
            Parameter { name: "layer"; type: "int" }
        }
        Signal {
            name: "faceChanged"
            Parameter { name: "face"; type: "QAbstractTexture::CubeMapFace" }
        }
        Method {
            name: "setMipLevel"
            Parameter { name: "level"; type: "int" }
        }
        Method {
            name: "setLayer"
            Parameter { name: "layer"; type: "int" }
        }
        Method {
            name: "setFace"
            Parameter { name: "face"; type: "QAbstractTexture::CubeMapFace" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QAlphaCoverage"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: [
            "Qt3D.Render/AlphaCoverage 2.0",
            "Qt3D.Render/AlphaCoverage 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QAlphaTest"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/AlphaTest 2.0", "Qt3D.Render/AlphaTest 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "AlphaFunction"
            values: [
                "Never",
                "Always",
                "Less",
                "LessOrEqual",
                "Equal",
                "GreaterOrEqual",
                "Greater",
                "NotEqual"
            ]
        }
        Property {
            name: "alphaFunction"
            type: "AlphaFunction"
            read: "alphaFunction"
            write: "setAlphaFunction"
            notify: "alphaFunctionChanged"
            index: 0
        }
        Property {
            name: "referenceValue"
            type: "float"
            read: "referenceValue"
            write: "setReferenceValue"
            notify: "referenceValueChanged"
            index: 1
        }
        Signal {
            name: "alphaFunctionChanged"
            Parameter { name: "alphaFunction"; type: "AlphaFunction" }
        }
        Signal {
            name: "referenceValueChanged"
            Parameter { name: "referenceValue"; type: "float" }
        }
        Method {
            name: "setAlphaFunction"
            Parameter { name: "alphaFunction"; type: "AlphaFunction" }
        }
        Method {
            name: "setReferenceValue"
            Parameter { name: "referenceValue"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QBlendEquationArguments"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: [
            "Qt3D.Render/BlendEquationArguments 2.0",
            "Qt3D.Render/BlendEquationArguments 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "Blending"
            values: [
                "Zero",
                "One",
                "SourceColor",
                "SourceAlpha",
                "Source1Alpha",
                "Source1Color",
                "DestinationColor",
                "DestinationAlpha",
                "SourceAlphaSaturate",
                "ConstantColor",
                "ConstantAlpha",
                "OneMinusSourceColor",
                "OneMinusSourceAlpha",
                "OneMinusDestinationAlpha",
                "OneMinusDestinationColor",
                "OneMinusConstantColor",
                "OneMinusConstantAlpha",
                "OneMinusSource1Alpha",
                "OneMinusSource1Color",
                "OneMinusSource1Color0"
            ]
        }
        Property {
            name: "sourceRgb"
            type: "Blending"
            read: "sourceRgb"
            write: "setSourceRgb"
            notify: "sourceRgbChanged"
            index: 0
        }
        Property {
            name: "sourceAlpha"
            type: "Blending"
            read: "sourceAlpha"
            write: "setSourceAlpha"
            notify: "sourceAlphaChanged"
            index: 1
        }
        Property {
            name: "destinationRgb"
            type: "Blending"
            read: "destinationRgb"
            write: "setDestinationRgb"
            notify: "destinationRgbChanged"
            index: 2
        }
        Property {
            name: "destinationAlpha"
            type: "Blending"
            read: "destinationAlpha"
            write: "setDestinationAlpha"
            notify: "destinationAlphaChanged"
            index: 3
        }
        Property {
            name: "bufferIndex"
            type: "int"
            read: "bufferIndex"
            write: "setBufferIndex"
            notify: "bufferIndexChanged"
            index: 4
        }
        Signal {
            name: "sourceRgbChanged"
            Parameter { name: "sourceRgb"; type: "Blending" }
        }
        Signal {
            name: "sourceAlphaChanged"
            Parameter { name: "sourceAlpha"; type: "Blending" }
        }
        Signal {
            name: "destinationRgbChanged"
            Parameter { name: "destinationRgb"; type: "Blending" }
        }
        Signal {
            name: "destinationAlphaChanged"
            Parameter { name: "destinationAlpha"; type: "Blending" }
        }
        Signal {
            name: "sourceRgbaChanged"
            Parameter { name: "sourceRgba"; type: "Blending" }
        }
        Signal {
            name: "destinationRgbaChanged"
            Parameter { name: "destinationRgba"; type: "Blending" }
        }
        Signal {
            name: "bufferIndexChanged"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "setSourceRgb"
            Parameter { name: "sourceRgb"; type: "Blending" }
        }
        Method {
            name: "setDestinationRgb"
            Parameter { name: "destinationRgb"; type: "Blending" }
        }
        Method {
            name: "setSourceAlpha"
            Parameter { name: "sourceAlpha"; type: "Blending" }
        }
        Method {
            name: "setDestinationAlpha"
            Parameter { name: "destinationAlpha"; type: "Blending" }
        }
        Method {
            name: "setSourceRgba"
            Parameter { name: "sourceRgba"; type: "Blending" }
        }
        Method {
            name: "setDestinationRgba"
            Parameter { name: "destinationRgba"; type: "Blending" }
        }
        Method {
            name: "setBufferIndex"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QBlendEquation"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: [
            "Qt3D.Render/BlendEquation 2.0",
            "Qt3D.Render/BlendEquation 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "BlendFunction"
            values: ["Add", "Subtract", "ReverseSubtract", "Min", "Max"]
        }
        Property {
            name: "blendFunction"
            type: "BlendFunction"
            read: "blendFunction"
            write: "setBlendFunction"
            notify: "blendFunctionChanged"
            index: 0
        }
        Signal {
            name: "blendFunctionChanged"
            Parameter { name: "blendFunction"; type: "BlendFunction" }
        }
        Method {
            name: "setBlendFunction"
            Parameter { name: "blendFunction"; type: "BlendFunction" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QBlitFramebuffer"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: [
            "Qt3D.Render/BlitFramebuffer 2.10",
            "Qt3D.Render/BlitFramebuffer 6.0"
        ]
        exportMetaObjectRevisions: [522, 1536]
        Enum {
            name: "InterpolationMethod"
            values: ["Nearest", "Linear"]
        }
        Property {
            name: "source"
            type: "Qt3DRender::QRenderTarget"
            isPointer: true
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "destination"
            type: "Qt3DRender::QRenderTarget"
            isPointer: true
            read: "destination"
            write: "setDestination"
            notify: "destinationChanged"
            index: 1
        }
        Property {
            name: "sourceRect"
            type: "QRectF"
            read: "sourceRect"
            write: "setSourceRect"
            notify: "sourceRectChanged"
            index: 2
        }
        Property {
            name: "destinationRect"
            type: "QRectF"
            read: "destinationRect"
            write: "setDestinationRect"
            notify: "destinationRectChanged"
            index: 3
        }
        Property {
            name: "sourceAttachmentPoint"
            type: "Qt3DRender::QRenderTargetOutput::AttachmentPoint"
            read: "sourceAttachmentPoint"
            write: "setSourceAttachmentPoint"
            notify: "sourceAttachmentPointChanged"
            index: 4
        }
        Property {
            name: "destinationAttachmentPoint"
            type: "Qt3DRender::QRenderTargetOutput::AttachmentPoint"
            read: "destinationAttachmentPoint"
            write: "setDestinationAttachmentPoint"
            notify: "destinationAttachmentPointChanged"
            index: 5
        }
        Property {
            name: "interpolationMethod"
            type: "InterpolationMethod"
            read: "interpolationMethod"
            write: "setInterpolationMethod"
            notify: "interpolationMethodChanged"
            index: 6
        }
        Signal { name: "sourceChanged" }
        Signal { name: "destinationChanged" }
        Signal { name: "sourceRectChanged" }
        Signal { name: "destinationRectChanged" }
        Signal { name: "sourceAttachmentPointChanged" }
        Signal { name: "destinationAttachmentPointChanged" }
        Signal { name: "interpolationMethodChanged" }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QBufferCapture"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: [
            "Qt3D.Render/BufferCapture 2.9",
            "Qt3D.Render/BufferCapture 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QCamera"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QEntity"
        exports: [
            "Qt3D.Render/Camera 2.0",
            "Qt3D.Render/Camera 2.9",
            "Qt3D.Render/Camera 2.14",
            "Qt3D.Render/Camera 6.0"
        ]
        exportMetaObjectRevisions: [512, 521, 526, 1536]
        Enum {
            name: "CameraTranslationOption"
            values: ["TranslateViewCenter", "DontTranslateViewCenter"]
        }
        Property {
            name: "projectionType"
            type: "Qt3DRender::QCameraLens::ProjectionType"
            read: "projectionType"
            write: "setProjectionType"
            notify: "projectionTypeChanged"
            index: 0
        }
        Property {
            name: "nearPlane"
            type: "float"
            read: "nearPlane"
            write: "setNearPlane"
            notify: "nearPlaneChanged"
            index: 1
        }
        Property {
            name: "farPlane"
            type: "float"
            read: "farPlane"
            write: "setFarPlane"
            notify: "farPlaneChanged"
            index: 2
        }
        Property {
            name: "fieldOfView"
            type: "float"
            read: "fieldOfView"
            write: "setFieldOfView"
            notify: "fieldOfViewChanged"
            index: 3
        }
        Property {
            name: "aspectRatio"
            type: "float"
            read: "aspectRatio"
            write: "setAspectRatio"
            notify: "aspectRatioChanged"
            index: 4
        }
        Property {
            name: "left"
            type: "float"
            read: "left"
            write: "setLeft"
            notify: "leftChanged"
            index: 5
        }
        Property {
            name: "right"
            type: "float"
            read: "right"
            write: "setRight"
            notify: "rightChanged"
            index: 6
        }
        Property {
            name: "bottom"
            type: "float"
            read: "bottom"
            write: "setBottom"
            notify: "bottomChanged"
            index: 7
        }
        Property { name: "top"; type: "float"; read: "top"; write: "setTop"; notify: "topChanged"; index: 8 }
        Property {
            name: "projectionMatrix"
            type: "QMatrix4x4"
            read: "projectionMatrix"
            write: "setProjectionMatrix"
            notify: "projectionMatrixChanged"
            index: 9
        }
        Property {
            name: "exposure"
            revision: 521
            type: "float"
            read: "exposure"
            write: "setExposure"
            notify: "exposureChanged"
            index: 10
        }
        Property {
            name: "position"
            type: "QVector3D"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 11
        }
        Property {
            name: "upVector"
            type: "QVector3D"
            read: "upVector"
            write: "setUpVector"
            notify: "upVectorChanged"
            index: 12
        }
        Property {
            name: "viewCenter"
            type: "QVector3D"
            read: "viewCenter"
            write: "setViewCenter"
            notify: "viewCenterChanged"
            index: 13
        }
        Property {
            name: "viewVector"
            type: "QVector3D"
            read: "viewVector"
            notify: "viewVectorChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "viewMatrix"
            type: "QMatrix4x4"
            read: "viewMatrix"
            notify: "viewMatrixChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "lens"
            revision: 526
            type: "Qt3DRender::QCameraLens"
            isPointer: true
            read: "lens"
            index: 16
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "transform"
            revision: 526
            type: "Qt3DCore::QTransform"
            isPointer: true
            read: "transform"
            index: 17
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "projectionTypeChanged"
            Parameter { name: "projectionType"; type: "QCameraLens::ProjectionType" }
        }
        Signal {
            name: "nearPlaneChanged"
            Parameter { name: "nearPlane"; type: "float" }
        }
        Signal {
            name: "farPlaneChanged"
            Parameter { name: "farPlane"; type: "float" }
        }
        Signal {
            name: "fieldOfViewChanged"
            Parameter { name: "fieldOfView"; type: "float" }
        }
        Signal {
            name: "aspectRatioChanged"
            Parameter { name: "aspectRatio"; type: "float" }
        }
        Signal {
            name: "leftChanged"
            Parameter { name: "left"; type: "float" }
        }
        Signal {
            name: "rightChanged"
            Parameter { name: "right"; type: "float" }
        }
        Signal {
            name: "bottomChanged"
            Parameter { name: "bottom"; type: "float" }
        }
        Signal {
            name: "topChanged"
            Parameter { name: "top"; type: "float" }
        }
        Signal {
            name: "projectionMatrixChanged"
            Parameter { name: "projectionMatrix"; type: "QMatrix4x4" }
        }
        Signal {
            name: "exposureChanged"
            Parameter { name: "exposure"; type: "float" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "QVector3D" }
        }
        Signal {
            name: "upVectorChanged"
            Parameter { name: "upVector"; type: "QVector3D" }
        }
        Signal {
            name: "viewCenterChanged"
            Parameter { name: "viewCenter"; type: "QVector3D" }
        }
        Signal {
            name: "viewVectorChanged"
            Parameter { name: "viewVector"; type: "QVector3D" }
        }
        Signal { name: "viewMatrixChanged" }
        Method {
            name: "setProjectionType"
            Parameter { name: "type"; type: "QCameraLens::ProjectionType" }
        }
        Method {
            name: "setNearPlane"
            Parameter { name: "nearPlane"; type: "float" }
        }
        Method {
            name: "setFarPlane"
            Parameter { name: "farPlane"; type: "float" }
        }
        Method {
            name: "setFieldOfView"
            Parameter { name: "fieldOfView"; type: "float" }
        }
        Method {
            name: "setAspectRatio"
            Parameter { name: "aspectRatio"; type: "float" }
        }
        Method {
            name: "setLeft"
            Parameter { name: "left"; type: "float" }
        }
        Method {
            name: "setRight"
            Parameter { name: "right"; type: "float" }
        }
        Method {
            name: "setBottom"
            Parameter { name: "bottom"; type: "float" }
        }
        Method {
            name: "setTop"
            Parameter { name: "top"; type: "float" }
        }
        Method {
            name: "setProjectionMatrix"
            Parameter { name: "projectionMatrix"; type: "QMatrix4x4" }
        }
        Method {
            name: "setExposure"
            Parameter { name: "exposure"; type: "float" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "QVector3D" }
        }
        Method {
            name: "setUpVector"
            Parameter { name: "upVector"; type: "QVector3D" }
        }
        Method {
            name: "setViewCenter"
            Parameter { name: "viewCenter"; type: "QVector3D" }
        }
        Method { name: "viewAll" }
        Method {
            name: "viewSphere"
            Parameter { name: "center"; type: "QVector3D" }
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "viewEntity"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "tiltRotation"
            type: "QQuaternion"
            isMethodConstant: true
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "panRotation"
            type: "QQuaternion"
            isMethodConstant: true
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "rollRotation"
            type: "QQuaternion"
            isMethodConstant: true
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "rotation"
            type: "QQuaternion"
            isMethodConstant: true
            Parameter { name: "angle"; type: "float" }
            Parameter { name: "axis"; type: "QVector3D" }
        }
        Method {
            name: "translate"
            Parameter { name: "vLocal"; type: "QVector3D" }
            Parameter { name: "option"; type: "CameraTranslationOption" }
        }
        Method {
            name: "translate"
            isCloned: true
            Parameter { name: "vLocal"; type: "QVector3D" }
        }
        Method {
            name: "translateWorld"
            Parameter { name: "vWorld"; type: "QVector3D" }
            Parameter { name: "option"; type: "CameraTranslationOption" }
        }
        Method {
            name: "translateWorld"
            isCloned: true
            Parameter { name: "vWorld"; type: "QVector3D" }
        }
        Method {
            name: "tilt"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "pan"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "pan"
            Parameter { name: "angle"; type: "float" }
            Parameter { name: "axis"; type: "QVector3D" }
        }
        Method {
            name: "roll"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "tiltAboutViewCenter"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "panAboutViewCenter"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "panAboutViewCenter"
            Parameter { name: "angle"; type: "float" }
            Parameter { name: "axis"; type: "QVector3D" }
        }
        Method {
            name: "rollAboutViewCenter"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "rotate"
            Parameter { name: "q"; type: "QQuaternion" }
        }
        Method {
            name: "rotateAboutViewCenter"
            Parameter { name: "q"; type: "QQuaternion" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QCameraLens"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Render/CameraLens 2.0",
            "Qt3D.Render/CameraLens 2.9",
            "Qt3D.Render/CameraLens 6.0"
        ]
        exportMetaObjectRevisions: [512, 521, 1536]
        Enum {
            name: "ProjectionType"
            values: [
                "OrthographicProjection",
                "PerspectiveProjection",
                "FrustumProjection",
                "CustomProjection"
            ]
        }
        Property {
            name: "projectionType"
            type: "ProjectionType"
            read: "projectionType"
            write: "setProjectionType"
            notify: "projectionTypeChanged"
            index: 0
        }
        Property {
            name: "nearPlane"
            type: "float"
            read: "nearPlane"
            write: "setNearPlane"
            notify: "nearPlaneChanged"
            index: 1
        }
        Property {
            name: "farPlane"
            type: "float"
            read: "farPlane"
            write: "setFarPlane"
            notify: "farPlaneChanged"
            index: 2
        }
        Property {
            name: "fieldOfView"
            type: "float"
            read: "fieldOfView"
            write: "setFieldOfView"
            notify: "fieldOfViewChanged"
            index: 3
        }
        Property {
            name: "aspectRatio"
            type: "float"
            read: "aspectRatio"
            write: "setAspectRatio"
            notify: "aspectRatioChanged"
            index: 4
        }
        Property {
            name: "left"
            type: "float"
            read: "left"
            write: "setLeft"
            notify: "leftChanged"
            index: 5
        }
        Property {
            name: "right"
            type: "float"
            read: "right"
            write: "setRight"
            notify: "rightChanged"
            index: 6
        }
        Property {
            name: "bottom"
            type: "float"
            read: "bottom"
            write: "setBottom"
            notify: "bottomChanged"
            index: 7
        }
        Property { name: "top"; type: "float"; read: "top"; write: "setTop"; notify: "topChanged"; index: 8 }
        Property {
            name: "projectionMatrix"
            type: "QMatrix4x4"
            read: "projectionMatrix"
            write: "setProjectionMatrix"
            notify: "projectionMatrixChanged"
            index: 9
        }
        Property {
            name: "exposure"
            revision: 521
            type: "float"
            read: "exposure"
            write: "setExposure"
            notify: "exposureChanged"
            index: 10
        }
        Signal {
            name: "projectionTypeChanged"
            Parameter { name: "projectionType"; type: "QCameraLens::ProjectionType" }
        }
        Signal {
            name: "nearPlaneChanged"
            Parameter { name: "nearPlane"; type: "float" }
        }
        Signal {
            name: "farPlaneChanged"
            Parameter { name: "farPlane"; type: "float" }
        }
        Signal {
            name: "fieldOfViewChanged"
            Parameter { name: "fieldOfView"; type: "float" }
        }
        Signal {
            name: "aspectRatioChanged"
            Parameter { name: "aspectRatio"; type: "float" }
        }
        Signal {
            name: "leftChanged"
            Parameter { name: "left"; type: "float" }
        }
        Signal {
            name: "rightChanged"
            Parameter { name: "right"; type: "float" }
        }
        Signal {
            name: "bottomChanged"
            Parameter { name: "bottom"; type: "float" }
        }
        Signal {
            name: "topChanged"
            Parameter { name: "top"; type: "float" }
        }
        Signal {
            name: "projectionMatrixChanged"
            Parameter { name: "projectionMatrix"; type: "QMatrix4x4" }
        }
        Signal {
            name: "exposureChanged"
            Parameter { name: "exposure"; type: "float" }
        }
        Signal {
            name: "viewSphere"
            Parameter { name: "center"; type: "QVector3D" }
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setProjectionType"
            Parameter { name: "projectionType"; type: "ProjectionType" }
        }
        Method {
            name: "setNearPlane"
            Parameter { name: "nearPlane"; type: "float" }
        }
        Method {
            name: "setFarPlane"
            Parameter { name: "farPlane"; type: "float" }
        }
        Method {
            name: "setFieldOfView"
            Parameter { name: "fieldOfView"; type: "float" }
        }
        Method {
            name: "setAspectRatio"
            Parameter { name: "aspectRatio"; type: "float" }
        }
        Method {
            name: "setLeft"
            Parameter { name: "left"; type: "float" }
        }
        Method {
            name: "setRight"
            Parameter { name: "right"; type: "float" }
        }
        Method {
            name: "setBottom"
            Parameter { name: "bottom"; type: "float" }
        }
        Method {
            name: "setTop"
            Parameter { name: "top"; type: "float" }
        }
        Method {
            name: "setProjectionMatrix"
            Parameter { name: "projectionMatrix"; type: "QMatrix4x4" }
        }
        Method {
            name: "setExposure"
            Parameter { name: "exposure"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QCameraSelector"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        extension: "Qt3DCore::Quick::Quick3DNode"
        exports: [
            "Qt3D.Render/CameraSelector 2.0",
            "Qt3D.Render/CameraSelector 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "camera"
            type: "Qt3DCore::QEntity"
            isPointer: true
            read: "camera"
            write: "setCamera"
            notify: "cameraChanged"
            index: 0
        }
        Signal {
            name: "cameraChanged"
            Parameter { name: "camera"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "setCamera"
            Parameter { name: "camera"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QClearBuffers"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: [
            "Qt3D.Render/ClearBuffers 2.0",
            "Qt3D.Render/ClearBuffers 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "BufferType"
            values: [
                "None",
                "ColorBuffer",
                "DepthBuffer",
                "StencilBuffer",
                "DepthStencilBuffer",
                "ColorDepthBuffer",
                "ColorDepthStencilBuffer",
                "AllBuffers"
            ]
        }
        Property {
            name: "buffers"
            type: "BufferType"
            read: "buffers"
            write: "setBuffers"
            notify: "buffersChanged"
            index: 0
        }
        Property {
            name: "clearColor"
            type: "QColor"
            read: "clearColor"
            write: "setClearColor"
            notify: "clearColorChanged"
            index: 1
        }
        Property {
            name: "clearDepthValue"
            type: "float"
            read: "clearDepthValue"
            write: "setClearDepthValue"
            notify: "clearDepthValueChanged"
            index: 2
        }
        Property {
            name: "clearStencilValue"
            type: "int"
            read: "clearStencilValue"
            write: "setClearStencilValue"
            notify: "clearStencilValueChanged"
            index: 3
        }
        Property {
            name: "colorBuffer"
            type: "Qt3DRender::QRenderTargetOutput"
            isPointer: true
            read: "colorBuffer"
            write: "setColorBuffer"
            notify: "colorBufferChanged"
            index: 4
        }
        Signal {
            name: "buffersChanged"
            Parameter { name: "buffers"; type: "BufferType" }
        }
        Signal {
            name: "clearColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "clearDepthValueChanged"
            Parameter { name: "clearDepthValue"; type: "float" }
        }
        Signal {
            name: "clearStencilValueChanged"
            Parameter { name: "clearStencilValue"; type: "int" }
        }
        Signal {
            name: "colorBufferChanged"
            Parameter { name: "buffer"; type: "QRenderTargetOutput"; isPointer: true }
        }
        Method {
            name: "setBuffers"
            Parameter { name: "buffers"; type: "BufferType" }
        }
        Method {
            name: "setClearColor"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "setClearDepthValue"
            Parameter { name: "clearDepthValue"; type: "float" }
        }
        Method {
            name: "setClearStencilValue"
            Parameter { name: "clearStencilValue"; type: "int" }
        }
        Method {
            name: "setColorBuffer"
            Parameter { name: "buffer"; type: "QRenderTargetOutput"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QClipPlane"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/ClipPlane 2.0", "Qt3D.Render/ClipPlane 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "planeIndex"
            type: "int"
            read: "planeIndex"
            write: "setPlaneIndex"
            notify: "planeIndexChanged"
            index: 0
        }
        Property {
            name: "normal"
            type: "QVector3D"
            read: "normal"
            write: "setNormal"
            notify: "normalChanged"
            index: 1
        }
        Property {
            name: "distance"
            type: "float"
            read: "distance"
            write: "setDistance"
            notify: "distanceChanged"
            index: 2
        }
        Signal {
            name: "planeIndexChanged"
            Parameter { name: "planeIndex"; type: "int" }
        }
        Signal {
            name: "normalChanged"
            Parameter { name: "normal"; type: "QVector3D" }
        }
        Signal {
            name: "distanceChanged"
            Parameter { name: "distance"; type: "float" }
        }
        Method {
            name: "setPlaneIndex"
            Parameter { type: "int" }
        }
        Method {
            name: "setNormal"
            Parameter { type: "QVector3D" }
        }
        Method {
            name: "setDistance"
            Parameter { type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QColorMask"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/ColorMask 2.0", "Qt3D.Render/ColorMask 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "redMasked"
            type: "bool"
            read: "isRedMasked"
            write: "setRedMasked"
            notify: "redMaskedChanged"
            index: 0
        }
        Property {
            name: "greenMasked"
            type: "bool"
            read: "isGreenMasked"
            write: "setGreenMasked"
            notify: "greenMaskedChanged"
            index: 1
        }
        Property {
            name: "blueMasked"
            type: "bool"
            read: "isBlueMasked"
            write: "setBlueMasked"
            notify: "blueMaskedChanged"
            index: 2
        }
        Property {
            name: "alphaMasked"
            type: "bool"
            read: "isAlphaMasked"
            write: "setAlphaMasked"
            notify: "alphaMaskedChanged"
            index: 3
        }
        Signal {
            name: "redMaskedChanged"
            Parameter { name: "redMasked"; type: "bool" }
        }
        Signal {
            name: "greenMaskedChanged"
            Parameter { name: "greenMasked"; type: "bool" }
        }
        Signal {
            name: "blueMaskedChanged"
            Parameter { name: "blueMasked"; type: "bool" }
        }
        Signal {
            name: "alphaMaskedChanged"
            Parameter { name: "alphaMasked"; type: "bool" }
        }
        Method {
            name: "setRedMasked"
            Parameter { name: "redMasked"; type: "bool" }
        }
        Method {
            name: "setGreenMasked"
            Parameter { name: "greenMasked"; type: "bool" }
        }
        Method {
            name: "setBlueMasked"
            Parameter { name: "blueMasked"; type: "bool" }
        }
        Method {
            name: "setAlphaMasked"
            Parameter { name: "alphaMasked"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QComputeCommand"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Render/ComputeCommand 2.0",
            "Qt3D.Render/ComputeCommand 2.13",
            "Qt3D.Render/ComputeCommand 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
        Enum {
            name: "RunType"
            values: ["Continuous", "Manual"]
        }
        Property {
            name: "workGroupX"
            type: "int"
            read: "workGroupX"
            write: "setWorkGroupX"
            notify: "workGroupXChanged"
            index: 0
        }
        Property {
            name: "workGroupY"
            type: "int"
            read: "workGroupY"
            write: "setWorkGroupY"
            notify: "workGroupYChanged"
            index: 1
        }
        Property {
            name: "workGroupZ"
            type: "int"
            read: "workGroupZ"
            write: "setWorkGroupZ"
            notify: "workGroupZChanged"
            index: 2
        }
        Property {
            name: "runType"
            revision: 525
            type: "RunType"
            read: "runType"
            write: "setRunType"
            notify: "runTypeChanged"
            index: 3
        }
        Signal { name: "workGroupXChanged" }
        Signal { name: "workGroupYChanged" }
        Signal { name: "workGroupZChanged" }
        Signal { name: "runTypeChanged" }
        Method {
            name: "setWorkGroupX"
            Parameter { name: "workGroupX"; type: "int" }
        }
        Method {
            name: "setWorkGroupY"
            Parameter { name: "workGroupY"; type: "int" }
        }
        Method {
            name: "setWorkGroupZ"
            Parameter { name: "workGroupZ"; type: "int" }
        }
        Method {
            name: "setRunType"
            revision: 525
            Parameter { name: "runType"; type: "RunType" }
        }
        Method {
            name: "trigger"
            revision: 525
            Parameter { name: "frameCount"; type: "int" }
        }
        Method { name: "trigger"; revision: 525; isCloned: true }
        Method {
            name: "trigger"
            revision: 525
            Parameter { name: "workGroupX"; type: "int" }
            Parameter { name: "workGroupY"; type: "int" }
            Parameter { name: "workGroupZ"; type: "int" }
            Parameter { name: "frameCount"; type: "int" }
        }
        Method {
            name: "trigger"
            revision: 525
            isCloned: true
            Parameter { name: "workGroupX"; type: "int" }
            Parameter { name: "workGroupY"; type: "int" }
            Parameter { name: "workGroupZ"; type: "int" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QCullFace"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/CullFace 2.0", "Qt3D.Render/CullFace 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "CullingMode"
            values: ["NoCulling", "Front", "Back", "FrontAndBack"]
        }
        Property {
            name: "mode"
            type: "CullingMode"
            read: "mode"
            write: "setMode"
            notify: "modeChanged"
            index: 0
        }
        Signal {
            name: "modeChanged"
            Parameter { name: "mode"; type: "CullingMode" }
        }
        Method {
            name: "setMode"
            Parameter { name: "mode"; type: "CullingMode" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QDebugOverlay"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: [
            "Qt3D.Render/DebugOverlay 2.16",
            "Qt3D.Render/DebugOverlay 6.0"
        ]
        exportMetaObjectRevisions: [528, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QDepthRange"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/DepthRange 2.14", "Qt3D.Render/DepthRange 6.0"]
        exportMetaObjectRevisions: [526, 1536]
        Property {
            name: "nearValue"
            type: "double"
            read: "nearValue"
            write: "setNearValue"
            notify: "nearValueChanged"
            index: 0
        }
        Property {
            name: "farValue"
            type: "double"
            read: "farValue"
            write: "setFarValue"
            notify: "farValueChanged"
            index: 1
        }
        Signal {
            name: "nearValueChanged"
            Parameter { name: "nearValue"; type: "double" }
        }
        Signal {
            name: "farValueChanged"
            Parameter { name: "farValue"; type: "double" }
        }
        Method {
            name: "setNearValue"
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "setFarValue"
            Parameter { name: "value"; type: "double" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QDepthTest"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/DepthTest 2.0", "Qt3D.Render/DepthTest 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "DepthFunction"
            values: [
                "Never",
                "Always",
                "Less",
                "LessOrEqual",
                "Equal",
                "GreaterOrEqual",
                "Greater",
                "NotEqual"
            ]
        }
        Property {
            name: "depthFunction"
            type: "DepthFunction"
            read: "depthFunction"
            write: "setDepthFunction"
            notify: "depthFunctionChanged"
            index: 0
        }
        Signal {
            name: "depthFunctionChanged"
            Parameter { name: "depthFunction"; type: "DepthFunction" }
        }
        Method {
            name: "setDepthFunction"
            Parameter { name: "depthFunction"; type: "DepthFunction" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QDirectionalLight"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractLight"
        exports: [
            "Qt3D.Render/DirectionalLight 2.0",
            "Qt3D.Render/DirectionalLight 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "worldDirection"
            type: "QVector3D"
            read: "worldDirection"
            write: "setWorldDirection"
            notify: "worldDirectionChanged"
            index: 0
        }
        Signal {
            name: "worldDirectionChanged"
            Parameter { name: "worldDirection"; type: "QVector3D" }
        }
        Method {
            name: "setWorldDirection"
            Parameter { name: "worldDirection"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QDispatchCompute"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: [
            "Qt3D.Render/DispatchCompute 2.0",
            "Qt3D.Render/DispatchCompute 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "workGroupX"
            type: "int"
            read: "workGroupX"
            write: "setWorkGroupX"
            notify: "workGroupXChanged"
            index: 0
        }
        Property {
            name: "workGroupY"
            type: "int"
            read: "workGroupY"
            write: "setWorkGroupY"
            notify: "workGroupYChanged"
            index: 1
        }
        Property {
            name: "workGroupZ"
            type: "int"
            read: "workGroupZ"
            write: "setWorkGroupZ"
            notify: "workGroupZChanged"
            index: 2
        }
        Signal { name: "workGroupXChanged" }
        Signal { name: "workGroupYChanged" }
        Signal { name: "workGroupZChanged" }
        Method {
            name: "setWorkGroupX"
            Parameter { name: "workGroupX"; type: "int" }
        }
        Method {
            name: "setWorkGroupY"
            Parameter { name: "workGroupY"; type: "int" }
        }
        Method {
            name: "setWorkGroupZ"
            Parameter { name: "workGroupZ"; type: "int" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QDithering"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/Dithering 2.0", "Qt3D.Render/Dithering 6.0"]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QEffect"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        extension: "Qt3DRender::Render::Quick::Quick3DEffect"
        exports: ["Qt3D.Render/Effect 2.0", "Qt3D.Render/Effect 6.0"]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QEnvironmentLight"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Render/EnvironmentLight 2.9",
            "Qt3D.Render/EnvironmentLight 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "irradiance"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "irradiance"
            write: "setIrradiance"
            notify: "irradianceChanged"
            index: 0
        }
        Property {
            name: "specular"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "specular"
            write: "setSpecular"
            notify: "specularChanged"
            index: 1
        }
        Signal {
            name: "irradianceChanged"
            Parameter {
                name: "environmentIrradiance"
                type: "Qt3DRender::QAbstractTexture"
                isPointer: true
            }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "environmentSpecular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setIrradiance"
            Parameter { name: "irradiance"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method { name: "_q_updateEnvMapsSize" }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QFilterKey"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/FilterKey 2.0", "Qt3D.Render/FilterKey 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "value"
            type: "QVariant"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 0
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 1
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "setValue"
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "setName"
            Parameter { name: "customType"; type: "QString" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QFrameGraphNode"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/FrameGraphNode 2.0",
            "Qt3D.Render/FrameGraphNode 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Method {
            name: "onParentChanged"
            Parameter { type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QFrontFace"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/FrontFace 2.0", "Qt3D.Render/FrontFace 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "WindingDirection"
            values: ["ClockWise", "CounterClockWise"]
        }
        Property {
            name: "direction"
            type: "WindingDirection"
            read: "direction"
            write: "setDirection"
            notify: "directionChanged"
            index: 0
        }
        Signal {
            name: "directionChanged"
            Parameter { name: "direction"; type: "WindingDirection" }
        }
        Method {
            name: "setDirection"
            Parameter { name: "direction"; type: "WindingDirection" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QFrustumCulling"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: [
            "Qt3D.Render/FrustumCulling 2.0",
            "Qt3D.Render/FrustumCulling 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QGeometryRenderer"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QBoundingVolume"
        exports: [
            "Qt3D.Render/GeometryRenderer 2.0",
            "Qt3D.Render/GeometryRenderer 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "PrimitiveType"
            values: [
                "Points",
                "Lines",
                "LineLoop",
                "LineStrip",
                "Triangles",
                "TriangleStrip",
                "TriangleFan",
                "LinesAdjacency",
                "TrianglesAdjacency",
                "LineStripAdjacency",
                "TriangleStripAdjacency",
                "Patches"
            ]
        }
        Property {
            name: "instanceCount"
            type: "int"
            read: "instanceCount"
            write: "setInstanceCount"
            notify: "instanceCountChanged"
            index: 0
        }
        Property {
            name: "vertexCount"
            type: "int"
            read: "vertexCount"
            write: "setVertexCount"
            notify: "vertexCountChanged"
            index: 1
        }
        Property {
            name: "indexOffset"
            type: "int"
            read: "indexOffset"
            write: "setIndexOffset"
            notify: "indexOffsetChanged"
            index: 2
        }
        Property {
            name: "firstInstance"
            type: "int"
            read: "firstInstance"
            write: "setFirstInstance"
            notify: "firstInstanceChanged"
            index: 3
        }
        Property {
            name: "firstVertex"
            type: "int"
            read: "firstVertex"
            write: "setFirstVertex"
            notify: "firstVertexChanged"
            index: 4
        }
        Property {
            name: "indexBufferByteOffset"
            type: "int"
            read: "indexBufferByteOffset"
            write: "setIndexBufferByteOffset"
            notify: "indexBufferByteOffsetChanged"
            index: 5
        }
        Property {
            name: "restartIndexValue"
            type: "int"
            read: "restartIndexValue"
            write: "setRestartIndexValue"
            notify: "restartIndexValueChanged"
            index: 6
        }
        Property {
            name: "verticesPerPatch"
            type: "int"
            read: "verticesPerPatch"
            write: "setVerticesPerPatch"
            notify: "verticesPerPatchChanged"
            index: 7
        }
        Property {
            name: "primitiveRestartEnabled"
            type: "bool"
            read: "primitiveRestartEnabled"
            write: "setPrimitiveRestartEnabled"
            notify: "primitiveRestartEnabledChanged"
            index: 8
        }
        Property {
            name: "geometry"
            type: "Qt3DCore::QGeometry"
            isPointer: true
            read: "geometry"
            write: "setGeometry"
            notify: "geometryChanged"
            index: 9
        }
        Property {
            name: "primitiveType"
            type: "PrimitiveType"
            read: "primitiveType"
            write: "setPrimitiveType"
            notify: "primitiveTypeChanged"
            index: 10
        }
        Property {
            name: "sortIndex"
            type: "float"
            read: "sortIndex"
            write: "setSortIndex"
            notify: "sortIndexChanged"
            index: 11
        }
        Signal {
            name: "instanceCountChanged"
            Parameter { name: "instanceCount"; type: "int" }
        }
        Signal {
            name: "vertexCountChanged"
            Parameter { name: "vertexCount"; type: "int" }
        }
        Signal {
            name: "indexOffsetChanged"
            Parameter { name: "indexOffset"; type: "int" }
        }
        Signal {
            name: "firstInstanceChanged"
            Parameter { name: "firstInstance"; type: "int" }
        }
        Signal {
            name: "firstVertexChanged"
            Parameter { name: "firstVertex"; type: "int" }
        }
        Signal {
            name: "indexBufferByteOffsetChanged"
            Parameter { name: "offset"; type: "int" }
        }
        Signal {
            name: "restartIndexValueChanged"
            Parameter { name: "restartIndexValue"; type: "int" }
        }
        Signal {
            name: "verticesPerPatchChanged"
            Parameter { name: "verticesPerPatch"; type: "int" }
        }
        Signal {
            name: "primitiveRestartEnabledChanged"
            Parameter { name: "primitiveRestartEnabled"; type: "bool" }
        }
        Signal {
            name: "geometryChanged"
            Parameter { name: "geometry"; type: "Qt3DCore::QGeometry"; isPointer: true }
        }
        Signal {
            name: "primitiveTypeChanged"
            Parameter { name: "primitiveType"; type: "PrimitiveType" }
        }
        Signal {
            name: "sortIndexChanged"
            Parameter { name: "sortIndex"; type: "float" }
        }
        Method {
            name: "setInstanceCount"
            Parameter { name: "instanceCount"; type: "int" }
        }
        Method {
            name: "setVertexCount"
            Parameter { name: "vertexCount"; type: "int" }
        }
        Method {
            name: "setIndexOffset"
            Parameter { name: "indexOffset"; type: "int" }
        }
        Method {
            name: "setFirstInstance"
            Parameter { name: "firstInstance"; type: "int" }
        }
        Method {
            name: "setFirstVertex"
            Parameter { name: "firstVertex"; type: "int" }
        }
        Method {
            name: "setIndexBufferByteOffset"
            Parameter { name: "offset"; type: "int" }
        }
        Method {
            name: "setRestartIndexValue"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "setVerticesPerPatch"
            Parameter { name: "verticesPerPatch"; type: "int" }
        }
        Method {
            name: "setPrimitiveRestartEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "geometry"; type: "Qt3DCore::QGeometry"; isPointer: true }
        }
        Method {
            name: "setPrimitiveType"
            Parameter { name: "primitiveType"; type: "PrimitiveType" }
        }
        Method {
            name: "setSortIndex"
            Parameter { name: "sortIndex"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QGraphicsApiFilter"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "Qt3D.Render/GraphicsApiFilter 2.0",
            "Qt3D.Render/GraphicsApiFilter 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "Api"
            values: ["OpenGLES", "OpenGL", "Vulkan", "DirectX", "RHI"]
        }
        Enum {
            name: "OpenGLProfile"
            values: ["NoProfile", "CoreProfile", "CompatibilityProfile"]
        }
        Property {
            name: "api"
            type: "Qt3DRender::QGraphicsApiFilter::Api"
            read: "api"
            write: "setApi"
            notify: "apiChanged"
            index: 0
        }
        Property {
            name: "profile"
            type: "Qt3DRender::QGraphicsApiFilter::OpenGLProfile"
            read: "profile"
            write: "setProfile"
            notify: "profileChanged"
            index: 1
        }
        Property {
            name: "minorVersion"
            type: "int"
            read: "minorVersion"
            write: "setMinorVersion"
            notify: "minorVersionChanged"
            index: 2
        }
        Property {
            name: "majorVersion"
            type: "int"
            read: "majorVersion"
            write: "setMajorVersion"
            notify: "majorVersionChanged"
            index: 3
        }
        Property {
            name: "extensions"
            type: "QStringList"
            read: "extensions"
            write: "setExtensions"
            notify: "extensionsChanged"
            index: 4
        }
        Property {
            name: "vendor"
            type: "QString"
            read: "vendor"
            write: "setVendor"
            notify: "vendorChanged"
            index: 5
        }
        Signal {
            name: "apiChanged"
            Parameter { name: "api"; type: "Qt3DRender::QGraphicsApiFilter::Api" }
        }
        Signal {
            name: "profileChanged"
            Parameter { name: "profile"; type: "Qt3DRender::QGraphicsApiFilter::OpenGLProfile" }
        }
        Signal {
            name: "minorVersionChanged"
            Parameter { name: "minorVersion"; type: "int" }
        }
        Signal {
            name: "majorVersionChanged"
            Parameter { name: "majorVersion"; type: "int" }
        }
        Signal {
            name: "extensionsChanged"
            Parameter { name: "extensions"; type: "QStringList" }
        }
        Signal {
            name: "vendorChanged"
            Parameter { name: "vendor"; type: "QString" }
        }
        Signal { name: "graphicsApiFilterChanged" }
        Method {
            name: "setApi"
            Parameter { name: "api"; type: "Api" }
        }
        Method {
            name: "setProfile"
            Parameter { name: "profile"; type: "OpenGLProfile" }
        }
        Method {
            name: "setMinorVersion"
            Parameter { name: "minorVersion"; type: "int" }
        }
        Method {
            name: "setMajorVersion"
            Parameter { name: "majorVersion"; type: "int" }
        }
        Method {
            name: "setExtensions"
            Parameter { name: "extensions"; type: "QStringList" }
        }
        Method {
            name: "setVendor"
            Parameter { name: "vendor"; type: "QString" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QLayerFilter"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        extension: "Qt3DRender::Render::Quick::Quick3DLayerFilter"
        exports: [
            "Qt3D.Render/LayerFilter 2.0",
            "Qt3D.Render/LayerFilter 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "FilterMode"
            values: [
                "AcceptAnyMatchingLayers",
                "AcceptAllMatchingLayers",
                "DiscardAnyMatchingLayers",
                "DiscardAllMatchingLayers"
            ]
        }
        Property {
            name: "filterMode"
            type: "FilterMode"
            read: "filterMode"
            write: "setFilterMode"
            notify: "filterModeChanged"
            index: 0
        }
        Signal {
            name: "filterModeChanged"
            Parameter { name: "filterMode"; type: "FilterMode" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QLayer"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Render/Layer 2.0", "Qt3D.Render/Layer 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "recursive"
            type: "bool"
            read: "recursive"
            write: "setRecursive"
            notify: "recursiveChanged"
            index: 0
        }
        Signal { name: "recursiveChanged" }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QLevelOfDetailBoundingSphere"
        accessSemantics: "value"
        exports: ["Qt3D.Render/levelOfDetailBoundingSphere 6.8"]
        isCreatable: false
        exportMetaObjectRevisions: [1544]
        Property {
            name: "center"
            type: "QVector3D"
            read: "center"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QLevelOfDetail"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Render/LevelOfDetail 2.9",
            "Qt3D.Render/LevelOfDetail 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Enum {
            name: "ThresholdType"
            values: [
                "DistanceToCameraThreshold",
                "ProjectedScreenPixelSizeThreshold"
            ]
        }
        Property {
            name: "camera"
            type: "Qt3DRender::QCamera"
            isPointer: true
            read: "camera"
            write: "setCamera"
            notify: "cameraChanged"
            index: 0
        }
        Property {
            name: "currentIndex"
            type: "int"
            read: "currentIndex"
            write: "setCurrentIndex"
            notify: "currentIndexChanged"
            index: 1
        }
        Property {
            name: "thresholdType"
            type: "ThresholdType"
            read: "thresholdType"
            write: "setThresholdType"
            notify: "thresholdTypeChanged"
            index: 2
        }
        Property {
            name: "thresholds"
            type: "double"
            isList: true
            read: "thresholds"
            write: "setThresholds"
            notify: "thresholdsChanged"
            index: 3
        }
        Property {
            name: "volumeOverride"
            type: "Qt3DRender::QLevelOfDetailBoundingSphere"
            read: "volumeOverride"
            write: "setVolumeOverride"
            notify: "volumeOverrideChanged"
            index: 4
        }
        Signal {
            name: "cameraChanged"
            Parameter { name: "camera"; type: "QCamera"; isPointer: true }
        }
        Signal {
            name: "currentIndexChanged"
            Parameter { name: "currentIndex"; type: "int" }
        }
        Signal {
            name: "thresholdTypeChanged"
            Parameter { name: "thresholdType"; type: "ThresholdType" }
        }
        Signal {
            name: "thresholdsChanged"
            Parameter { name: "thresholds"; type: "double"; isList: true }
        }
        Signal {
            name: "volumeOverrideChanged"
            Parameter { name: "volumeOverride"; type: "QLevelOfDetailBoundingSphere" }
        }
        Method {
            name: "setCamera"
            Parameter { name: "camera"; type: "QCamera"; isPointer: true }
        }
        Method {
            name: "setCurrentIndex"
            Parameter { name: "currentIndex"; type: "int" }
        }
        Method {
            name: "setThresholdType"
            Parameter { name: "thresholdType"; type: "ThresholdType" }
        }
        Method {
            name: "setThresholds"
            Parameter { name: "thresholds"; type: "double"; isList: true }
        }
        Method {
            name: "setVolumeOverride"
            Parameter { name: "volumeOverride"; type: "QLevelOfDetailBoundingSphere" }
        }
        Method {
            name: "createBoundingSphere"
            type: "Qt3DRender::QLevelOfDetailBoundingSphere"
            Parameter { name: "center"; type: "QVector3D" }
            Parameter { name: "radius"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QLevelOfDetailSwitch"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QLevelOfDetail"
        exports: [
            "Qt3D.Render/LevelOfDetailSwitch 2.9",
            "Qt3D.Render/LevelOfDetailSwitch 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QLineWidth"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/LineWidth 2.10", "Qt3D.Render/LineWidth 6.0"]
        exportMetaObjectRevisions: [522, 1536]
        Property {
            name: "value"
            type: "float"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 0
        }
        Property {
            name: "smooth"
            type: "bool"
            read: "smooth"
            write: "setSmooth"
            notify: "smoothChanged"
            index: 1
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "smoothChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setValue"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setSmooth"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        extension: "Qt3DRender::Render::Quick::Quick3DMaterial"
        exports: ["Qt3D.Render/Material 2.0", "Qt3D.Render/Material 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "effect"
            type: "Qt3DRender::QEffect"
            isPointer: true
            read: "effect"
            write: "setEffect"
            notify: "effectChanged"
            index: 0
        }
        Signal {
            name: "effectChanged"
            Parameter { name: "effect"; type: "QEffect"; isPointer: true }
        }
        Method {
            name: "setEffect"
            Parameter { name: "effect"; type: "QEffect"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QMemoryBarrier"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        extension: "Qt3DRender::Render::Quick::Quick3DMemoryBarrier"
        exports: [
            "Qt3D.Render/MemoryBarrier 2.9",
            "Qt3D.Render/MemoryBarrier 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Enum {
            name: "Operations"
            alias: "Operation"
            isFlag: true
            values: [
                "None",
                "VertexAttributeArray",
                "ElementArray",
                "Uniform",
                "TextureFetch",
                "ShaderImageAccess",
                "Command",
                "PixelBuffer",
                "TextureUpdate",
                "BufferUpdate",
                "FrameBuffer",
                "TransformFeedback",
                "AtomicCounter",
                "ShaderStorage",
                "QueryBuffer",
                "All"
            ]
        }
        Signal {
            name: "waitOperationsChanged"
            Parameter { name: "barrierTypes"; type: "QMemoryBarrier::Operations" }
        }
        Method {
            name: "setWaitOperations"
            Parameter { name: "operations"; type: "QMemoryBarrier::Operations" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QMesh"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Render/Mesh 2.0", "Qt3D.Render/Mesh 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "Status"
            values: ["None", "Loading", "Ready", "Error"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "meshName"
            type: "QString"
            read: "meshName"
            write: "setMeshName"
            notify: "meshNameChanged"
            index: 1
        }
        Property {
            name: "status"
            revision: 65291
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 2
            isReadonly: true
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
        Signal {
            name: "meshNameChanged"
            Parameter { name: "meshName"; type: "QString" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setMeshName"
            Parameter { name: "meshName"; type: "QString" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QMultiSampleAntiAliasing"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: [
            "Qt3D.Render/MultiSampleAntiAliasing 2.0",
            "Qt3D.Render/MultiSampleAntiAliasing 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QNoDepthMask"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: [
            "Qt3D.Render/NoDepthMask 2.0",
            "Qt3D.Render/NoDepthMask 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QNoDraw"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/NoDraw 2.0", "Qt3D.Render/NoDraw 6.0"]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QNoPicking"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/NoPicking 2.14", "Qt3D.Render/NoPicking 6.0"]
        exportMetaObjectRevisions: [526, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QObjectPicker"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Render/ObjectPicker 2.0",
            "Qt3D.Render/ObjectPicker 2.13",
            "Qt3D.Render/ObjectPicker 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
        Property {
            name: "hoverEnabled"
            type: "bool"
            read: "isHoverEnabled"
            write: "setHoverEnabled"
            notify: "hoverEnabledChanged"
            index: 0
        }
        Property {
            name: "dragEnabled"
            type: "bool"
            read: "isDragEnabled"
            write: "setDragEnabled"
            notify: "dragEnabledChanged"
            index: 1
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            notify: "pressedChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "containsMouse"
            type: "bool"
            read: "containsMouse"
            notify: "containsMouseChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "priority"
            revision: 525
            type: "int"
            read: "priority"
            write: "setPriority"
            notify: "priorityChanged"
            index: 4
        }
        Signal {
            name: "pressed"
            Parameter { name: "pick"; type: "Qt3DRender::QPickEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "pick"; type: "Qt3DRender::QPickEvent"; isPointer: true }
        }
        Signal {
            name: "clicked"
            Parameter { name: "pick"; type: "Qt3DRender::QPickEvent"; isPointer: true }
        }
        Signal {
            name: "moved"
            Parameter { name: "pick"; type: "Qt3DRender::QPickEvent"; isPointer: true }
        }
        Signal { name: "entered" }
        Signal { name: "exited" }
        Signal {
            name: "hoverEnabledChanged"
            Parameter { name: "hoverEnabled"; type: "bool" }
        }
        Signal {
            name: "dragEnabledChanged"
            Parameter { name: "dragEnabled"; type: "bool" }
        }
        Signal {
            name: "pressedChanged"
            Parameter { name: "pressed"; type: "bool" }
        }
        Signal {
            name: "containsMouseChanged"
            Parameter { name: "containsMouse"; type: "bool" }
        }
        Signal {
            name: "priorityChanged"
            revision: 525
            Parameter { name: "priority"; type: "int" }
        }
        Method {
            name: "setHoverEnabled"
            Parameter { name: "hoverEnabled"; type: "bool" }
        }
        Method {
            name: "setDragEnabled"
            Parameter { name: "dragEnabled"; type: "bool" }
        }
        Method {
            name: "setPriority"
            revision: 525
            Parameter { name: "priority"; type: "int" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QParameter"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Render/QParameter 2.0", "Qt3D.Render/QParameter 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 0
        }
        Property {
            name: "value"
            type: "QVariant"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 1
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "QVariant" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "setName"
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "setValue"
            Parameter { name: "dv"; type: "QVariant" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QPickEvent"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "Qt3D.Render/PickEvent 2.0",
            "Qt3D.Render/PickEvent 2.14",
            "Qt3D.Render/PickEvent 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 526, 1536]
        Enum {
            name: "Buttons"
            values: [
                "LeftButton",
                "RightButton",
                "MiddleButton",
                "BackButton",
                "NoButton"
            ]
        }
        Enum {
            name: "Modifiers"
            values: [
                "NoModifier",
                "ShiftModifier",
                "ControlModifier",
                "AltModifier",
                "MetaModifier",
                "KeypadModifier"
            ]
        }
        Property {
            name: "accepted"
            type: "bool"
            read: "isAccepted"
            write: "setAccepted"
            notify: "acceptedChanged"
            index: 0
        }
        Property {
            name: "position"
            type: "QPointF"
            read: "position"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "distance"
            type: "float"
            read: "distance"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "localIntersection"
            type: "QVector3D"
            read: "localIntersection"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "worldIntersection"
            type: "QVector3D"
            read: "worldIntersection"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "button"
            type: "Qt3DRender::QPickEvent::Buttons"
            read: "button"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "buttons"
            type: "int"
            read: "buttons"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "modifiers"
            type: "int"
            read: "modifiers"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "viewport"
            revision: 526
            type: "Qt3DRender::QViewport"
            isPointer: true
            read: "viewport"
            index: 8
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "entity"
            revision: 526
            type: "Qt3DCore::QEntity"
            isPointer: true
            read: "entity"
            index: 9
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "acceptedChanged"
            Parameter { name: "accepted"; type: "bool" }
        }
        Method {
            name: "setAccepted"
            Parameter { name: "accepted"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QPickingProxy"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QBoundingVolume"
        exports: [
            "Qt3D.Render/PickingProxy 2.16",
            "Qt3D.Render/PickingProxy 6.0"
        ]
        exportMetaObjectRevisions: [528, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QPickingSettings"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/PickingSettings 2.0",
            "Qt3D.Render/PickingSettings 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "PickMethod"
            values: [
                "BoundingVolumePicking",
                "TrianglePicking",
                "LinePicking",
                "PointPicking",
                "PrimitivePicking"
            ]
        }
        Enum {
            name: "PickResultMode"
            values: ["NearestPick", "AllPicks", "NearestPriorityPick"]
        }
        Enum {
            name: "FaceOrientationPickingMode"
            values: ["FrontFace", "BackFace", "FrontAndBackFace"]
        }
        Property {
            name: "pickMethod"
            type: "PickMethod"
            read: "pickMethod"
            write: "setPickMethod"
            notify: "pickMethodChanged"
            index: 0
        }
        Property {
            name: "pickResultMode"
            type: "PickResultMode"
            read: "pickResultMode"
            write: "setPickResultMode"
            notify: "pickResultModeChanged"
            index: 1
        }
        Property {
            name: "faceOrientationPickingMode"
            type: "FaceOrientationPickingMode"
            read: "faceOrientationPickingMode"
            write: "setFaceOrientationPickingMode"
            notify: "faceOrientationPickingModeChanged"
            index: 2
        }
        Property {
            name: "worldSpaceTolerance"
            revision: 65290
            type: "float"
            read: "worldSpaceTolerance"
            write: "setWorldSpaceTolerance"
            notify: "worldSpaceToleranceChanged"
            index: 3
        }
        Signal {
            name: "pickMethodChanged"
            Parameter { name: "pickMethod"; type: "QPickingSettings::PickMethod" }
        }
        Signal {
            name: "pickResultModeChanged"
            Parameter { name: "pickResult"; type: "QPickingSettings::PickResultMode" }
        }
        Signal {
            name: "faceOrientationPickingModeChanged"
            Parameter {
                name: "faceOrientationPickingMode"
                type: "QPickingSettings::FaceOrientationPickingMode"
            }
        }
        Signal {
            name: "worldSpaceToleranceChanged"
            Parameter { name: "worldSpaceTolerance"; type: "float" }
        }
        Method {
            name: "setPickMethod"
            Parameter { name: "pickMethod"; type: "PickMethod" }
        }
        Method {
            name: "setPickResultMode"
            Parameter { name: "pickResultMode"; type: "PickResultMode" }
        }
        Method {
            name: "setFaceOrientationPickingMode"
            Parameter { name: "faceOrientationPickingMode"; type: "FaceOrientationPickingMode" }
        }
        Method {
            name: "setWorldSpaceTolerance"
            Parameter { name: "worldSpaceTolerance"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QPointLight"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractLight"
        exports: ["Qt3D.Render/PointLight 2.0", "Qt3D.Render/PointLight 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "constantAttenuation"
            type: "float"
            read: "constantAttenuation"
            write: "setConstantAttenuation"
            notify: "constantAttenuationChanged"
            index: 0
        }
        Property {
            name: "linearAttenuation"
            type: "float"
            read: "linearAttenuation"
            write: "setLinearAttenuation"
            notify: "linearAttenuationChanged"
            index: 1
        }
        Property {
            name: "quadraticAttenuation"
            type: "float"
            read: "quadraticAttenuation"
            write: "setQuadraticAttenuation"
            notify: "quadraticAttenuationChanged"
            index: 2
        }
        Signal {
            name: "constantAttenuationChanged"
            Parameter { name: "constantAttenuation"; type: "float" }
        }
        Signal {
            name: "linearAttenuationChanged"
            Parameter { name: "linearAttenuation"; type: "float" }
        }
        Signal {
            name: "quadraticAttenuationChanged"
            Parameter { name: "quadraticAttenuation"; type: "float" }
        }
        Method {
            name: "setConstantAttenuation"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setLinearAttenuation"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setQuadraticAttenuation"
            Parameter { name: "value"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QPointSize"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/PointSize 2.0", "Qt3D.Render/PointSize 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "SizeMode"
            values: ["Fixed", "Programmable"]
        }
        Property {
            name: "sizeMode"
            type: "SizeMode"
            read: "sizeMode"
            write: "setSizeMode"
            notify: "sizeModeChanged"
            index: 0
        }
        Property {
            name: "value"
            type: "float"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 1
        }
        Signal {
            name: "sizeModeChanged"
            Parameter { name: "sizeMode"; type: "SizeMode" }
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setSizeMode"
            Parameter { name: "sizeMode"; type: "SizeMode" }
        }
        Method {
            name: "setValue"
            Parameter { name: "value"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QPolygonOffset"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: [
            "Qt3D.Render/PolygonOffset 2.0",
            "Qt3D.Render/PolygonOffset 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "scaleFactor"
            type: "float"
            read: "scaleFactor"
            write: "setScaleFactor"
            notify: "scaleFactorChanged"
            index: 0
        }
        Property {
            name: "depthSteps"
            type: "float"
            read: "depthSteps"
            write: "setDepthSteps"
            notify: "depthStepsChanged"
            index: 1
        }
        Signal {
            name: "scaleFactorChanged"
            Parameter { name: "scaleFactor"; type: "float" }
        }
        Signal {
            name: "depthStepsChanged"
            Parameter { name: "depthSteps"; type: "float" }
        }
        Method {
            name: "setScaleFactor"
            Parameter { name: "scaleFactor"; type: "float" }
        }
        Method {
            name: "setDepthSteps"
            Parameter { name: "depthSteps"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QProximityFilter"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: [
            "Qt3D.Render/ProximityFilter 2.10",
            "Qt3D.Render/ProximityFilter 6.0"
        ]
        exportMetaObjectRevisions: [522, 1536]
        Property {
            name: "entity"
            type: "Qt3DCore::QEntity"
            isPointer: true
            read: "entity"
            write: "setEntity"
            notify: "entityChanged"
            index: 0
        }
        Property {
            name: "distanceThreshold"
            type: "float"
            read: "distanceThreshold"
            write: "setDistanceThreshold"
            notify: "distanceThresholdChanged"
            index: 1
        }
        Signal {
            name: "entityChanged"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Signal {
            name: "distanceThresholdChanged"
            Parameter { name: "distanceThreshold"; type: "float" }
        }
        Method {
            name: "setEntity"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "setDistanceThreshold"
            Parameter { name: "distanceThreshold"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRasterMode"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: ["Qt3D.Render/RasterMode 2.13", "Qt3D.Render/RasterMode 6.0"]
        exportMetaObjectRevisions: [525, 1536]
        Enum {
            name: "RasterMode"
            values: ["Points", "Lines", "Fill"]
        }
        Enum {
            name: "FaceMode"
            values: ["Front", "Back", "FrontAndBack"]
        }
        Property {
            name: "rasterMode"
            type: "RasterMode"
            read: "rasterMode"
            write: "setRasterMode"
            notify: "rasterModeChanged"
            index: 0
        }
        Property {
            name: "faceMode"
            type: "FaceMode"
            read: "faceMode"
            write: "setFaceMode"
            notify: "faceModeChanged"
            index: 1
        }
        Signal {
            name: "rasterModeChanged"
            Parameter { name: "rasterMode"; type: "RasterMode" }
        }
        Signal {
            name: "faceModeChanged"
            Parameter { name: "faceMode"; type: "FaceMode" }
        }
        Method {
            name: "setRasterMode"
            Parameter { name: "rasterMode"; type: "RasterMode" }
        }
        Method {
            name: "setFaceMode"
            Parameter { name: "faceMode"; type: "FaceMode" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRenderCapabilities"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "Qt3D.Render/RenderCapabilities 2.15",
            "Qt3D.Render/RenderCapabilities 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [527, 1536]
        Enum {
            name: "API"
            values: ["OpenGL", "OpenGLES", "Vulkan", "DirectX", "RHI"]
        }
        Enum {
            name: "Profile"
            values: ["NoProfile", "CoreProfile", "CompatibilityProfile"]
        }
        Property {
            name: "valid"
            type: "bool"
            read: "isValid"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "api"
            type: "API"
            read: "api"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "profile"
            type: "Profile"
            read: "profile"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "majorVersion"
            type: "int"
            read: "majorVersion"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "minorVersion"
            type: "int"
            read: "minorVersion"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "extensions"
            type: "QStringList"
            read: "extensions"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "vendor"
            type: "QString"
            read: "vendor"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "renderer"
            type: "QString"
            read: "renderer"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "driverVersion"
            type: "QString"
            read: "driverVersion"
            index: 8
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "glslVersion"
            type: "QString"
            read: "glslVersion"
            index: 9
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxSamples"
            type: "int"
            read: "maxSamples"
            index: 10
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxTextureSize"
            type: "int"
            read: "maxTextureSize"
            index: 11
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxTextureUnits"
            type: "int"
            read: "maxTextureUnits"
            index: 12
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxTextureLayers"
            type: "int"
            read: "maxTextureLayers"
            index: 13
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "supportsUBO"
            type: "bool"
            read: "supportsUBO"
            index: 14
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxUBOSize"
            type: "int"
            read: "maxUBOSize"
            index: 15
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxUBOBindings"
            type: "int"
            read: "maxUBOBindings"
            index: 16
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "supportsSSBO"
            type: "bool"
            read: "supportsSSBO"
            index: 17
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxSSBOSize"
            type: "int"
            read: "maxSSBOSize"
            index: 18
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxSSBOBindings"
            type: "int"
            read: "maxSSBOBindings"
            index: 19
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "supportsImageStore"
            type: "bool"
            read: "supportsImageStore"
            index: 20
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxImageUnits"
            type: "int"
            read: "maxImageUnits"
            index: 21
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "supportsCompute"
            type: "bool"
            read: "supportsCompute"
            index: 22
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxWorkGroupCountX"
            type: "int"
            read: "maxWorkGroupCountX"
            index: 23
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxWorkGroupCountY"
            type: "int"
            read: "maxWorkGroupCountY"
            index: 24
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxWorkGroupCountZ"
            type: "int"
            read: "maxWorkGroupCountZ"
            index: 25
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxWorkGroupSizeX"
            type: "int"
            read: "maxWorkGroupSizeX"
            index: 26
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxWorkGroupSizeY"
            type: "int"
            read: "maxWorkGroupSizeY"
            index: 27
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxWorkGroupSizeZ"
            type: "int"
            read: "maxWorkGroupSizeZ"
            index: 28
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxComputeInvocations"
            type: "int"
            read: "maxComputeInvocations"
            index: 29
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxComputeSharedMemorySize"
            type: "int"
            read: "maxComputeSharedMemorySize"
            index: 30
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRenderCapture"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: [
            "Qt3D.Render/RenderCapture 2.1",
            "Qt3D.Render/RenderCapture 2.9",
            "Qt3D.Render/RenderCapture 2.10",
            "Qt3D.Render/RenderCapture 6.0"
        ]
        exportMetaObjectRevisions: [513, 521, 522, 1536]
        Method {
            name: "requestCapture"
            revision: 521
            type: "Qt3DRender::QRenderCaptureReply"
            isPointer: true
        }
        Method {
            name: "requestCapture"
            revision: 522
            type: "Qt3DRender::QRenderCaptureReply"
            isPointer: true
            Parameter { name: "rect"; type: "QRect" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRenderCaptureReply"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "Qt3D.Render/RenderCaptureReply 2.1",
            "Qt3D.Render/RenderCaptureReply 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [513, 1536]
        Property {
            name: "image"
            type: "QImage"
            read: "image"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "captureId"
            type: "int"
            read: "captureId"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "complete"
            type: "bool"
            read: "isComplete"
            notify: "completed"
            index: 2
            isReadonly: true
        }
        Signal { name: "completed" }
        Method {
            name: "saveImage"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "fileName"; type: "QString" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRenderPassFilter"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        extension: "Qt3DRender::Render::Quick::Quick3DRenderPassFilter"
        exports: [
            "Qt3D.Render/RenderPassFilter 2.0",
            "Qt3D.Render/RenderPassFilter 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRenderPass"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        extension: "Qt3DRender::Render::Quick::Quick3DRenderPass"
        exports: ["Qt3D.Render/RenderPass 2.0", "Qt3D.Render/RenderPass 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "shaderProgram"
            type: "Qt3DRender::QShaderProgram"
            isPointer: true
            read: "shaderProgram"
            write: "setShaderProgram"
            notify: "shaderProgramChanged"
            index: 0
        }
        Signal {
            name: "shaderProgramChanged"
            Parameter { name: "shaderProgram"; type: "QShaderProgram"; isPointer: true }
        }
        Method {
            name: "setShaderProgram"
            Parameter { name: "shaderProgram"; type: "QShaderProgram"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRenderSettings"
        accessSemantics: "reference"
        defaultProperty: "activeFrameGraph"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Render/RenderSettings 2.0",
            "Qt3D.Render/RenderSettings 2.15",
            "Qt3D.Render/RenderSettings 6.0"
        ]
        exportMetaObjectRevisions: [512, 527, 1536]
        Enum {
            name: "RenderPolicy"
            values: ["OnDemand", "Always"]
        }
        Property {
            name: "renderCapabilities"
            revision: 527
            type: "Qt3DRender::QRenderCapabilities"
            isPointer: true
            read: "renderCapabilities"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "pickingSettings"
            type: "Qt3DRender::QPickingSettings"
            isPointer: true
            read: "pickingSettings"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "renderPolicy"
            type: "RenderPolicy"
            read: "renderPolicy"
            write: "setRenderPolicy"
            notify: "renderPolicyChanged"
            index: 2
        }
        Property {
            name: "activeFrameGraph"
            type: "Qt3DRender::QFrameGraphNode"
            isPointer: true
            read: "activeFrameGraph"
            write: "setActiveFrameGraph"
            notify: "activeFrameGraphChanged"
            index: 3
        }
        Signal {
            name: "activeFrameGraphChanged"
            Parameter { name: "activeFrameGraph"; type: "QFrameGraphNode"; isPointer: true }
        }
        Signal {
            name: "renderPolicyChanged"
            Parameter { name: "renderPolicy"; type: "RenderPolicy" }
        }
        Method {
            name: "setActiveFrameGraph"
            Parameter { name: "activeFrameGraph"; type: "QFrameGraphNode"; isPointer: true }
        }
        Method {
            name: "setRenderPolicy"
            Parameter { name: "renderPolicy"; type: "RenderPolicy" }
        }
        Method {
            name: "_q_onPickingMethodChanged"
            Parameter { type: "QPickingSettings::PickMethod" }
        }
        Method {
            name: "_q_onPickResultModeChanged"
            Parameter { type: "QPickingSettings::PickResultMode" }
        }
        Method {
            name: "_q_onFaceOrientationPickingModeChanged"
            Parameter { type: "QPickingSettings::FaceOrientationPickingMode" }
        }
        Method {
            name: "_q_onWorldSpaceToleranceChanged"
            Parameter { type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRenderState"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/RenderState 2.0",
            "Qt3D.Render/RenderState 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRenderStateSet"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        extension: "Qt3DRender::Render::Quick::Quick3DStateSet"
        exports: [
            "Qt3D.Render/RenderStateSet 2.0",
            "Qt3D.Render/RenderStateSet 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRenderSurfaceSelector"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: [
            "Qt3D.Render/RenderSurfaceSelector 2.0",
            "Qt3D.Render/RenderSurfaceSelector 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "surface"
            type: "QObject"
            isPointer: true
            read: "surface"
            write: "setSurface"
            notify: "surfaceChanged"
            index: 0
        }
        Property {
            name: "externalRenderTargetSize"
            type: "QSize"
            read: "externalRenderTargetSize"
            write: "setExternalRenderTargetSize"
            notify: "externalRenderTargetSizeChanged"
            index: 1
        }
        Property {
            name: "surfacePixelRatio"
            type: "float"
            read: "surfacePixelRatio"
            write: "setSurfacePixelRatio"
            notify: "surfacePixelRatioChanged"
            index: 2
        }
        Signal {
            name: "surfaceChanged"
            Parameter { name: "surface"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "externalRenderTargetSizeChanged"
            Parameter { name: "size"; type: "QSize" }
        }
        Signal {
            name: "surfacePixelRatioChanged"
            Parameter { name: "ratio"; type: "float" }
        }
        Method {
            name: "setSurface"
            Parameter { name: "surfaceObject"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "setSurfacePixelRatio"
            Parameter { name: "ratio"; type: "float" }
        }
        Method {
            name: "setExternalRenderTargetSize"
            Parameter { name: "size"; type: "QSize" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRenderTarget"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        extension: "Qt3DRender::Render::Quick::Quick3DRenderTargetOutput"
        exports: [
            "Qt3D.Render/RenderTarget 2.0",
            "Qt3D.Render/RenderTarget 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRenderTargetOutput"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/RenderTargetOutput 2.0",
            "Qt3D.Render/RenderTargetOutput 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "AttachmentPoint"
            values: [
                "Color0",
                "Color1",
                "Color2",
                "Color3",
                "Color4",
                "Color5",
                "Color6",
                "Color7",
                "Color8",
                "Color9",
                "Color10",
                "Color11",
                "Color12",
                "Color13",
                "Color14",
                "Color15",
                "Depth",
                "Stencil",
                "DepthStencil",
                "Left",
                "Right"
            ]
        }
        Property {
            name: "attachmentPoint"
            type: "AttachmentPoint"
            read: "attachmentPoint"
            write: "setAttachmentPoint"
            notify: "attachmentPointChanged"
            index: 0
        }
        Property {
            name: "texture"
            type: "QAbstractTexture"
            isPointer: true
            read: "texture"
            write: "setTexture"
            notify: "textureChanged"
            index: 1
        }
        Property {
            name: "mipLevel"
            type: "int"
            read: "mipLevel"
            write: "setMipLevel"
            notify: "mipLevelChanged"
            index: 2
        }
        Property {
            name: "layer"
            type: "int"
            read: "layer"
            write: "setLayer"
            notify: "layerChanged"
            index: 3
        }
        Property {
            name: "face"
            type: "Qt3DRender::QAbstractTexture::CubeMapFace"
            read: "face"
            write: "setFace"
            notify: "faceChanged"
            index: 4
        }
        Signal {
            name: "attachmentPointChanged"
            Parameter { name: "attachmentPoint"; type: "AttachmentPoint" }
        }
        Signal {
            name: "textureChanged"
            Parameter { name: "texture"; type: "QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "mipLevelChanged"
            Parameter { name: "mipLevel"; type: "int" }
        }
        Signal {
            name: "layerChanged"
            Parameter { name: "layer"; type: "int" }
        }
        Signal {
            name: "faceChanged"
            Parameter { name: "face"; type: "QAbstractTexture::CubeMapFace" }
        }
        Method {
            name: "setAttachmentPoint"
            Parameter { name: "attachmentPoint"; type: "AttachmentPoint" }
        }
        Method {
            name: "setTexture"
            Parameter { name: "texture"; type: "QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setMipLevel"
            Parameter { name: "level"; type: "int" }
        }
        Method {
            name: "setLayer"
            Parameter { name: "layer"; type: "int" }
        }
        Method {
            name: "setFace"
            Parameter { name: "face"; type: "QAbstractTexture::CubeMapFace" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRenderTargetSelector"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: [
            "Qt3D.Render/RenderTargetSelector 2.0",
            "Qt3D.Render/RenderTargetSelector 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "target"
            type: "Qt3DRender::QRenderTarget"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 0
        }
        Signal {
            name: "targetChanged"
            Parameter { name: "target"; type: "QRenderTarget"; isPointer: true }
        }
        Method {
            name: "setTarget"
            Parameter { name: "target"; type: "QRenderTarget"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QSceneLoader"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        extension: "Qt3DRender::Render::Quick::Quick3DScene"
        exports: [
            "Qt3D.Render/SceneLoader 2.0",
            "Qt3D.Render/SceneLoader 2.9",
            "Qt3D.Render/SceneLoader 6.0"
        ]
        exportMetaObjectRevisions: [512, 521, 1536]
        Enum {
            name: "Status"
            values: ["None", "Loading", "Ready", "Error"]
        }
        Enum {
            name: "ComponentType"
            values: [
                "UnknownComponent",
                "GeometryRendererComponent",
                "TransformComponent",
                "MaterialComponent",
                "LightComponent",
                "CameraLensComponent"
            ]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 1
            isReadonly: true
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Method {
            name: "setSource"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method {
            name: "entity"
            revision: 521
            type: "Qt3DCore::QEntity"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "entityName"; type: "QString" }
        }
        Method { name: "entityNames"; revision: 521; type: "QStringList"; isMethodConstant: true }
        Method {
            name: "component"
            revision: 521
            type: "Qt3DCore::QComponent"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "entityName"; type: "QString" }
            Parameter { name: "componentType"; type: "ComponentType" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QScissorTest"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: [
            "Qt3D.Render/ScissorTest 2.0",
            "Qt3D.Render/ScissorTest 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "left"
            type: "int"
            read: "left"
            write: "setLeft"
            notify: "leftChanged"
            index: 0
        }
        Property {
            name: "bottom"
            type: "int"
            read: "bottom"
            write: "setBottom"
            notify: "bottomChanged"
            index: 1
        }
        Property {
            name: "width"
            type: "int"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 2
        }
        Property {
            name: "height"
            type: "int"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 3
        }
        Signal {
            name: "leftChanged"
            Parameter { name: "left"; type: "int" }
        }
        Signal {
            name: "bottomChanged"
            Parameter { name: "bottom"; type: "int" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "int" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "int" }
        }
        Method {
            name: "setLeft"
            Parameter { name: "left"; type: "int" }
        }
        Method {
            name: "setBottom"
            Parameter { name: "bottom"; type: "int" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "width"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "height"; type: "int" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QSeamlessCubemap"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: [
            "Qt3D.Render/SeamlessCubemap 2.0",
            "Qt3D.Render/SeamlessCubemap 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QSetFence"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/SetFence 2.13", "Qt3D.Render/SetFence 6.0"]
        exportMetaObjectRevisions: [525, 1536]
        Enum {
            name: "HandleType"
            values: ["NoHandle", "OpenGLFenceId"]
        }
        Property {
            name: "handleType"
            type: "HandleType"
            read: "handleType"
            notify: "handleTypeChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "handle"
            type: "QVariant"
            read: "handle"
            notify: "handleChanged"
            index: 1
            isReadonly: true
        }
        Signal {
            name: "handleTypeChanged"
            Parameter { name: "handleType"; type: "HandleType" }
        }
        Signal {
            name: "handleChanged"
            Parameter { name: "handle"; type: "QVariant" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QShaderData"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Render/QShaderData 2.0",
            "Qt3D.Render/QShaderData 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QShaderImage"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/ShaderImage 2.14",
            "Qt3D.Render/ShaderImage 6.0"
        ]
        exportMetaObjectRevisions: [526, 1536]
        Enum {
            name: "Access"
            values: ["ReadOnly", "WriteOnly", "ReadWrite"]
        }
        Enum {
            name: "ImageFormat"
            values: [
                "NoFormat",
                "Automatic",
                "R8_UNorm",
                "RG8_UNorm",
                "RGBA8_UNorm",
                "R16_UNorm",
                "RG16_UNorm",
                "RGBA16_UNorm",
                "R8_SNorm",
                "RG8_SNorm",
                "RGBA8_SNorm",
                "R16_SNorm",
                "RG16_SNorm",
                "RGBA16_SNorm",
                "R8U",
                "RG8U",
                "RGBA8U",
                "R16U",
                "RG16U",
                "RGBA16U",
                "R32U",
                "RG32U",
                "RGBA32U",
                "R8I",
                "RG8I",
                "RGBA8I",
                "R16I",
                "RG16I",
                "RGBA16I",
                "R32I",
                "RG32I",
                "RGBA32I",
                "R16F",
                "RG16F",
                "RGBA16F",
                "R32F",
                "RG32F",
                "RGBA32F",
                "RG11B10F",
                "RGB10A2",
                "RGB10A2U"
            ]
        }
        Property {
            name: "texture"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "texture"
            write: "setTexture"
            notify: "textureChanged"
            index: 0
        }
        Property {
            name: "layered"
            type: "bool"
            read: "layered"
            write: "setLayered"
            notify: "layeredChanged"
            index: 1
        }
        Property {
            name: "mipLevel"
            type: "int"
            read: "mipLevel"
            write: "setMipLevel"
            notify: "mipLevelChanged"
            index: 2
        }
        Property {
            name: "layer"
            type: "int"
            read: "layer"
            write: "setLayer"
            notify: "layerChanged"
            index: 3
        }
        Property {
            name: "access"
            type: "Access"
            read: "access"
            write: "setAccess"
            notify: "accessChanged"
            index: 4
        }
        Property {
            name: "format"
            type: "ImageFormat"
            read: "format"
            write: "setFormat"
            notify: "formatChanged"
            index: 5
        }
        Signal {
            name: "textureChanged"
            Parameter { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "layeredChanged"
            Parameter { name: "layered"; type: "bool" }
        }
        Signal {
            name: "mipLevelChanged"
            Parameter { name: "mipLevel"; type: "int" }
        }
        Signal {
            name: "layerChanged"
            Parameter { name: "layer"; type: "int" }
        }
        Signal {
            name: "accessChanged"
            Parameter { name: "access"; type: "Access" }
        }
        Signal {
            name: "formatChanged"
            Parameter { name: "format"; type: "ImageFormat" }
        }
        Method {
            name: "setTexture"
            Parameter { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setLayered"
            Parameter { name: "layered"; type: "bool" }
        }
        Method {
            name: "setMipLevel"
            Parameter { name: "mipLevel"; type: "int" }
        }
        Method {
            name: "setLayer"
            Parameter { name: "layer"; type: "int" }
        }
        Method {
            name: "setAccess"
            Parameter { name: "access"; type: "Access" }
        }
        Method {
            name: "setFormat"
            Parameter { name: "format"; type: "ImageFormat" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QShaderProgramBuilder"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/ShaderProgramBuilder 2.10",
            "Qt3D.Render/ShaderProgramBuilder 2.13",
            "Qt3D.Render/ShaderProgramBuilder 6.0"
        ]
        exportMetaObjectRevisions: [522, 525, 1536]
        Property {
            name: "shaderProgram"
            type: "Qt3DRender::QShaderProgram"
            isPointer: true
            read: "shaderProgram"
            write: "setShaderProgram"
            notify: "shaderProgramChanged"
            index: 0
        }
        Property {
            name: "enabledLayers"
            type: "QStringList"
            read: "enabledLayers"
            write: "setEnabledLayers"
            notify: "enabledLayersChanged"
            index: 1
        }
        Property {
            name: "vertexShaderGraph"
            type: "QUrl"
            read: "vertexShaderGraph"
            write: "setVertexShaderGraph"
            notify: "vertexShaderGraphChanged"
            index: 2
        }
        Property {
            name: "tessellationControlShaderGraph"
            type: "QUrl"
            read: "tessellationControlShaderGraph"
            write: "setTessellationControlShaderGraph"
            notify: "tessellationControlShaderGraphChanged"
            index: 3
        }
        Property {
            name: "tessellationEvaluationShaderGraph"
            type: "QUrl"
            read: "tessellationEvaluationShaderGraph"
            write: "setTessellationEvaluationShaderGraph"
            notify: "tessellationEvaluationShaderGraphChanged"
            index: 4
        }
        Property {
            name: "geometryShaderGraph"
            type: "QUrl"
            read: "geometryShaderGraph"
            write: "setGeometryShaderGraph"
            notify: "geometryShaderGraphChanged"
            index: 5
        }
        Property {
            name: "fragmentShaderGraph"
            type: "QUrl"
            read: "fragmentShaderGraph"
            write: "setFragmentShaderGraph"
            notify: "fragmentShaderGraphChanged"
            index: 6
        }
        Property {
            name: "computeShaderGraph"
            type: "QUrl"
            read: "computeShaderGraph"
            write: "setComputeShaderGraph"
            notify: "computeShaderGraphChanged"
            index: 7
        }
        Property {
            name: "vertexShaderCode"
            revision: 525
            type: "QByteArray"
            read: "vertexShaderCode"
            notify: "vertexShaderCodeChanged"
            index: 8
            isReadonly: true
        }
        Property {
            name: "tessellationControlShaderCode"
            revision: 525
            type: "QByteArray"
            read: "tessellationControlShaderCode"
            notify: "tessellationControlShaderCodeChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "tessellationEvaluationShaderCode"
            revision: 525
            type: "QByteArray"
            read: "tessellationEvaluationShaderCode"
            notify: "tessellationEvaluationShaderCodeChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "geometryShaderCode"
            revision: 525
            type: "QByteArray"
            read: "geometryShaderCode"
            notify: "geometryShaderCodeChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "fragmentShaderCode"
            revision: 525
            type: "QByteArray"
            read: "fragmentShaderCode"
            notify: "fragmentShaderCodeChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "computeShaderCode"
            revision: 525
            type: "QByteArray"
            read: "computeShaderCode"
            notify: "computeShaderCodeChanged"
            index: 13
            isReadonly: true
        }
        Signal {
            name: "shaderProgramChanged"
            Parameter { name: "shaderProgram"; type: "Qt3DRender::QShaderProgram"; isPointer: true }
        }
        Signal {
            name: "enabledLayersChanged"
            Parameter { name: "layers"; type: "QStringList" }
        }
        Signal {
            name: "vertexShaderGraphChanged"
            Parameter { name: "vertexShaderGraph"; type: "QUrl" }
        }
        Signal {
            name: "tessellationControlShaderGraphChanged"
            Parameter { name: "tessellationControlShaderGraph"; type: "QUrl" }
        }
        Signal {
            name: "tessellationEvaluationShaderGraphChanged"
            Parameter { name: "tessellationEvaluationShaderGraph"; type: "QUrl" }
        }
        Signal {
            name: "geometryShaderGraphChanged"
            Parameter { name: "geometryShaderGraph"; type: "QUrl" }
        }
        Signal {
            name: "fragmentShaderGraphChanged"
            Parameter { name: "fragmentShaderGraph"; type: "QUrl" }
        }
        Signal {
            name: "computeShaderGraphChanged"
            Parameter { name: "computeShaderGraph"; type: "QUrl" }
        }
        Signal {
            name: "vertexShaderCodeChanged"
            revision: 65293
            Parameter { name: "vertexShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "tessellationControlShaderCodeChanged"
            revision: 65293
            Parameter { name: "tessellationControlShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "tessellationEvaluationShaderCodeChanged"
            revision: 65293
            Parameter { name: "tessellationEvaluationShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "geometryShaderCodeChanged"
            revision: 65293
            Parameter { name: "geometryShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "fragmentShaderCodeChanged"
            revision: 65293
            Parameter { name: "fragmentShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "computeShaderCodeChanged"
            revision: 65293
            Parameter { name: "computeShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "setShaderProgram"
            Parameter { name: "program"; type: "Qt3DRender::QShaderProgram"; isPointer: true }
        }
        Method {
            name: "setEnabledLayers"
            Parameter { name: "layers"; type: "QStringList" }
        }
        Method {
            name: "setVertexShaderGraph"
            Parameter { name: "vertexShaderGraph"; type: "QUrl" }
        }
        Method {
            name: "setTessellationControlShaderGraph"
            Parameter { name: "tessellationControlShaderGraph"; type: "QUrl" }
        }
        Method {
            name: "setTessellationEvaluationShaderGraph"
            Parameter { name: "tessellationEvaluationShaderGraph"; type: "QUrl" }
        }
        Method {
            name: "setGeometryShaderGraph"
            Parameter { name: "geometryShaderGraph"; type: "QUrl" }
        }
        Method {
            name: "setFragmentShaderGraph"
            Parameter { name: "fragmentShaderGraph"; type: "QUrl" }
        }
        Method {
            name: "setComputeShaderGraph"
            Parameter { name: "computeShaderGraph"; type: "QUrl" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QShaderProgram"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/ShaderProgram 2.0",
            "Qt3D.Render/ShaderProgram 2.9",
            "Qt3D.Render/ShaderProgram 2.15",
            "Qt3D.Render/ShaderProgram 6.0"
        ]
        exportMetaObjectRevisions: [512, 521, 527, 1536]
        Enum {
            name: "ShaderType"
            values: [
                "Vertex",
                "Fragment",
                "TessellationControl",
                "TessellationEvaluation",
                "Geometry",
                "Compute"
            ]
        }
        Enum {
            name: "Status"
            values: ["NotReady", "Ready", "Error"]
        }
        Enum {
            name: "Format"
            values: ["GLSL", "SPIRV"]
        }
        Property {
            name: "vertexShaderCode"
            type: "QByteArray"
            read: "vertexShaderCode"
            write: "setVertexShaderCode"
            notify: "vertexShaderCodeChanged"
            index: 0
        }
        Property {
            name: "tessellationControlShaderCode"
            type: "QByteArray"
            read: "tessellationControlShaderCode"
            write: "setTessellationControlShaderCode"
            notify: "tessellationControlShaderCodeChanged"
            index: 1
        }
        Property {
            name: "tessellationEvaluationShaderCode"
            type: "QByteArray"
            read: "tessellationEvaluationShaderCode"
            write: "setTessellationEvaluationShaderCode"
            notify: "tessellationEvaluationShaderCodeChanged"
            index: 2
        }
        Property {
            name: "geometryShaderCode"
            type: "QByteArray"
            read: "geometryShaderCode"
            write: "setGeometryShaderCode"
            notify: "geometryShaderCodeChanged"
            index: 3
        }
        Property {
            name: "fragmentShaderCode"
            type: "QByteArray"
            read: "fragmentShaderCode"
            write: "setFragmentShaderCode"
            notify: "fragmentShaderCodeChanged"
            index: 4
        }
        Property {
            name: "computeShaderCode"
            type: "QByteArray"
            read: "computeShaderCode"
            write: "setComputeShaderCode"
            notify: "computeShaderCodeChanged"
            index: 5
        }
        Property {
            name: "log"
            revision: 521
            type: "QString"
            read: "log"
            notify: "logChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "status"
            revision: 521
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "format"
            revision: 527
            type: "Format"
            read: "format"
            write: "setFormat"
            notify: "formatChanged"
            index: 8
        }
        Signal {
            name: "vertexShaderCodeChanged"
            Parameter { name: "vertexShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "tessellationControlShaderCodeChanged"
            Parameter { name: "tessellationControlShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "tessellationEvaluationShaderCodeChanged"
            Parameter { name: "tessellationEvaluationShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "geometryShaderCodeChanged"
            Parameter { name: "geometryShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "fragmentShaderCodeChanged"
            Parameter { name: "fragmentShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "computeShaderCodeChanged"
            Parameter { name: "computeShaderCode"; type: "QByteArray" }
        }
        Signal {
            name: "logChanged"
            Parameter { name: "log"; type: "QString" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Signal {
            name: "formatChanged"
            Parameter { name: "format"; type: "Format" }
        }
        Method {
            name: "setVertexShaderCode"
            Parameter { name: "vertexShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "setTessellationControlShaderCode"
            Parameter { name: "tessellationControlShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "setTessellationEvaluationShaderCode"
            Parameter { name: "tessellationEvaluationShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "setGeometryShaderCode"
            Parameter { name: "geometryShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "setFragmentShaderCode"
            Parameter { name: "fragmentShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "setComputeShaderCode"
            Parameter { name: "computeShaderCode"; type: "QByteArray" }
        }
        Method {
            name: "loadSource"
            type: "QByteArray"
            Parameter { name: "sourceUrl"; type: "QUrl" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QSharedGLTexture"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        exports: [
            "Qt3D.Render/SharedGLTexture 2.13",
            "Qt3D.Render/SharedGLTexture 6.0"
        ]
        exportMetaObjectRevisions: [525, 1536]
        Property {
            name: "textureId"
            type: "int"
            read: "textureId"
            write: "setTextureId"
            notify: "textureIdChanged"
            index: 0
        }
        Signal {
            name: "textureIdChanged"
            Parameter { name: "textureId"; type: "int" }
        }
        Method {
            name: "setTextureId"
            Parameter { name: "id"; type: "int" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QSortPolicy"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/SortPolicy 2.0", "Qt3D.Render/SortPolicy 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "SortType"
            values: [
                "StateChangeCost",
                "BackToFront",
                "Material",
                "FrontToBack",
                "Texture",
                "Uniform"
            ]
        }
        Property {
            name: "sortTypes"
            type: "int"
            isList: true
            read: "sortTypesInt"
            write: "setSortTypes"
            notify: "sortTypesChanged"
            index: 0
        }
        Signal {
            name: "sortTypesChanged"
            Parameter { name: "sortTypes"; type: "SortType"; isList: true }
        }
        Signal {
            name: "sortTypesChanged"
            Parameter { name: "sortTypes"; type: "int"; isList: true }
        }
        Method {
            name: "setSortTypes"
            Parameter { name: "sortTypes"; type: "SortType"; isList: true }
        }
        Method {
            name: "setSortTypes"
            Parameter { name: "sortTypesInt"; type: "int"; isList: true }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QSpotLight"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractLight"
        exports: ["Qt3D.Render/SpotLight 2.0", "Qt3D.Render/SpotLight 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "constantAttenuation"
            type: "float"
            read: "constantAttenuation"
            write: "setConstantAttenuation"
            notify: "constantAttenuationChanged"
            index: 0
        }
        Property {
            name: "linearAttenuation"
            type: "float"
            read: "linearAttenuation"
            write: "setLinearAttenuation"
            notify: "linearAttenuationChanged"
            index: 1
        }
        Property {
            name: "quadraticAttenuation"
            type: "float"
            read: "quadraticAttenuation"
            write: "setQuadraticAttenuation"
            notify: "quadraticAttenuationChanged"
            index: 2
        }
        Property {
            name: "localDirection"
            type: "QVector3D"
            read: "localDirection"
            write: "setLocalDirection"
            notify: "localDirectionChanged"
            index: 3
        }
        Property {
            name: "cutOffAngle"
            type: "float"
            read: "cutOffAngle"
            write: "setCutOffAngle"
            notify: "cutOffAngleChanged"
            index: 4
        }
        Signal {
            name: "constantAttenuationChanged"
            Parameter { name: "constantAttenuation"; type: "float" }
        }
        Signal {
            name: "linearAttenuationChanged"
            Parameter { name: "linearAttenuation"; type: "float" }
        }
        Signal {
            name: "quadraticAttenuationChanged"
            Parameter { name: "quadraticAttenuation"; type: "float" }
        }
        Signal {
            name: "localDirectionChanged"
            Parameter { name: "localDirection"; type: "QVector3D" }
        }
        Signal {
            name: "cutOffAngleChanged"
            Parameter { name: "cutOffAngle"; type: "float" }
        }
        Method {
            name: "setConstantAttenuation"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setLinearAttenuation"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setQuadraticAttenuation"
            Parameter { name: "value"; type: "float" }
        }
        Method {
            name: "setLocalDirection"
            Parameter { name: "localDirection"; type: "QVector3D" }
        }
        Method {
            name: "setCutOffAngle"
            Parameter { name: "cutOffAngle"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QStencilMask"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: [
            "Qt3D.Render/StencilMask 2.0",
            "Qt3D.Render/StencilMask 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "frontOutputMask"
            type: "uint"
            read: "frontOutputMask"
            write: "setFrontOutputMask"
            notify: "frontOutputMaskChanged"
            index: 0
        }
        Property {
            name: "backOutputMask"
            type: "uint"
            read: "backOutputMask"
            write: "setBackOutputMask"
            notify: "backOutputMaskChanged"
            index: 1
        }
        Signal {
            name: "frontOutputMaskChanged"
            Parameter { name: "frontOutputMask"; type: "uint" }
        }
        Signal {
            name: "backOutputMaskChanged"
            Parameter { name: "backOutputMask"; type: "uint" }
        }
        Method {
            name: "setFrontOutputMask"
            Parameter { name: "frontOutputMask"; type: "uint" }
        }
        Method {
            name: "setBackOutputMask"
            Parameter { name: "backOutputMask"; type: "uint" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QStencilOperationArguments"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "Qt3D.Render/StencilOperationArguments 2.0",
            "Qt3D.Render/StencilOperationArguments 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "FaceMode"
            values: ["Front", "Back", "FrontAndBack"]
        }
        Enum {
            name: "Operation"
            values: [
                "Zero",
                "Keep",
                "Replace",
                "Increment",
                "Decrement",
                "IncrementWrap",
                "DecrementWrap",
                "Invert"
            ]
        }
        Property {
            name: "faceMode"
            type: "FaceMode"
            read: "faceMode"
            notify: "faceModeChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "stencilTestFailureOperation"
            type: "Operation"
            read: "stencilTestFailureOperation"
            write: "setStencilTestFailureOperation"
            notify: "stencilTestFailureOperationChanged"
            index: 1
        }
        Property {
            name: "depthTestFailureOperation"
            type: "Operation"
            read: "depthTestFailureOperation"
            write: "setDepthTestFailureOperation"
            notify: "depthTestFailureOperationChanged"
            index: 2
        }
        Property {
            name: "allTestsPassOperation"
            type: "Operation"
            read: "allTestsPassOperation"
            write: "setAllTestsPassOperation"
            notify: "allTestsPassOperationChanged"
            index: 3
        }
        Signal {
            name: "stencilTestFailureOperationChanged"
            Parameter { name: "stencilFail"; type: "Operation" }
        }
        Signal {
            name: "depthTestFailureOperationChanged"
            Parameter { name: "depthFail"; type: "Operation" }
        }
        Signal {
            name: "allTestsPassOperationChanged"
            Parameter { name: "stencilDepthPass"; type: "Operation" }
        }
        Signal {
            name: "faceModeChanged"
            Parameter { name: "faceMode"; type: "FaceMode" }
        }
        Method {
            name: "setStencilTestFailureOperation"
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "setDepthTestFailureOperation"
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "setAllTestsPassOperation"
            Parameter { name: "operation"; type: "Operation" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QStencilOperation"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: [
            "Qt3D.Render/StencilOperation 2.0",
            "Qt3D.Render/StencilOperation 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "front"
            type: "Qt3DRender::QStencilOperationArguments"
            isPointer: true
            read: "front"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "back"
            type: "Qt3DRender::QStencilOperationArguments"
            isPointer: true
            read: "back"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QStencilTestArguments"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "Qt3D.Render/StencilTestArguments 2.0",
            "Qt3D.Render/StencilTestArguments 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "StencilFaceMode"
            values: ["Front", "Back", "FrontAndBack"]
        }
        Enum {
            name: "StencilFunction"
            values: [
                "Never",
                "Always",
                "Less",
                "LessOrEqual",
                "Equal",
                "GreaterOrEqual",
                "Greater",
                "NotEqual"
            ]
        }
        Property {
            name: "faceMode"
            type: "StencilFaceMode"
            read: "faceMode"
            notify: "faceModeChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "comparisonMask"
            type: "uint"
            read: "comparisonMask"
            write: "setComparisonMask"
            notify: "comparisonMaskChanged"
            index: 1
        }
        Property {
            name: "referenceValue"
            type: "int"
            read: "referenceValue"
            write: "setReferenceValue"
            notify: "referenceValueChanged"
            index: 2
        }
        Property {
            name: "stencilFunction"
            type: "StencilFunction"
            read: "stencilFunction"
            write: "setStencilFunction"
            notify: "stencilFunctionChanged"
            index: 3
        }
        Signal {
            name: "comparisonMaskChanged"
            Parameter { name: "comparisonMask"; type: "uint" }
        }
        Signal {
            name: "stencilFunctionChanged"
            Parameter { name: "stencilFunction"; type: "StencilFunction" }
        }
        Signal {
            name: "referenceValueChanged"
            Parameter { name: "referenceValue"; type: "int" }
        }
        Signal {
            name: "faceModeChanged"
            Parameter { name: "faceMode"; type: "StencilFaceMode" }
        }
        Method {
            name: "setComparisonMask"
            Parameter { name: "comparisonMask"; type: "uint" }
        }
        Method {
            name: "setReferenceValue"
            Parameter { name: "referenceValue"; type: "int" }
        }
        Method {
            name: "setStencilFunction"
            Parameter { name: "stencilFunction"; type: "StencilFunction" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QStencilTest"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRenderState"
        exports: [
            "Qt3D.Render/StencilTest 2.0",
            "Qt3D.Render/StencilTest 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "front"
            type: "Qt3DRender::QStencilTestArguments"
            isPointer: true
            read: "front"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "back"
            type: "Qt3DRender::QStencilTestArguments"
            isPointer: true
            read: "back"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QSubtreeEnabler"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: [
            "Qt3D.Render/SubtreeEnabler 2.14",
            "Qt3D.Render/SubtreeEnabler 6.0"
        ]
        exportMetaObjectRevisions: [526, 1536]
        Enum {
            name: "Enablement"
            values: ["Persistent", "SingleShot"]
        }
        Property {
            name: "enablement"
            type: "Enablement"
            read: "enablement"
            write: "setEnablement"
            notify: "enablementChanged"
            index: 0
        }
        Signal {
            name: "enablementChanged"
            Parameter { name: "enablement"; type: "Qt3DRender::QSubtreeEnabler::Enablement" }
        }
        Method { name: "requestUpdate" }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTechniqueFilter"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        extension: "Qt3DRender::Render::Quick::Quick3DTechniqueFilter"
        exports: [
            "Qt3D.Render/TechniqueFilter 2.0",
            "Qt3D.Render/TechniqueFilter 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTechnique"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        extension: "Qt3DRender::Render::Quick::Quick3DTechnique"
        exports: ["Qt3D.Render/Technique 2.0", "Qt3D.Render/Technique 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "graphicsApiFilter"
            type: "Qt3DRender::QGraphicsApiFilter"
            isPointer: true
            read: "graphicsApiFilter"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Method { name: "_q_graphicsApiFilterChanged" }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTexture1DArray"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        extension: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        exports: [
            "Qt3D.Render/Texture1DArray 2.0",
            "Qt3D.Render/Texture1DArray 2.13",
            "Qt3D.Render/Texture1DArray 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTexture1D"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        extension: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        exports: [
            "Qt3D.Render/Texture1D 2.0",
            "Qt3D.Render/Texture1D 2.13",
            "Qt3D.Render/Texture1D 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTexture2DArray"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        extension: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        exports: [
            "Qt3D.Render/Texture2DArray 2.0",
            "Qt3D.Render/Texture2DArray 2.13",
            "Qt3D.Render/Texture2DArray 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTexture2D"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        extension: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        exports: [
            "Qt3D.Render/Texture2D 2.0",
            "Qt3D.Render/Texture2D 2.13",
            "Qt3D.Render/Texture2D 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTexture2DMultisampleArray"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        extension: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        exports: [
            "Qt3D.Render/Texture2DMultisampleArray 2.0",
            "Qt3D.Render/Texture2DMultisampleArray 2.13",
            "Qt3D.Render/Texture2DMultisampleArray 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTexture2DMultisample"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        extension: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        exports: [
            "Qt3D.Render/Texture2DMultisample 2.0",
            "Qt3D.Render/Texture2DMultisample 2.13",
            "Qt3D.Render/Texture2DMultisample 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTexture3D"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        extension: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        exports: [
            "Qt3D.Render/Texture3D 2.0",
            "Qt3D.Render/Texture3D 2.13",
            "Qt3D.Render/Texture3D 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTextureBuffer"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        extension: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        exports: [
            "Qt3D.Render/TextureBuffer 2.0",
            "Qt3D.Render/TextureBuffer 2.13",
            "Qt3D.Render/TextureBuffer 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTextureCubeMapArray"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        extension: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        exports: [
            "Qt3D.Render/TextureCubeMapArray 2.0",
            "Qt3D.Render/TextureCubeMapArray 2.13",
            "Qt3D.Render/TextureCubeMapArray 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTextureCubeMap"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        extension: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        exports: [
            "Qt3D.Render/TextureCubeMap 2.0",
            "Qt3D.Render/TextureCubeMap 2.13",
            "Qt3D.Render/TextureCubeMap 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTextureImage"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTextureImage"
        exports: [
            "Qt3D.Render/TextureImage 2.0",
            "Qt3D.Render/TextureImage 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "Status"
            values: ["None", "Loading", "Ready", "Error"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "mirrored"
            type: "bool"
            read: "isMirrored"
            write: "setMirrored"
            notify: "mirroredChanged"
            index: 2
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Signal {
            name: "mirroredChanged"
            Parameter { name: "mirrored"; type: "bool" }
        }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setMirrored"
            Parameter { name: "mirrored"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTextureLoader"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        extension: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        exports: [
            "Qt3D.Render/TextureLoader 2.0",
            "Qt3D.Render/TextureLoader 2.13",
            "Qt3D.Render/TextureLoader 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "mirrored"
            type: "bool"
            read: "isMirrored"
            write: "setMirrored"
            notify: "mirroredChanged"
            index: 1
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
        Signal {
            name: "mirroredChanged"
            Parameter { name: "mirrored"; type: "bool" }
        }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setMirrored"
            Parameter { name: "mirrored"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTextureRectangle"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractTexture"
        extension: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        exports: [
            "Qt3D.Render/TextureRectangle 2.0",
            "Qt3D.Render/TextureRectangle 2.13",
            "Qt3D.Render/TextureRectangle 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QTextureWrapMode"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["Qt3D.Render/WrapMode 2.0", "Qt3D.Render/WrapMode 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "WrapMode"
            values: [
                "Repeat",
                "MirroredRepeat",
                "ClampToEdge",
                "ClampToBorder"
            ]
        }
        Property { name: "x"; type: "WrapMode"; read: "x"; write: "setX"; notify: "xChanged"; index: 0 }
        Property { name: "y"; type: "WrapMode"; read: "y"; write: "setY"; notify: "yChanged"; index: 1 }
        Property { name: "z"; type: "WrapMode"; read: "z"; write: "setZ"; notify: "zChanged"; index: 2 }
        Signal {
            name: "xChanged"
            Parameter { name: "x"; type: "WrapMode" }
        }
        Signal {
            name: "yChanged"
            Parameter { name: "y"; type: "WrapMode" }
        }
        Signal {
            name: "zChanged"
            Parameter { name: "z"; type: "WrapMode" }
        }
        Method {
            name: "setX"
            Parameter { name: "x"; type: "WrapMode" }
        }
        Method {
            name: "setY"
            Parameter { name: "y"; type: "WrapMode" }
        }
        Method {
            name: "setZ"
            Parameter { name: "z"; type: "WrapMode" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QViewport"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        extension: "Qt3DRender::Render::Quick::Quick3DViewport"
        exports: [
            "Qt3D.Render/Viewport 2.0",
            "Qt3D.Render/Viewport 2.9",
            "Qt3D.Render/Viewport 6.0"
        ]
        exportMetaObjectRevisions: [512, 521, 1536]
        Property {
            name: "normalizedRect"
            type: "QRectF"
            read: "normalizedRect"
            write: "setNormalizedRect"
            notify: "normalizedRectChanged"
            index: 0
        }
        Property {
            name: "gamma"
            revision: 521
            type: "float"
            read: "gamma"
            write: "setGamma"
            notify: "gammaChanged"
            index: 1
        }
        Signal {
            name: "normalizedRectChanged"
            Parameter { name: "normalizedRect"; type: "QRectF" }
        }
        Signal {
            name: "gammaChanged"
            Parameter { name: "gamma"; type: "float" }
        }
        Method {
            name: "setNormalizedRect"
            Parameter { name: "normalizedRect"; type: "QRectF" }
        }
        Method {
            name: "setGamma"
            Parameter { name: "gamma"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QWaitFence"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
        exports: ["Qt3D.Render/WaitFence 2.13", "Qt3D.Render/WaitFence 6.0"]
        exportMetaObjectRevisions: [525, 1536]
        Enum {
            name: "HandleType"
            values: ["NoHandle", "OpenGLFenceId"]
        }
        Property {
            name: "handleType"
            type: "HandleType"
            read: "handleType"
            write: "setHandleType"
            notify: "handleTypeChanged"
            index: 0
        }
        Property {
            name: "handle"
            type: "QVariant"
            read: "handle"
            write: "setHandle"
            notify: "handleChanged"
            index: 1
        }
        Property {
            name: "waitOnCPU"
            type: "bool"
            read: "waitOnCPU"
            write: "setWaitOnCPU"
            notify: "waitOnCPUChanged"
            index: 2
        }
        Property {
            name: "timeout"
            type: "qulonglong"
            read: "timeout"
            write: "setTimeout"
            notify: "timeoutChanged"
            index: 3
        }
        Signal {
            name: "waitOnCPUChanged"
            Parameter { name: "waitOnCPU"; type: "bool" }
        }
        Signal {
            name: "timeoutChanged"
            Parameter { name: "timeoutChanged"; type: "qulonglong" }
        }
        Signal {
            name: "handleTypeChanged"
            Parameter { name: "handleType"; type: "HandleType" }
        }
        Signal {
            name: "handleChanged"
            Parameter { name: "handle"; type: "QVariant" }
        }
    }
    Component {
        file: "qabstractraycaster.h"
        name: "Qt3DRender::QAbstractRayCaster"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        Enum {
            name: "RunMode"
            values: ["Continuous", "SingleShot"]
        }
        Enum {
            name: "FilterMode"
            values: [
                "AcceptAnyMatchingLayers",
                "AcceptAllMatchingLayers",
                "DiscardAnyMatchingLayers",
                "DiscardAllMatchingLayers"
            ]
        }
        Property {
            name: "runMode"
            type: "Qt3DRender::QAbstractRayCaster::RunMode"
            read: "runMode"
            write: "setRunMode"
            notify: "runModeChanged"
            index: 0
        }
        Property {
            name: "filterMode"
            type: "Qt3DRender::QAbstractRayCaster::FilterMode"
            read: "filterMode"
            write: "setFilterMode"
            notify: "filterModeChanged"
            index: 1
        }
        Property {
            name: "hits"
            type: "Qt3DRender::QAbstractRayCaster::Hits"
            read: "hits"
            notify: "hitsChanged"
            index: 2
            isReadonly: true
        }
        Signal {
            name: "runModeChanged"
            Parameter { name: "runMode"; type: "Qt3DRender::QAbstractRayCaster::RunMode" }
        }
        Signal {
            name: "hitsChanged"
            Parameter { name: "hits"; type: "Qt3DRender::QAbstractRayCaster::Hits" }
        }
        Signal {
            name: "filterModeChanged"
            Parameter { name: "filterMode"; type: "Qt3DRender::QAbstractRayCaster::FilterMode" }
        }
        Method {
            name: "setRunMode"
            Parameter { name: "runMode"; type: "RunMode" }
        }
        Method {
            name: "setFilterMode"
            Parameter { name: "filterMode"; type: "FilterMode" }
        }
    }
    Component {
        file: "qraycaster.h"
        name: "Qt3DRender::QRayCaster"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractRayCaster"
        Property {
            name: "origin"
            type: "QVector3D"
            read: "origin"
            write: "setOrigin"
            notify: "originChanged"
            index: 0
        }
        Property {
            name: "direction"
            type: "QVector3D"
            read: "direction"
            write: "setDirection"
            notify: "directionChanged"
            index: 1
        }
        Property {
            name: "length"
            type: "float"
            read: "length"
            write: "setLength"
            notify: "lengthChanged"
            index: 2
        }
        Signal {
            name: "originChanged"
            Parameter { name: "origin"; type: "QVector3D" }
        }
        Signal {
            name: "directionChanged"
            Parameter { name: "direction"; type: "QVector3D" }
        }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setOrigin"
            Parameter { name: "origin"; type: "QVector3D" }
        }
        Method {
            name: "setDirection"
            Parameter { name: "direction"; type: "QVector3D" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
        Method { name: "trigger" }
        Method {
            name: "trigger"
            Parameter { name: "origin"; type: "QVector3D" }
            Parameter { name: "direction"; type: "QVector3D" }
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "pick"
            type: "Qt3DRender::QAbstractRayCaster::Hits"
            Parameter { name: "origin"; type: "QVector3D" }
            Parameter { name: "direction"; type: "QVector3D" }
            Parameter { name: "length"; type: "float" }
        }
    }
    Component {
        file: "qscreenraycaster.h"
        name: "Qt3DRender::QScreenRayCaster"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QAbstractRayCaster"
        Property {
            name: "position"
            type: "QPoint"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 0
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "QPoint" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "QPoint" }
        }
        Method { name: "trigger" }
        Method {
            name: "trigger"
            Parameter { name: "position"; type: "QPoint" }
        }
        Method {
            name: "pick"
            type: "Qt3DRender::QAbstractRayCaster::Hits"
            Parameter { name: "position"; type: "QPoint" }
        }
    }
    Component {
        file: "private/quick3deffect_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DEffect"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "techniques"
            type: "Qt3DRender::QTechnique"
            isList: true
            read: "techniqueList"
            index: 0
            isReadonly: true
        }
        Property {
            name: "parameters"
            type: "Qt3DRender::QParameter"
            isList: true
            read: "parameterList"
            index: 1
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dlayerfilter_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DLayerFilter"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "layers"
            type: "Qt3DRender::QLayer"
            isList: true
            read: "qmlLayers"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dmaterial_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DMaterial"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "parameters"
            type: "Qt3DRender::QParameter"
            isList: true
            read: "qmlParameters"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dmemorybarrier_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DMemoryBarrier"
        accessSemantics: "reference"
        prototype: "QObject"
        Property { name: "waitFor"; type: "int"; read: "waitFor"; write: "setWaitFor"; index: 0 }
    }
    Component {
        file: "private/quick3dparameter_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DParameter"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QParameter"
        exports: ["Qt3D.Render/Parameter 2.0", "Qt3D.Render/Parameter 6.0"]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/quick3draycaster_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DRayCaster"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QRayCaster"
        exports: ["Qt3D.Render/RayCaster 2.11", "Qt3D.Render/RayCaster 6.0"]
        exportMetaObjectRevisions: [523, 1536]
        Property {
            name: "layers"
            type: "Qt3DRender::QLayer"
            isList: true
            read: "qmlLayers"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3drenderpass_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DRenderPass"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "filterKeys"
            type: "Qt3DRender::QFilterKey"
            isList: true
            read: "filterKeyList"
            index: 0
            isReadonly: true
        }
        Property {
            name: "renderStates"
            type: "Qt3DRender::QRenderState"
            isList: true
            read: "renderStateList"
            index: 1
            isReadonly: true
        }
        Property {
            name: "parameters"
            type: "Qt3DRender::QParameter"
            isList: true
            read: "parameterList"
            index: 2
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3drenderpassfilter_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DRenderPassFilter"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "matchAny"
            type: "Qt3DRender::QFilterKey"
            isList: true
            read: "includeList"
            index: 0
            isReadonly: true
        }
        Property {
            name: "parameters"
            type: "Qt3DRender::QParameter"
            isList: true
            read: "parameterList"
            index: 1
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3drendertargetoutput_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DRenderTargetOutput"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "attachments"
            type: "Qt3DRender::QRenderTargetOutput"
            isList: true
            read: "qmlAttachments"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dscene_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DScene"
        accessSemantics: "reference"
        prototype: "QObject"
    }
    Component {
        file: "private/quick3dscreenraycaster_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DScreenRayCaster"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QScreenRayCaster"
        exports: [
            "Qt3D.Render/ScreenRayCaster 2.11",
            "Qt3D.Render/ScreenRayCaster 6.0"
        ]
        exportMetaObjectRevisions: [523, 1536]
        Property {
            name: "layers"
            type: "Qt3DRender::QLayer"
            isList: true
            read: "qmlLayers"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dshaderdata_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DShaderData"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QShaderData"
        exports: ["Qt3D.Render/ShaderData 2.0", "Qt3D.Render/ShaderData 6.0"]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/quick3dshaderdataarray_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DShaderDataArray"
        accessSemantics: "reference"
        defaultProperty: "values"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Render/ShaderDataArray 2.0",
            "Qt3D.Render/ShaderDataArray 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "values"
            type: "Qt3DRender::QShaderData"
            isList: true
            read: "valuesList"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dstateset_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DStateSet"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "renderStates"
            type: "Qt3DRender::QRenderState"
            isList: true
            read: "renderStateList"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/quick3dtechnique_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DTechnique"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "filterKeys"
            type: "Qt3DRender::QFilterKey"
            isList: true
            read: "filterKeyList"
            index: 0
            isReadonly: true
        }
        Property {
            name: "renderPasses"
            type: "Qt3DRender::QRenderPass"
            isList: true
            read: "renderPassList"
            index: 1
            isReadonly: true
        }
        Property {
            name: "parameters"
            type: "Qt3DRender::QParameter"
            isList: true
            read: "parameterList"
            index: 2
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dtechniquefilter_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DTechniqueFilter"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "matchAll"
            type: "Qt3DRender::QFilterKey"
            isList: true
            read: "matchList"
            index: 0
            isReadonly: true
        }
        Property {
            name: "parameters"
            type: "Qt3DRender::QParameter"
            isList: true
            read: "parameterList"
            index: 1
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dtexture_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DTextureExtension"
        accessSemantics: "reference"
        defaultProperty: "textureImages"
        prototype: "QObject"
        Property {
            name: "textureImages"
            type: "Qt3DRender::QAbstractTextureImage"
            isList: true
            read: "textureImages"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dviewport_p.h"
        name: "Qt3DRender::Render::Quick::Quick3DViewport"
        accessSemantics: "reference"
        prototype: "QObject"
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QRayCasterHit"
        accessSemantics: "value"
        Enum {
            name: "HitType"
            values: ["TriangleHit", "LineHit", "PointHit", "EntityHit"]
        }
        Property {
            name: "type"
            type: "QRayCasterHit::HitType"
            read: "type"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "entityId"
            type: "Qt3DCore::QNodeId"
            read: "entityId"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "entity"
            type: "Qt3DCore::QEntity"
            isPointer: true
            read: "entity"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "distance"
            type: "float"
            read: "distance"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "localIntersection"
            type: "QVector3D"
            read: "localIntersection"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "worldIntersection"
            type: "QVector3D"
            read: "worldIntersection"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "primitiveIndex"
            type: "uint"
            read: "primitiveIndex"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "vertex1Index"
            type: "uint"
            read: "vertex1Index"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "vertex2Index"
            type: "uint"
            read: "vertex2Index"
            index: 8
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "vertex3Index"
            type: "uint"
            read: "vertex3Index"
            index: 9
            isReadonly: true
            isPropertyConstant: true
        }
        Method { name: "toString"; type: "QString" }
    }
    Component {
        file: "private/qt3dquick3drenderforeign_p.h"
        name: "Qt3DRender::QAbstractRayCaster::Hits"
        accessSemantics: "sequence"
        valueType: "Qt3DRender::QRayCasterHit"
    }
}
