#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple GUI Test for Gait Detection System
"""

import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QLabel, QFrame, QProgressBar)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

class SimpleProgressBar(QProgressBar):
    """Simple gradient progress bar"""
    
    def __init__(self):
        super().__init__()
        self.setMinimumHeight(25)
        self.setStyleSheet("""
            QProgressBar {
                border: 2px solid #BDC3C7;
                border-radius: 12px;
                background-color: #ECF0F1;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                border-radius: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                           stop:0 #E74C3C, stop:0.3 #F39C12, 
                                           stop:0.7 #F1C40F, stop:1 #27AE60);
            }
        """)

class SimpleMainWindow(QMainWindow):
    """Simple main window for testing"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize UI"""
        self.setWindowTitle("Gait Detection System - Simple Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Title
        title = QLabel("Gait Detection System")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setStyleSheet("color: #2C3E50; padding: 20px;")
        main_layout.addWidget(title)
        
        # Status
        self.status_label = QLabel("System Ready")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Arial", 14))
        self.status_label.setStyleSheet("color: #27AE60; padding: 10px;")
        main_layout.addWidget(self.status_label)
        
        # Progress bars frame
        progress_frame = QFrame()
        progress_frame.setFrameStyle(QFrame.Box)
        progress_frame.setStyleSheet("background-color: white; border: 2px solid #3498DB; border-radius: 10px; padding: 20px;")
        progress_layout = QVBoxLayout(progress_frame)
        
        # Angle progress bar
        angle_label = QLabel("Angle Score")
        angle_label.setFont(QFont("Arial", 12, QFont.Bold))
        angle_label.setStyleSheet("color: #2C3E50;")
        progress_layout.addWidget(angle_label)
        
        self.angle_progress = SimpleProgressBar()
        self.angle_progress.setRange(0, 100)
        self.angle_progress.setValue(75)  # Test value
        progress_layout.addWidget(self.angle_progress)
        
        angle_value = QLabel("75 / 100")
        angle_value.setAlignment(Qt.AlignCenter)
        angle_value.setStyleSheet("color: #2C3E50;")
        progress_layout.addWidget(angle_value)
        
        progress_layout.addSpacing(20)
        
        # Speed progress bar
        speed_label = QLabel("Speed Score")
        speed_label.setFont(QFont("Arial", 12, QFont.Bold))
        speed_label.setStyleSheet("color: #2C3E50;")
        progress_layout.addWidget(speed_label)
        
        self.speed_progress = SimpleProgressBar()
        self.speed_progress.setRange(0, 100)
        self.speed_progress.setValue(60)  # Test value
        progress_layout.addWidget(self.speed_progress)
        
        speed_value = QLabel("60 / 100")
        speed_value.setAlignment(Qt.AlignCenter)
        speed_value.setStyleSheet("color: #2C3E50;")
        progress_layout.addWidget(speed_value)
        
        main_layout.addWidget(progress_frame)
        
        # Info frame
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.Box)
        info_frame.setStyleSheet("background-color: #F8F9FA; border: 1px solid #DEE2E6; border-radius: 8px; padding: 15px;")
        info_layout = QVBoxLayout(info_frame)
        
        info_title = QLabel("Joint Angles")
        info_title.setFont(QFont("Arial", 12, QFont.Bold))
        info_title.setStyleSheet("color: #2C3E50;")
        info_layout.addWidget(info_title)
        
        left_knee = QLabel("Left Knee: 85.5°")
        left_knee.setStyleSheet("color: #2C3E50;")
        info_layout.addWidget(left_knee)
        
        right_knee = QLabel("Right Knee: 92.3°")
        right_knee.setStyleSheet("color: #2C3E50;")
        info_layout.addWidget(right_knee)
        
        speed_display = QLabel("Walking Speed: 0.25 m/s")
        speed_display.setFont(QFont("Arial", 11, QFont.Bold))
        speed_display.setStyleSheet("color: #2C3E50; margin-top: 10px;")
        info_layout.addWidget(speed_display)
        
        main_layout.addWidget(info_frame)
        
        # Instructions
        instructions = QLabel("This is a simple test of the GUI components.\nThe full application will include camera feed and real-time analysis.")
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setWordWrap(True)
        instructions.setStyleSheet("color: #7F8C8D; font-style: italic; padding: 20px;")
        main_layout.addWidget(instructions)
        
        # Apply global styles
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ECF0F1;
            }
        """)

def main():
    """Main function"""
    app = QApplication(sys.argv)
    app.setApplicationName("Gait Detection System - Simple Test")
    
    window = SimpleMainWindow()
    window.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
