#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test GUI Features for Gait Detection System
"""

import sys
import time
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QPushButton
from PySide6.QtCore import QTimer, Qt
from PySide6.QtGui import QFont

from gui_widgets import GradientProgressBar, StatusPanel, MetricDisplay, AngleDisplay

class TestWindow(QMainWindow):
    """Test window for GUI features"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """Initialize UI"""
        self.setWindowTitle("GUI Features Test - Gait Detection System")
        self.setGeometry(100, 100, 900, 700)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Title
        title = QLabel("GUI Components Test")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: #2C3E50; padding: 20px;")
        main_layout.addWidget(title)
        
        # Progress bars section
        progress_section = self.create_progress_section()
        main_layout.addWidget(progress_section)
        
        # Metrics section
        metrics_section = self.create_metrics_section()
        main_layout.addWidget(metrics_section)
        
        # Angles section
        angles_section = self.create_angles_section()
        main_layout.addWidget(angles_section)
        
        # Control buttons
        controls = self.create_controls()
        main_layout.addWidget(controls)
        
        # Apply styles
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ECF0F1;
            }
            QWidget {
                background-color: transparent;
            }
        """)
        
    def create_progress_section(self):
        """Create progress bars section"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        title = QLabel("Gradient Progress Bars")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #2C3E50;")
        layout.addWidget(title)
        
        # Angle progress bar
        angle_label = QLabel("Angle Score")
        angle_label.setFont(QFont("Arial", 11, QFont.Bold))
        layout.addWidget(angle_label)
        
        self.angle_progress = GradientProgressBar()
        self.angle_progress.setRange(0, 100)
        self.angle_progress.setValue(75)
        layout.addWidget(self.angle_progress)
        
        self.angle_value_label = QLabel("75 / 100")
        self.angle_value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.angle_value_label)
        
        layout.addSpacing(10)
        
        # Speed progress bar
        speed_label = QLabel("Speed Score")
        speed_label.setFont(QFont("Arial", 11, QFont.Bold))
        layout.addWidget(speed_label)
        
        self.speed_progress = GradientProgressBar()
        self.speed_progress.setRange(0, 100)
        self.speed_progress.setValue(45)
        layout.addWidget(self.speed_progress)
        
        self.speed_value_label = QLabel("45 / 100")
        self.speed_value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.speed_value_label)
        
        widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 2px solid #3498DB;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        return widget
        
    def create_metrics_section(self):
        """Create metrics display section"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # Status panel
        self.status_panel = StatusPanel("Gait Status")
        self.status_panel.update_content("Excellent Gait", "#27AE60")
        layout.addWidget(self.status_panel)
        
        # Speed metric
        self.speed_metric = MetricDisplay("Walking Speed", "m/s")
        self.speed_metric.update_value(0.25, "#2C3E50")
        layout.addWidget(self.speed_metric)
        
        # Overall score metric
        self.score_metric = MetricDisplay("Overall Score", "/ 100")
        self.score_metric.update_value(72, "#27AE60")
        layout.addWidget(self.score_metric)
        
        return widget
        
    def create_angles_section(self):
        """Create angles display section"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        
        title = QLabel("Joint Angles")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #2C3E50;")
        title.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title)
        
        angles_container = QWidget()
        angles_layout = QHBoxLayout(angles_container)
        
        self.left_knee_display = AngleDisplay("Left Knee")
        self.left_knee_display.update_angle(85.5, "#2C3E50")
        angles_layout.addWidget(self.left_knee_display)
        
        self.right_knee_display = AngleDisplay("Right Knee")
        self.right_knee_display.update_angle(92.3, "#2C3E50")
        angles_layout.addWidget(self.right_knee_display)
        
        title_layout.addWidget(angles_container)
        layout.addWidget(title_widget)
        
        return widget
        
    def create_controls(self):
        """Create control buttons"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        self.animate_button = QPushButton("Animate Progress Bars")
        self.animate_button.clicked.connect(self.animate_progress)
        self.animate_button.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        layout.addWidget(self.animate_button)
        
        self.reset_button = QPushButton("Reset Values")
        self.reset_button.clicked.connect(self.reset_values)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        layout.addWidget(self.reset_button)
        
        return widget
        
    def setup_timer(self):
        """Setup timer for animations"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_values)
        self.animation_step = 0
        
    def animate_progress(self):
        """Start progress bar animation"""
        if not self.timer.isActive():
            self.animation_step = 0
            self.timer.start(100)  # Update every 100ms
            self.animate_button.setText("Animating...")
            self.animate_button.setEnabled(False)
            
    def update_values(self):
        """Update values during animation"""
        import math
        
        self.animation_step += 1
        
        # Calculate sine wave values for smooth animation
        angle_value = int(50 + 40 * math.sin(self.animation_step * 0.1))
        speed_value = int(30 + 50 * math.cos(self.animation_step * 0.08))
        
        # Update progress bars
        self.angle_progress.setValue(angle_value)
        self.angle_value_label.setText(f"{angle_value} / 100")
        
        self.speed_progress.setValue(speed_value)
        self.speed_value_label.setText(f"{speed_value} / 100")
        
        # Update metrics
        speed_ms = 0.1 + 0.3 * (speed_value / 100)
        self.speed_metric.update_value(speed_ms)
        
        overall_score = (angle_value + speed_value) // 2
        self.score_metric.update_value(overall_score)
        
        # Update status based on overall score
        if overall_score >= 70:
            self.status_panel.update_content("Excellent Gait", "#27AE60")
        elif overall_score >= 40:
            self.status_panel.update_content("Good Gait", "#F39C12")
        else:
            self.status_panel.update_content("Poor Gait", "#E74C3C")
        
        # Update angles
        left_angle = 80 + 20 * math.sin(self.animation_step * 0.05)
        right_angle = 85 + 15 * math.cos(self.animation_step * 0.07)
        
        self.left_knee_display.update_angle(left_angle)
        self.right_knee_display.update_angle(right_angle)
        
        # Stop animation after 50 steps
        if self.animation_step >= 50:
            self.timer.stop()
            self.animate_button.setText("Animate Progress Bars")
            self.animate_button.setEnabled(True)
            
    def reset_values(self):
        """Reset all values to defaults"""
        self.timer.stop()
        
        self.angle_progress.setValue(75)
        self.angle_value_label.setText("75 / 100")
        
        self.speed_progress.setValue(45)
        self.speed_value_label.setText("45 / 100")
        
        self.status_panel.update_content("Excellent Gait", "#27AE60")
        self.speed_metric.update_value(0.25)
        self.score_metric.update_value(72)
        
        self.left_knee_display.update_angle(85.5)
        self.right_knee_display.update_angle(92.3)
        
        self.animate_button.setText("Animate Progress Bars")
        self.animate_button.setEnabled(True)

def main():
    """Main function"""
    app = QApplication(sys.argv)
    app.setApplicationName("GUI Features Test")
    
    window = TestWindow()
    window.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
