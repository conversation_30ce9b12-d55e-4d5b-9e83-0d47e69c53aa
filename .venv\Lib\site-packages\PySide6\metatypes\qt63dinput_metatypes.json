[{"classes": [{"className": "KeyboardMouseGenericDeviceIntegration", "lineNumber": 28, "object": true, "qualifiedClassName": "Qt3DInput::Input::KeyboardMouseGenericDeviceIntegration", "superClasses": [{"access": "public", "name": "Qt3DInput::QInputDeviceIntegration"}]}], "inputFile": "keyboardmousegenericdeviceintegration_p.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractActionInput", "lineNumber": 15, "object": true, "qualifiedClassName": "Qt3DInput::QAbstractActionInput", "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qabstractactioninput.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractAxisInput", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sourceDevice", "notify": "sourceDeviceChanged", "read": "sourceDevice", "required": false, "scriptable": true, "stored": true, "type": "Qt3DInput::QAbstractPhysicalDevice*", "user": false, "write": "setSourceDevice"}], "qualifiedClassName": "Qt3DInput::QAbstractAxisInput", "signals": [{"access": "public", "arguments": [{"name": "sourceDevice", "type": "QAbstractPhysicalDevice*"}], "index": 0, "name": "sourceDeviceChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "sourceDevice", "type": "QAbstractPhysicalDevice*"}], "index": 1, "name": "setSourceDevice", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qabstractaxisinput.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractPhysicalDevice", "lineNumber": 20, "object": true, "qualifiedClassName": "Qt3DInput::QAbstractPhysicalDevice", "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qabstractphysicaldevice.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractPhysicalDeviceProxy", "enums": [{"isClass": false, "isFlag": false, "name": "DeviceStatus", "values": ["Ready", "NotFound"]}], "lineNumber": 30, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "deviceName", "read": "deviceName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "QAbstractPhysicalDeviceProxy::DeviceStatus", "user": false}], "qualifiedClassName": "Qt3DInput::QAbstractPhysicalDeviceProxy", "signals": [{"access": "public", "arguments": [{"name": "status", "type": "QAbstractPhysicalDeviceProxy::DeviceStatus"}], "index": 0, "name": "statusChanged", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DInput::QAbstractPhysicalDevice", "name": "QAbstractPhysicalDevice"}]}], "inputFile": "qabstractphysicaldeviceproxy_p.h", "outputRevision": 69}, {"classes": [{"className": "QAction", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "active", "notify": "activeChanged", "read": "isActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "Qt3DInput::QAction", "signals": [{"access": "public", "arguments": [{"name": "isActive", "type": "bool"}], "index": 0, "name": "activeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qaction.h", "outputRevision": 69}, {"classes": [{"className": "QActionInput", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sourceDevice", "notify": "sourceDeviceChanged", "read": "sourceDevice", "required": false, "scriptable": true, "stored": true, "type": "Qt3DInput::QAbstractPhysicalDevice*", "user": false, "write": "setSourceDevice"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "buttons", "notify": "buttonsChanged", "read": "buttons", "required": false, "scriptable": true, "stored": true, "type": "QList<int>", "user": false, "write": "setButtons"}], "qualifiedClassName": "Qt3DInput::QActionInput", "signals": [{"access": "public", "arguments": [{"name": "sourceDevice", "type": "QAbstractPhysicalDevice*"}], "index": 0, "name": "sourceDeviceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "buttons", "type": "QList<int>"}], "index": 1, "name": "buttonsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "sourceDevice", "type": "QAbstractPhysicalDevice*"}], "index": 2, "name": "setSourceDevice", "returnType": "void"}, {"access": "public", "arguments": [{"name": "buttons", "type": "QList<int>"}], "index": 3, "name": "setButtons", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DInput::QAbstractActionInput"}]}], "inputFile": "qactioninput.h", "outputRevision": 69}, {"classes": [{"className": "QAnalogAxisInput", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axis", "notify": "axisChanged", "read": "axis", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setAxis"}], "qualifiedClassName": "Qt3DInput::QAnalogAxisInput", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "int"}], "index": 0, "name": "axisChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "axis", "type": "int"}], "index": 1, "name": "setAxis", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DInput::QAbstractAxisInput", "name": "QAbstractAxisInput"}]}], "inputFile": "qanalogaxisinput.h", "outputRevision": 69}, {"classes": [{"className": "QAxis", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "value", "notify": "valueChanged", "read": "value", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}], "qualifiedClassName": "Qt3DInput::QAxis", "signals": [{"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qaxis.h", "outputRevision": 69}, {"classes": [{"className": "QAxisAccumulator", "enums": [{"isClass": false, "isFlag": false, "name": "SourceAxisType", "values": ["Velocity", "Acceleration"]}], "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sourceAxis", "notify": "sourceAxisChanged", "read": "sourceAxis", "required": false, "scriptable": true, "stored": true, "type": "Qt3DInput::QAxis*", "user": false, "write": "setSourceAxis"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "sourceAxisType", "notify": "sourceAxisTypeChanged", "read": "sourceAxisType", "required": false, "scriptable": true, "stored": true, "type": "SourceAxisType", "user": false, "write": "setSourceAxisType"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "scale", "notify": "scaleChanged", "read": "scale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setScale"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "value", "notify": "valueChanged", "read": "value", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "velocity", "notify": "velocityChanged", "read": "velocity", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}], "qualifiedClassName": "Qt3DInput::QAxisAccumulator", "signals": [{"access": "public", "arguments": [{"name": "sourceAxis", "type": "Qt3DInput::QAxis*"}], "index": 0, "name": "sourceAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceAxisType", "type": "QAxisAccumulator::SourceAxisType"}], "index": 1, "name": "sourceAxisTypeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 2, "name": "valueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 3, "name": "velocityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scale", "type": "float"}], "index": 4, "name": "scaleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "sourceAxis", "type": "Qt3DInput::QAxis*"}], "index": 5, "name": "setSourceAxis", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceAxisType", "type": "QAxisAccumulator::SourceAxisType"}], "index": 6, "name": "setSourceAxisType", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scale", "type": "float"}], "index": 7, "name": "setScale", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QComponent"}]}], "inputFile": "qaxisaccumulator.h", "outputRevision": 69}, {"classes": [{"className": "QAxisSetting", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "deadZoneRadius", "notify": "deadZoneRadiusChanged", "read": "deadZoneRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDeadZoneRadius"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axes", "notify": "axesChanged", "read": "axes", "required": false, "scriptable": true, "stored": true, "type": "QList<int>", "user": false, "write": "setAxes"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "smooth", "notify": "smoothChanged", "read": "isSmoothEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSmoothEnabled"}], "qualifiedClassName": "Qt3DInput::QAxisSetting", "signals": [{"access": "public", "arguments": [{"name": "deadZoneRadius", "type": "float"}], "index": 0, "name": "deadZoneRadiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axes", "type": "QList<int>"}], "index": 1, "name": "axesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "smooth", "type": "bool"}], "index": 2, "name": "smoothChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "deadZoneRadius", "type": "float"}], "index": 3, "name": "setDeadZoneRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axes", "type": "QList<int>"}], "index": 4, "name": "setAxes", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 5, "name": "setSmoothEnabled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QNode"}]}], "inputFile": "qaxissetting.h", "outputRevision": 69}, {"classes": [{"className": "QButtonAxisInput", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "scale", "notify": "scaleChanged", "read": "scale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setScale"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "buttons", "notify": "buttonsChanged", "read": "buttons", "required": false, "scriptable": true, "stored": true, "type": "QList<int>", "user": false, "write": "setButtons"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "acceleration", "notify": "accelerationChanged", "read": "acceleration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setAcceleration"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "deceleration", "notify": "decelerationChanged", "read": "deceleration", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDeceleration"}], "qualifiedClassName": "Qt3DInput::QButtonAxisInput", "signals": [{"access": "public", "arguments": [{"name": "scale", "type": "float"}], "index": 0, "name": "scaleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "buttons", "type": "QList<int>"}], "index": 1, "name": "buttonsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "acceleration", "type": "float"}], "index": 2, "name": "accelerationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "deceleration", "type": "float"}], "index": 3, "name": "decelerationChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "scale", "type": "float"}], "index": 4, "name": "setScale", "returnType": "void"}, {"access": "public", "arguments": [{"name": "buttons", "type": "QList<int>"}], "index": 5, "name": "setButtons", "returnType": "void"}, {"access": "public", "arguments": [{"name": "acceleration", "type": "float"}], "index": 6, "name": "setAcceleration", "returnType": "void"}, {"access": "public", "arguments": [{"name": "deceleration", "type": "float"}], "index": 7, "name": "setDeceleration", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DInput::QAbstractAxisInput", "name": "QAbstractAxisInput"}]}], "inputFile": "qbuttonaxisinput.h", "outputRevision": 69}, {"classes": [{"className": "QGenericInputDevice", "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axesMap", "notify": "axesMapChanged", "read": "axesMap", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false, "write": "setAxesMap"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "buttonsMap", "notify": "buttonsMapChanged", "read": "buttonsMap", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false, "write": "setButtonsMap"}], "qualifiedClassName": "Qt3DInput::QGenericInputDevice", "signals": [{"access": "public", "index": 0, "name": "axesMapChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "buttonsMapChanged", "returnType": "void"}], "superClasses": [{"access": "public", "fullyQualifiedName": "Qt3DInput::QAbstractPhysicalDevice", "name": "QAbstractPhysicalDevice"}]}], "inputFile": "qgenericinputdevice_p.h", "outputRevision": 69}, {"classes": [{"className": "QInputAspect", "lineNumber": 18, "object": true, "qualifiedClassName": "Qt3DInput::QInputAspect", "superClasses": [{"access": "public", "name": "Qt3DCore::QAbstractAspect"}]}], "inputFile": "qinputaspect.h", "outputRevision": 69}, {"classes": [{"className": "QInputChord", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "timeout", "notify": "timeoutChanged", "read": "timeout", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTimeout"}], "qualifiedClassName": "Qt3DInput::QInputChord", "signals": [{"access": "public", "arguments": [{"name": "timeout", "type": "int"}], "index": 0, "name": "timeoutChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "timeout", "type": "int"}], "index": 1, "name": "setTimeout", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DInput::QAbstractActionInput"}]}], "inputFile": "qinputchord.h", "outputRevision": 69}, {"classes": [{"className": "QInputDeviceIntegration", "lineNumber": 41, "object": true, "qualifiedClassName": "Qt3DInput::QInputDeviceIntegration", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qinputdeviceintegration_p.h", "outputRevision": 69}, {"classes": [{"className": "QInputDevicePlugin", "lineNumber": 30, "object": true, "qualifiedClassName": "Qt3DInput::QInputDevicePlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qinputdeviceplugin_p.h", "outputRevision": 69}, {"classes": [{"className": "QInputSequence", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "timeout", "notify": "timeoutChanged", "read": "timeout", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTimeout"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "buttonInterval", "notify": "buttonIntervalChanged", "read": "buttonInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setButtonInterval"}], "qualifiedClassName": "Qt3DInput::QInputSequence", "signals": [{"access": "public", "arguments": [{"name": "timeout", "type": "int"}], "index": 0, "name": "timeoutChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "buttonInterval", "type": "int"}], "index": 1, "name": "buttonIntervalChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "timeout", "type": "int"}], "index": 2, "name": "setTimeout", "returnType": "void"}, {"access": "public", "arguments": [{"name": "buttonInterval", "type": "int"}], "index": 3, "name": "setButtonInterval", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DInput::QAbstractActionInput"}]}], "inputFile": "qinputsequence.h", "outputRevision": 69}, {"classes": [{"className": "QInputSettings", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "eventSource", "notify": "eventSourceChanged", "read": "eventSource", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setEventSource"}], "qualifiedClassName": "Qt3DInput::QInputSettings", "signals": [{"access": "public", "arguments": [{"type": "QObject*"}], "index": 0, "name": "eventSourceChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "eventSource", "type": "QObject*"}], "index": 1, "name": "setEventSource", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QComponent"}]}], "inputFile": "qinputsettings.h", "outputRevision": 69}, {"classes": [{"className": "QKeyboardDevice", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "activeInput", "notify": "activeInputChanged", "read": "activeInput", "required": false, "scriptable": true, "stored": true, "type": "Qt3DInput::QKeyboardHandler*", "user": false}], "qualifiedClassName": "Qt3DInput::QKeyboardDevice", "signals": [{"access": "public", "arguments": [{"name": "activeInput", "type": "QKeyboardHandler*"}], "index": 0, "name": "activeInputChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DInput::QAbstractPhysicalDevice"}]}], "inputFile": "qkeyboarddevice.h", "outputRevision": 69}, {"classes": [{"className": "QKeyboardHandler", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sourceDevice", "notify": "sourceDeviceChanged", "read": "sourceDevice", "required": false, "scriptable": true, "stored": true, "type": "Qt3DInput::QKeyboardDevice*", "user": false, "write": "setSourceDevice"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "focus", "notify": "focusChanged", "read": "focus", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFocus"}], "qualifiedClassName": "Qt3DInput::QKeyboardHandler", "signals": [{"access": "public", "arguments": [{"name": "keyboardDevice", "type": "QKeyboardDevice*"}], "index": 0, "name": "sourceDeviceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "focus", "type": "bool"}], "index": 1, "name": "focusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 2, "name": "digit0Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 3, "name": "digit1Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 4, "name": "digit2Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 5, "name": "digit3Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 6, "name": "digit4Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 7, "name": "digit5Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 8, "name": "digit6Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 9, "name": "digit7Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 10, "name": "digit8Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 11, "name": "digit9Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 12, "name": "leftPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 13, "name": "rightPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 14, "name": "upPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 15, "name": "downPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 16, "name": "tabPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 17, "name": "backtabPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 18, "name": "asteriskPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 19, "name": "numberSignPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 20, "name": "escapePressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 21, "name": "returnPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 22, "name": "enterPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 23, "name": "deletePressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 24, "name": "spacePressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 25, "name": "backPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 26, "name": "cancelPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 27, "name": "selectPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 28, "name": "yesPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 29, "name": "noPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 30, "name": "context1Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 31, "name": "context2Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 32, "name": "context3Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 33, "name": "context4Pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 34, "name": "callPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 35, "name": "hangupPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 36, "name": "flipPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 37, "name": "menuPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 38, "name": "volumeUpPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 39, "name": "volumeDownPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 40, "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "event", "type": "Qt3DInput::QKeyEvent*"}], "index": 41, "name": "released", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "keyboardDevice", "type": "Qt3DInput::QKeyboardDevice*"}], "index": 42, "name": "setSourceDevice", "returnType": "void"}, {"access": "public", "arguments": [{"name": "focus", "type": "bool"}], "index": 43, "name": "setFocus", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QComponent"}]}], "inputFile": "qkeyboardhandler.h", "outputRevision": 69}, {"classes": [{"className": "QKeyEvent", "lineNumber": 21, "methods": [{"access": "public", "arguments": [{"name": "key_", "type": "QKeySequence::StandardKey"}], "index": 0, "isConst": true, "name": "matches", "returnType": "bool"}], "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "key", "read": "key", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "text", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "modifiers", "read": "modifiers", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "isAutoRepeat", "read": "isAutoRepeat", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "count", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "nativeScanCode", "read": "nativeScanCode", "required": false, "scriptable": true, "stored": true, "type": "quint32", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "accepted", "read": "isAccepted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAccepted"}], "qualifiedClassName": "Qt3DInput::QKeyEvent", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qkeyevent.h", "outputRevision": 69}, {"classes": [{"className": "QLogicalDevice", "lineNumber": 18, "object": true, "qualifiedClassName": "Qt3DInput::QLogicalDevice", "superClasses": [{"access": "public", "name": "Qt3DCore::QComponent"}]}], "inputFile": "qlogicaldevice.h", "outputRevision": 69}, {"classes": [{"className": "QMouseDevice", "enums": [{"isClass": false, "isFlag": false, "name": "Axis", "values": ["X", "Y", "WheelX", "WheelY"]}], "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sensitivity", "notify": "sensitivityChanged", "read": "sensitivity", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSensitivity"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "updateAxesContinuously", "notify": "updateAxesContinuouslyChanged", "read": "updateAxesContinuously", "required": false, "revision": 65295, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUpdateAxesContinuously"}], "qualifiedClassName": "Qt3DInput::QMouseDevice", "signals": [{"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 0, "name": "sensitivityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "updateAxesContinuously", "type": "bool"}], "index": 1, "name": "updateAxesContinuouslyChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 2, "name": "setSensitivity", "returnType": "void"}, {"access": "public", "arguments": [{"name": "updateAxesContinuously", "type": "bool"}], "index": 3, "name": "setUpdateAxesContinuously", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DInput::QAbstractPhysicalDevice"}]}], "inputFile": "qmousedevice.h", "outputRevision": 69}, {"classes": [{"className": "QMouseEvent", "enums": [{"isClass": false, "isFlag": false, "name": "Buttons", "values": ["LeftButton", "RightButton", "MiddleButton", "BackButton", "NoButton"]}, {"isClass": false, "isFlag": false, "name": "Modifiers", "values": ["NoModifier", "ShiftModifier", "ControlModifier", "AltModifier", "MetaModifier", "KeypadModifier"]}], "lineNumber": 18, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "x", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "y", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "wasHeld", "read": "wasHeld", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "button", "read": "button", "required": false, "scriptable": true, "stored": true, "type": "Qt3DInput::QMouseEvent::Buttons", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "buttons", "read": "buttons", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "modifiers", "read": "modifiers", "required": false, "scriptable": true, "stored": true, "type": "Qt3DInput::QMouseEvent::Modifiers", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "accepted", "read": "isAccepted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAccepted"}], "qualifiedClassName": "Qt3DInput::QMouseEvent", "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QWheelEvent", "enums": [{"isClass": false, "isFlag": false, "name": "Buttons", "values": ["LeftButton", "RightButton", "MiddleButton", "BackButton", "NoButton"]}, {"isClass": false, "isFlag": false, "name": "Modifiers", "values": ["NoModifier", "ShiftModifier", "ControlModifier", "AltModifier", "MetaModifier", "KeypadModifier"]}], "lineNumber": 76, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "x", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "y", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "<PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "buttons", "read": "buttons", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "modifiers", "read": "modifiers", "required": false, "scriptable": true, "stored": true, "type": "Qt3DInput::QWheelEvent::Modifiers", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "accepted", "read": "isAccepted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAccepted"}], "qualifiedClassName": "Qt3DInput::QWheelEvent", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmouseevent.h", "outputRevision": 69}, {"classes": [{"className": "QMouseHandler", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sourceDevice", "notify": "sourceDeviceChanged", "read": "sourceDevice", "required": false, "scriptable": true, "stored": true, "type": "Qt3DInput::QMouseDevice*", "user": false, "write": "setSourceDevice"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "containsMouse", "notify": "containsMouseChanged", "read": "containsMouse", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "Qt3DInput::QMouseHandler", "signals": [{"access": "public", "arguments": [{"name": "mouseDevice", "type": "QMouseDevice*"}], "index": 0, "name": "sourceDeviceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "containsMouse", "type": "bool"}], "index": 1, "name": "containsMouseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mouse", "type": "Qt3DInput::QMouseEvent*"}], "index": 2, "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mouse", "type": "Qt3DInput::QMouseEvent*"}], "index": 3, "name": "doubleClicked", "returnType": "void"}, {"access": "public", "index": 4, "name": "entered", "returnType": "void"}, {"access": "public", "index": 5, "name": "exited", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mouse", "type": "Qt3DInput::QMouseEvent*"}], "index": 6, "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mouse", "type": "Qt3DInput::QMouseEvent*"}], "index": 7, "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mouse", "type": "Qt3DInput::QMouseEvent*"}], "index": 8, "name": "pressAndHold", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mouse", "type": "Qt3DInput::QMouseEvent*"}], "index": 9, "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "wheel", "type": "Qt3DInput::QWheelEvent*"}], "index": 10, "name": "wheel", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "mouseDevice", "type": "QMouseDevice*"}], "index": 11, "name": "setSourceDevice", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Qt3DCore::QComponent"}]}], "inputFile": "qmousehandler.h", "outputRevision": 69}]