# Gait Detection System Configuration File
# Walking Gait Detection and Speed Calculation System

gait_analysis:
  # 角度條件閾值設定（膝關節角度，單位：度）
  # 這些參數控制膝關節角度的評分標準
  angle_thresholds:
    # 優良範圍：正常行走角度範圍
    # 可以設定多個範圍，系統會檢查角度是否落在任一範圍內
    excellent_ranges:
      - [60, 100]   # 膝關節彎曲範圍（正常行走時的彎曲角度）
      - [160, 180]  # 膝關節伸展範圍（正常行走時的伸展角度）

    # 一般範圍：輕微異常角度範圍
    # 這些角度表示步態有輕微問題但仍可接受
    good_ranges:
      - [100, 130]  # 輕微過度彎曲
      - [130, 160]  # 中度異常範圍

    # 不佳範圍：嚴重異常角度（雙腿同時在此範圍內）
    # 當雙腿膝關節角度都在此範圍時，表示步態有明顯問題
    poor_range: [130, 160]

  # 速度條件閾值設定
  # 系統會根據 pixel_to_meter 設定自動選擇使用哪組閾值
  speed_thresholds:
    # 實際速度閾值（當 pixel_to_meter 不為 null 時使用，單位：公尺/秒）
    meters_per_second:
      excellent: 1.1    # 優良：每秒1.1公尺以上（快速正常行走）
      good: 0.8         # 一般：每秒0.8-1.1公尺（中等速度行走）
      # 不佳：低於0.8公尺/秒（緩慢行走，可能有步態問題）

    # 相對速度閾值（當 pixel_to_meter 為 null 時使用，單位：像素/秒）
    pixels_per_second:
      excellent: 0.25     # 優良：每秒0.25像素以上（快速移動）
      good: 0.12          # 一般：每秒0.12-0.25像素（中等移動）
      # 不佳：低於0.12像素/秒（緩慢移動）

  # 評分權重設定
  # 這些參數控制角度和速度在最終評分中的重要性
  scoring:
    angle_weight: 0.3    # 角度條件權重（30%）- 角度評分的重要性
    speed_weight: 0.7    # 速度條件權重（70%）- 速度評分的重要性
    # 注意：兩個權重的總和應該等於1.0

  # 平滑處理參數
  # 這些參數用於減少數據波動，提供更穩定的評分
  smoothing:
    speed_window: 5      # 速度平滑視窗大小（使用最近5個數據點計算平均速度）
    score_window: 10     # 評分平滑視窗大小（使用最近10個評分計算平均分數）

# Visualization settings
visualization:
  # Progress bar settings
  progress_bar:
    width: 300           # Progress bar width
    height: 20           # Progress bar height
    position: [50, 120]  # Progress bar position [x, y]

    # Color settings (BGR format)
    colors:
      excellent: [0, 255, 0]    # Green
      good: [0, 255, 255]       # Yellow
      poor: [0, 0, 255]         # Red
      background: [64, 64, 64]  # Background gray
      border: [255, 255, 255]   # Border white

    # Range segments
    ranges:
      excellent: [70, 100]  # Excellent range
      good: [30, 70]        # Good range
      poor: [0, 30]         # Poor range

# GUI settings
gui:
  window:
    title: "Gait Detection System"
    width: 1200
    height: 800
    min_width: 800
    min_height: 600

  colors:
    primary: "#2C3E50"      # Dark blue-gray
    secondary: "#34495E"    # Lighter blue-gray
    accent: "#3498DB"       # Blue
    success: "#27AE60"      # Green
    warning: "#F39C12"      # Orange
    danger: "#E74C3C"       # Red
    background: "#ECF0F1"   # Light gray
    text: "#2C3E50"         # Dark text
    text_light: "#7F8C8D"   # Light text

# Auto scoring system
auto_scoring:
  # Auto scoring enable/disable
  enabled: false          # Default disabled, user can enable via GUI

  # Person detection settings
  detection:
    enter_threshold: 5    # Frames to confirm person entered
    exit_threshold: 10    # Frames to confirm person left
    min_session_duration: 3.0  # Minimum session duration in seconds

  # Scoring calculation settings
  calculation:
    min_samples: 5       # Minimum samples required for valid scoring
    # 注意：評分權重使用 gait_analysis.scoring 中的設定，避免重複配置

  # Result display settings
  result_display:
    show_duration: 15     # Duration to show result dialog in seconds
    font_size: 24         # Font size for result display
    window_size: [400, 300]  # Result window size [width, height]

    # Score level thresholds
    thresholds:
      excellent: 80       # Score >= 80 is excellent
      good: 60           # Score >= 60 is good
      poor: 0            # Score < 60 is poor

    # Display colors for different score levels
    colors:
      excellent: "#27AE60"  # Green
      good: "#F39C12"       # Orange
      poor: "#E74C3C"       # Red
      background: "#FFFFFF" # White background
      text: "#2C3E50"       # Dark text

# 系統參數
system:
  # 像素到公尺的轉換比例
  # null 表示使用相對速度，不進行實際距離轉換
  # 如果要使用實際速度測量，需要設定此參數（例如：0.001 表示1像素=0.001公尺）
  pixel_to_meter: null

  # 目標幀率設定
  fps_target: 30          # 系統運行的目標幀率（每秒30幀）

# 參數調整指南：
#
# 1. 速度閾值調整：
#    系統會根據 pixel_to_meter 設定自動選擇使用哪組閾值：
#    - pixel_to_meter 不為 null：使用 meters_per_second 閾值
#    - pixel_to_meter 為 null：使用 pixels_per_second 閾值
#
#    實際速度調整（meters_per_second）：
#    - 如果覺得速度標準太嚴格，可以降低 excellent 和 good 的數值
#    - 如果覺得速度標準太寬鬆，可以提高這些數值
#    - 建議根據實際測試對象的行走能力進行調整
#
#    相對速度調整（pixels_per_second）：
#    - 需要根據攝影機解析度和拍攝距離調整
#    - 較高解析度或較近距離需要較高的像素閾值
#    - 較低解析度或較遠距離需要較低的像素閾值
#
# 2. 角度閾值調整：
#    - excellent_ranges: 正常步態的角度範圍，可以根據醫學標準調整
#    - good_ranges: 可接受的輕微異常範圍
#    - poor_range: 明顯異常的角度範圍
#
# 3. 評分權重調整：
#    - angle_weight + speed_weight 必須等於 1.0
#    - 如果更重視角度評估，可以提高 angle_weight（例如 0.7）並降低 speed_weight（例如 0.3）
#    - 如果更重視速度評估，可以提高 speed_weight 並降低 angle_weight
#
# 4. 平滑參數調整：
#    - speed_window: 數值越大，速度變化越平滑但反應越慢
#    - score_window: 數值越大，評分變化越平滑但反應越慢
#
# 5. 像素轉換調整：
#    - 如果要使用實際速度測量，需要校準 pixel_to_meter 參數
#    - 可以通過測量已知距離在畫面中的像素數來計算此比例
