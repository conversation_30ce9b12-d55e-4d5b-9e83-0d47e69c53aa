#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Quick test for the enum fix
"""

try:
    from PySide6.QtCore import Qt
    
    print("Testing Qt.CheckState enum:")
    print(f"Qt.Checked: {Qt.Checked}")
    print(f"Qt.Checked.value: {Qt.Checked.value}")
    print(f"Type of Qt.Checked: {type(Qt.Checked)}")
    print(f"Type of Qt.Checked.value: {type(Qt.Checked.value)}")
    
    # Test the logic
    state_int = 2  # This is what stateChanged signal sends
    state_enum = Qt.Checked
    
    print(f"\nTesting logic:")
    print(f"state_int == Qt.Checked.value: {state_int == Qt.Checked.value}")
    print(f"state_enum == Qt.Checked: {state_enum == Qt.Checked}")
    
    print("\nEnum fix test passed!")
    
except ImportError:
    print("PySide6 not available in current environment")
except Exception as e:
    print(f"Error: {e}")
