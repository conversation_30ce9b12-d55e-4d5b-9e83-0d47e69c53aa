import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/eventconnection_p.h"
        name: "QScxmlEventConnection"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtScxml/EventConnection 5.8",
            "QtScxml/EventConnection 6.0"
        ]
        exportMetaObjectRevisions: [1288, 1536]
        Property {
            name: "events"
            type: "QStringList"
            bindable: "bindableEvents"
            read: "events"
            write: "setEvents"
            notify: "eventsChanged"
            index: 0
        }
        Property {
            name: "stateMachine"
            type: "QScxmlStateMachine"
            isPointer: true
            bindable: "bindableStateMachine"
            read: "stateMachine"
            write: "setStateMachine"
            notify: "stateMachineChanged"
            index: 1
        }
        Signal { name: "eventsChanged" }
        Signal { name: "stateMachineChanged" }
        Signal {
            name: "occurred"
            Parameter { name: "event"; type: "QScxmlEvent" }
        }
    }
    Component {
        file: "private/eventconnection_p.h"
        name: "QScxmlEvent"
        accessSemantics: "value"
        Enum {
            name: "EventType"
            values: ["PlatformEvent", "InternalEvent", "ExternalEvent"]
        }
        Property { name: "name"; type: "QString"; read: "name"; write: "setName"; index: 0 }
        Property {
            name: "eventType"
            type: "EventType"
            read: "eventType"
            write: "setEventType"
            index: 1
        }
        Property { name: "scxmlType"; type: "QString"; read: "scxmlType"; index: 2; isReadonly: true }
        Property { name: "sendId"; type: "QString"; read: "sendId"; write: "setSendId"; index: 3 }
        Property { name: "origin"; type: "QString"; read: "origin"; write: "setOrigin"; index: 4 }
        Property {
            name: "originType"
            type: "QString"
            read: "originType"
            write: "setOriginType"
            index: 5
        }
        Property { name: "invokeId"; type: "QString"; read: "invokeId"; write: "setInvokeId"; index: 6 }
        Property { name: "delay"; type: "int"; read: "delay"; write: "setDelay"; index: 7 }
        Property { name: "data"; type: "QVariant"; read: "data"; write: "setData"; index: 8 }
        Property { name: "errorEvent"; type: "bool"; read: "isErrorEvent"; index: 9; isReadonly: true }
        Property {
            name: "errorMessage"
            type: "QString"
            read: "errorMessage"
            write: "setErrorMessage"
            index: 10
        }
        Method { name: "clear" }
    }
    Component {
        file: "private/invokedservices_p.h"
        name: "QScxmlInvokedServices"
        accessSemantics: "reference"
        defaultProperty: "qmlChildren"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtScxml/InvokedServices 5.8",
            "QtScxml/InvokedServices 6.0"
        ]
        exportMetaObjectRevisions: [1288, 1536]
        Property {
            name: "stateMachine"
            type: "QScxmlStateMachine"
            isPointer: true
            bindable: "bindableStateMachine"
            read: "stateMachine"
            write: "setStateMachine"
            notify: "stateMachineChanged"
            index: 0
        }
        Property {
            name: "children"
            type: "QVariantMap"
            bindable: "bindableChildren"
            read: "children"
            notify: "childrenChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "qmlChildren"
            type: "QObject"
            isList: true
            read: "qmlChildren"
            index: 2
            isReadonly: true
        }
        Signal { name: "childrenChanged" }
        Signal { name: "stateMachineChanged" }
    }
    Component {
        file: "private/statemachineextended_p.h"
        name: "QScxmlStateMachineExtended"
        accessSemantics: "reference"
        defaultProperty: "children"
        prototype: "QObject"
        Property {
            name: "children"
            type: "QObject"
            isList: true
            read: "children"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/statemachineextended_p.h"
        name: "QScxmlStateMachine"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QScxmlStateMachineExtended"
        exports: ["QtScxml/StateMachine 5.8", "QtScxml/StateMachine 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1288, 1536]
        Property {
            name: "running"
            type: "bool"
            read: "isRunning"
            write: "setRunning"
            notify: "runningChanged"
            index: 0
        }
        Property {
            name: "initialized"
            type: "bool"
            bindable: "bindableInitialized"
            read: "isInitialized"
            notify: "initializedChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "dataModel"
            type: "QScxmlDataModel"
            isPointer: true
            bindable: "bindableDataModel"
            read: "dataModel"
            write: "setDataModel"
            notify: "dataModelChanged"
            index: 2
        }
        Property {
            name: "initialValues"
            type: "QVariantMap"
            bindable: "bindableInitialValues"
            read: "initialValues"
            write: "setInitialValues"
            notify: "initialValuesChanged"
            index: 3
        }
        Property {
            name: "invokedServices"
            type: "QList<QScxmlInvokableService*>"
            bindable: "bindableInvokedServices"
            read: "invokedServices"
            notify: "invokedServicesChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "sessionId"
            type: "QString"
            read: "sessionId"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "invoked"
            type: "bool"
            read: "isInvoked"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "parseErrors"
            type: "QScxmlError"
            isList: true
            read: "parseErrors"
            index: 8
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "loader"
            type: "QScxmlCompiler::Loader"
            isPointer: true
            bindable: "bindableLoader"
            read: "loader"
            write: "setLoader"
            notify: "loaderChanged"
            index: 9
        }
        Property {
            name: "tableData"
            type: "QScxmlTableData"
            isPointer: true
            bindable: "bindableTableData"
            read: "tableData"
            write: "setTableData"
            notify: "tableDataChanged"
            index: 10
        }
        Signal {
            name: "runningChanged"
            Parameter { name: "running"; type: "bool" }
        }
        Signal {
            name: "invokedServicesChanged"
            Parameter { name: "invokedServices"; type: "QList<QScxmlInvokableService*>" }
        }
        Signal {
            name: "log"
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "msg"; type: "QString" }
        }
        Signal { name: "reachedStableState" }
        Signal { name: "finished" }
        Signal {
            name: "dataModelChanged"
            Parameter { name: "model"; type: "QScxmlDataModel"; isPointer: true }
        }
        Signal {
            name: "initialValuesChanged"
            Parameter { name: "initialValues"; type: "QVariantMap" }
        }
        Signal {
            name: "initializedChanged"
            Parameter { name: "initialized"; type: "bool" }
        }
        Signal {
            name: "loaderChanged"
            Parameter { name: "loader"; type: "QScxmlCompiler::Loader"; isPointer: true }
        }
        Signal {
            name: "tableDataChanged"
            Parameter { name: "tableData"; type: "QScxmlTableData"; isPointer: true }
        }
        Method { name: "start" }
        Method { name: "stop" }
        Method { name: "init"; type: "bool" }
        Method {
            name: "stateNames"
            type: "QStringList"
            isMethodConstant: true
            Parameter { name: "compress"; type: "bool" }
        }
        Method { name: "stateNames"; type: "QStringList"; isCloned: true; isMethodConstant: true }
        Method {
            name: "activeStateNames"
            type: "QStringList"
            isMethodConstant: true
            Parameter { name: "compress"; type: "bool" }
        }
        Method { name: "activeStateNames"; type: "QStringList"; isCloned: true; isMethodConstant: true }
        Method {
            name: "isActive"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "scxmlStateName"; type: "QString" }
        }
        Method {
            name: "submitEvent"
            Parameter { name: "event"; type: "QScxmlEvent"; isPointer: true }
        }
        Method {
            name: "submitEvent"
            Parameter { name: "eventName"; type: "QString" }
        }
        Method {
            name: "submitEvent"
            Parameter { name: "eventName"; type: "QString" }
            Parameter { name: "data"; type: "QVariant" }
        }
        Method {
            name: "cancelDelayedEvent"
            Parameter { name: "sendId"; type: "QString" }
        }
        Method {
            name: "isDispatchableTarget"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "target"; type: "QString" }
        }
    }
    Component {
        file: "private/statemachineloader_p.h"
        name: "QScxmlStateMachineLoader"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtScxml/StateMachineLoader 5.8",
            "QtScxml/StateMachineLoader 6.0"
        ]
        exportMetaObjectRevisions: [1288, 1536]
        Property {
            name: "source"
            type: "QUrl"
            bindable: "bindableSource"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "stateMachine"
            type: "QScxmlStateMachine"
            isPointer: true
            bindable: "bindableStateMachine"
            read: "stateMachine"
            notify: "stateMachineChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "initialValues"
            type: "QVariantMap"
            bindable: "bindableInitialValues"
            read: "initialValues"
            write: "setInitialValues"
            notify: "initialValuesChanged"
            index: 2
        }
        Property {
            name: "dataModel"
            type: "QScxmlDataModel"
            isPointer: true
            bindable: "bindableDataModel"
            read: "dataModel"
            write: "setDataModel"
            notify: "dataModelChanged"
            index: 3
        }
        Signal { name: "sourceChanged" }
        Signal { name: "initialValuesChanged" }
        Signal { name: "stateMachineChanged" }
        Signal { name: "dataModelChanged" }
    }
}
