[{"classes": [{"className": "AutofillPopupEventFilter", "lineNumber": 120, "object": true, "qualifiedClassName": "QtWebEngineCore::AutofillPopupEventFilter", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "ui_delegates_manager_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "WebEngineAction"}, {"name": "QML.AddedInVersion", "value": "264"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebEngineAction", "lineNumber": 32, "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "text", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "iconName", "read": "iconName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "enabled", "notify": "enabledChanged", "read": "isEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QQuickWebEngineAction", "signals": [{"access": "public", "index": 0, "name": "triggered", "returnType": "void"}, {"access": "public", "index": 1, "name": "enabledChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 2, "name": "trigger", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickwebengineaction_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "WebEngineClientCertificateOption"}, {"name": "QML.AddedInVersion", "value": "265"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebEngineClientCertificateOption", "lineNumber": 36, "methods": [{"access": "public", "index": 0, "name": "select", "returnType": "void"}], "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "issuer", "read": "issuer", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "subject", "read": "subject", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 2, "name": "effectiveDate", "read": "effectiveDate", "required": false, "scriptable": true, "stored": true, "type": "QDateTime", "user": false}, {"constant": true, "designable": true, "final": true, "index": 3, "name": "expiryDate", "read": "expiryDate", "required": false, "scriptable": true, "stored": true, "type": "QDateTime", "user": false}, {"constant": true, "designable": true, "final": true, "index": 4, "name": "isSelfSigned", "read": "isSelfSigned", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QQuickWebEngineClientCertificateOption", "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "WebEngineClientCertificateSelection"}, {"name": "QML.AddedInVersion", "value": "265"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebEngineClientCertificateSelection", "lineNumber": 65, "methods": [{"access": "public", "arguments": [{"name": "idx", "type": "int"}], "index": 0, "name": "select", "returnType": "void"}, {"access": "public", "arguments": [{"name": "certificate", "type": "const QQuickWebEngineClientCertificateOption*"}], "index": 1, "name": "select", "returnType": "void"}, {"access": "public", "index": 2, "name": "selectNone", "returnType": "void"}], "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "host", "read": "host", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "certificates", "read": "certificates", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQuickWebEngineClientCertificateOption>", "user": false}], "qualifiedClassName": "QQuickWebEngineClientCertificateSelection", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickwebengineclientcertificateselection_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "AuthenticationDialogRequest"}, {"name": "QML.AddedInVersion", "value": "260"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebEngineAuthenticationDialogRequest", "enums": [{"isClass": false, "isFlag": false, "name": "AuthenticationType", "values": ["AuthenticationTypeHTTP", "AuthenticationTypeProxy"]}], "lineNumber": 35, "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "url", "read": "url", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "realm", "read": "realm", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 2, "name": "proxyHost", "read": "proxyHost", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 3, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "AuthenticationType", "user": false}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "accepted", "read": "isAccepted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAccepted"}], "qualifiedClassName": "QQuickWebEngineAuthenticationDialogRequest", "slots": [{"access": "public", "arguments": [{"name": "user", "type": "QString"}, {"name": "password", "type": "QString"}], "index": 0, "name": "dialogAccept", "returnType": "void"}, {"access": "public", "index": 1, "name": "dialogReject", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "JavaScriptDialogRequest"}, {"name": "QML.AddedInVersion", "value": "260"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebEngineJavaScriptDialogRequest", "enums": [{"isClass": false, "isFlag": false, "name": "DialogType", "values": ["DialogTypeAlert", "DialogTypeConfirm", "DialogTypePrompt", "DialogTypeBeforeUnload"]}], "lineNumber": 82, "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "message", "read": "message", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "defaultText", "read": "defaultText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 2, "name": "title", "read": "title", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 3, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "DialogType", "user": false}, {"constant": true, "designable": true, "final": true, "index": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "accepted", "read": "isAccepted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAccepted"}], "qualifiedClassName": "QQuickWebEngineJavaScriptDialogRequest", "slots": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 0, "name": "dialogAccept", "returnType": "void"}, {"access": "public", "index": 1, "isCloned": true, "name": "dialogAccept", "returnType": "void"}, {"access": "public", "index": 2, "name": "dialogReject", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "ColorDialogRequest"}, {"name": "QML.AddedInVersion", "value": "260"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebEngineColorDialogRequest", "lineNumber": 133, "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "color", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "accepted", "read": "isAccepted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAccepted"}], "qualifiedClassName": "QQuickWebEngineColorDialogRequest", "slots": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 0, "name": "dialogAccept", "returnType": "void"}, {"access": "public", "index": 1, "name": "dialogReject", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "FileDialogRequest"}, {"name": "QML.AddedInVersion", "value": "260"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebEngineFileDialogRequest", "enums": [{"isClass": false, "isFlag": false, "name": "FileMode", "values": ["FileModeOpen", "FileModeOpenMultiple", "FileModeUploadFolder", "FileModeSave"]}], "lineNumber": 164, "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "defaultFileName", "read": "defaultFileName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "acceptedMimeTypes", "read": "acceptedMimeTypes", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}, {"constant": true, "designable": true, "final": true, "index": 2, "name": "mode", "read": "mode", "required": false, "scriptable": true, "stored": true, "type": "FileMode", "user": false}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "accepted", "read": "isAccepted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAccepted"}], "qualifiedClassName": "QQuickWebEngineFileDialogRequest", "slots": [{"access": "public", "arguments": [{"name": "files", "type": "QStringList"}], "index": 0, "name": "dialogAccept", "returnType": "void"}, {"access": "public", "index": 1, "name": "dialogReject", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "TooltipRequest"}, {"name": "QML.AddedInVersion", "value": "266"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebEngineTooltipRequest", "enums": [{"isClass": false, "isFlag": false, "name": "RequestType", "values": ["Show", "<PERSON>de"]}], "lineNumber": 209, "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "x", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "y", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": true, "index": 2, "name": "text", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 3, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "RequestType", "user": false}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "accepted", "read": "isAccepted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAccepted"}], "qualifiedClassName": "QQuickWebEngineTooltipRequest", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickwebenginedialogrequests_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "WebEngineDownloadRequest"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebEngineDownloadRequest", "lineNumber": 16, "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "view", "read": "view", "required": false, "scriptable": true, "stored": true, "type": "QQuickWebEngineView*", "user": false}], "qualifiedClassName": "QQuickWebEngineDownloadRequest", "superClasses": [{"access": "public", "name": "QWebEngineDownloadRequest"}]}], "inputFile": "qquickwebenginedownloadrequest.h", "outputRevision": 69}, {"classes": [{"className": "FaviconImageResponse", "lineNumber": 27, "object": true, "qualifiedClassName": "FaviconImageResponse", "slots": [{"access": "public", "arguments": [{"name": "pixmap", "type": "QPixmap"}], "index": 0, "name": "handleDone", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickImageResponse"}]}, {"className": "FaviconImageRequester", "lineNumber": 47, "object": true, "qualifiedClassName": "FaviconImageRequester", "signals": [{"access": "public", "arguments": [{"name": "pixmap", "type": "QPixmap"}], "index": 0, "name": "done", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "icon", "type": "QIcon"}], "index": 1, "name": "iconRequestDone", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QQuickWebEngineFaviconProvider", "lineNumber": 71, "object": true, "qualifiedClassName": "QQuickWebEngineFaviconProvider", "signals": [{"access": "public", "arguments": [{"name": "faviconResponse", "type": "QPointer<FaviconImageResponse>"}], "index": 0, "name": "imageResponseRequested", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickAsyncImageProvider"}]}, {"className": "FaviconProviderHelper", "lineNumber": 87, "object": true, "qualifiedClassName": "FaviconProviderHelper", "slots": [{"access": "public", "arguments": [{"name": "faviconResponse", "type": "QPointer<FaviconImageResponse>"}], "index": 0, "name": "handleImageRequest", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickwebenginefaviconprovider_p_p.h", "outputRevision": 69}, {"classes": [{"className": "QWebEngineLoadingInfoDerived", "gadget": true, "lineNumber": 41, "qualifiedClassName": "QWebEngineLoadingInfoDerived", "superClasses": [{"access": "public", "name": "QWebEngineLoadingInfo"}]}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineLoadingInfo"}, {"name": "QML.Element", "value": "webEngineLoadingInfo"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineLoadingInfo", "gadget": true, "lineNumber": 55, "qualifiedClassName": "ForeignWebEngineLoadingInfo"}, {"className": "QWebEngineCertificateErrorDerived", "gadget": true, "lineNumber": 67, "qualifiedClassName": "QWebEngineCertificateErrorDerived", "superClasses": [{"access": "public", "name": "QWebEngineCertificateError"}]}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineCertificateError"}, {"name": "QML.Element", "value": "webEngineCertificateError"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineCertificateError", "gadget": true, "lineNumber": 81, "qualifiedClassName": "ForeignWebEngineCertificateError"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineNavigationRequest"}, {"name": "QML.Element", "value": "WebEngineNavigationRequest"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineNavigationRequest", "gadget": true, "lineNumber": 91, "qualifiedClassName": "ForeignWebEngineNavigationRequest"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineScript"}, {"name": "QML.Element", "value": "webEngineScript"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineScript", "gadget": true, "lineNumber": 110, "qualifiedClassName": "ForeignWebEngineScript"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineHistory"}, {"name": "QML.Element", "value": "WebEngineHistory"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineHistory", "gadget": true, "lineNumber": 120, "qualifiedClassName": "ForeignWebEngineHistory"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineHistoryModel"}, {"name": "QML.Element", "value": "WebEngineHistoryModel"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineHistoryModel", "gadget": true, "lineNumber": 130, "qualifiedClassName": "ForeignWebEngineHistoryModel"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineFullScreenRequest"}, {"name": "QML.Element", "value": "fullScreenRequest"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineFullScreenRequest", "gadget": true, "lineNumber": 140, "qualifiedClassName": "ForeignWebEngineFullScreenRequest"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineContextMenuRequest"}, {"name": "QML.Element", "value": "ContextMenuRequest"}, {"name": "QML.AddedInVersion", "value": "263"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineContextMenuRequest", "gadget": true, "lineNumber": 150, "qualifiedClassName": "ForeignWebEngineContextMenuRequest"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineQuotaRequest"}, {"name": "QML.Element", "value": "webEngineQuotaRequest"}, {"name": "QML.AddedInVersion", "value": "263"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineQuotaRequest", "gadget": true, "lineNumber": 161, "qualifiedClassName": "ForeignWebEngineQuotaRequest"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineRegisterProtocolHandlerRequest"}, {"name": "QML.Element", "value": "registerProtocolHandlerRequest"}, {"name": "QML.AddedInVersion", "value": "263"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineRegisterProtocolHandlerRequest", "gadget": true, "lineNumber": 172, "qualifiedClassName": "ForeignWebEngineRegisterProtocolHandlerRequest"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineNotification"}, {"name": "QML.Element", "value": "WebEngineNotification"}, {"name": "QML.AddedInVersion", "value": "265"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineNotification", "gadget": true, "lineNumber": 182, "qualifiedClassName": "ForeignWebEngineNotification"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineFindTextResult"}, {"name": "QML.Element", "value": "findTextResult"}, {"name": "QML.AddedInVersion", "value": "266"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineFindTextResult", "gadget": true, "lineNumber": 192, "qualifiedClassName": "ForeignWebEngineFindTextResult"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineFileSystemAccessRequest"}, {"name": "QML.Element", "value": "webEngineFileSystemAccessRequest"}, {"name": "QML.AddedInVersion", "value": "1540"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeginWebEngineFileSystemAccessRequest", "gadget": true, "lineNumber": 202, "qualifiedClassName": "ForeginWebEngineFileSystemAccessRequest"}, {"className": "QWebEngineFileSystemAccessRequestDerived", "gadget": true, "lineNumber": 213, "qualifiedClassName": "QWebEngineFileSystemAccessRequestDerived", "superClasses": [{"access": "public", "name": "QWebEngineFileSystemAccessRequest"}]}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineWebAuthUxRequest"}, {"name": "QML.Element", "value": "WebEngineWebAuthUxRequest"}, {"name": "QML.AddedInVersion", "value": "1543"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEngineWebAuthUxRequest", "gadget": true, "lineNumber": 226, "qualifiedClassName": "ForeignWebEngineWebAuthUxRequest"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineWebAuthPinRequest"}, {"name": "QML.Element", "value": "webEngineWebAuthPinRequest"}, {"name": "QML.AddedInVersion", "value": "1544"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeginWebEngineWebAuthPinRequest", "gadget": true, "lineNumber": 235, "qualifiedClassName": "ForeginWebEngineWebAuthPinRequest"}, {"className": "QWebEnginePermissionDerived", "gadget": true, "lineNumber": 246, "qualifiedClassName": "QWebEnginePermissionDerived", "superClasses": [{"access": "public", "name": "QWebEnginePermission"}]}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEnginePermission"}, {"name": "QML.Element", "value": "webEnginePermission"}, {"name": "QML.AddedInVersion", "value": "1544"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "ForeignWebEnginePermission", "gadget": true, "lineNumber": 259, "qualifiedClassName": "ForeignWebEnginePermission"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineLoadingInfoDerived"}, {"name": "QML.ForeignIsNamespace", "value": "true"}, {"name": "QML.Element", "value": "WebEngineLoadingInfo"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "ForeignWebEngineLoadingInfoNamespace", "lineNumber": 46, "namespace": true, "qualifiedClassName": "ForeignWebEngineLoadingInfoNamespace"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineCertificateErrorDerived"}, {"name": "QML.ForeignIsNamespace", "value": "true"}, {"name": "QML.Element", "value": "WebEngineCertificateError"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "ForeignWebEngineCertificateErrorNamespace", "lineNumber": 72, "namespace": true, "qualifiedClassName": "ForeignWebEngineCertificateErrorNamespace"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineScript"}, {"name": "QML.ForeignIsNamespace", "value": "true"}, {"name": "QML.Element", "value": "WebEngineScript"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "ForeignWebEngineScriptNamespace", "lineNumber": 101, "namespace": true, "qualifiedClassName": "ForeignWebEngineScriptNamespace"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEngineFileSystemAccessRequestDerived"}, {"name": "QML.ForeignIsNamespace", "value": "true"}, {"name": "QML.Element", "value": "WebEngineFileSystemAccessRequest"}, {"name": "QML.AddedInVersion", "value": "1540"}], "className": "ForeginWebEngineFileSystemAccessRequestNamespace", "lineNumber": 218, "namespace": true, "qualifiedClassName": "ForeginWebEngineFileSystemAccessRequestNamespace"}, {"classInfos": [{"name": "QML.Foreign", "value": "QWebEnginePermissionDerived"}, {"name": "QML.ForeignIsNamespace", "value": "true"}, {"name": "QML.Element", "value": "WebEnginePermission"}, {"name": "QML.AddedInVersion", "value": "1544"}], "className": "ForeignWebEnginePermissionNamespace", "lineNumber": 251, "namespace": true, "qualifiedClassName": "ForeignWebEnginePermissionNamespace"}], "inputFile": "qquickwebengineforeigntypes_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "WebEngineNewWindowRequest"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebEngineNewWindowRequest", "lineNumber": 26, "methods": [{"access": "public", "arguments": [{"type": "QQuickWebEngineView*"}], "index": 0, "name": "openIn", "returnType": "void"}], "object": true, "qualifiedClassName": "QQuickWebEngineNewWindowRequest", "superClasses": [{"access": "public", "name": "QWebEngineNewWindowRequest"}]}], "inputFile": "qquickwebenginenewwindowrequest_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "WebEngineProfile"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "QQuickWebEngineProfile", "enums": [{"isClass": false, "isFlag": false, "name": "HttpCacheType", "values": ["MemoryHttpCache", "DiskHttpCache", "NoCache"]}, {"isClass": false, "isFlag": false, "name": "PersistentCookiesPolicy", "values": ["NoPersistentCookies", "AllowPersistentCookies", "ForcePersistentCookies"]}, {"isClass": true, "isFlag": false, "name": "PersistentPermissionsPolicy", "type": "quint8", "values": ["AskEveryTime", "StoreInMemory", "StoreOnDisk"]}], "lineNumber": 27, "methods": [{"access": "public", "index": 18, "name": "clearHttpCache", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "QUrl"}, {"name": "permissionType", "type": "QWebEnginePermission::PermissionType"}], "index": 19, "isConst": true, "name": "queryPermission", "returnType": "QWebEnginePermission", "revision": 1544}, {"access": "public", "index": 20, "isConst": true, "name": "listAllPermissions", "returnType": "QList<QWebEnginePermission>", "revision": 1544}, {"access": "public", "arguments": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "QUrl"}], "index": 21, "isConst": true, "name": "listPermissionsForOrigin", "returnType": "QList<QWebEnginePermission>", "revision": 1544}, {"access": "public", "arguments": [{"name": "permissionType", "type": "QWebEnginePermission::PermissionType"}], "index": 22, "isConst": true, "name": "listPermissionsForPermissionType", "returnType": "QList<QWebEnginePermission>", "revision": 1544}], "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "storageName", "notify": "storageNameChanged", "read": "storageName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setStorageName"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "offTheRecord", "notify": "offTheRecordChanged", "read": "isOffTheRecord", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setOffTheRecord"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "persistentStoragePath", "notify": "persistentStoragePathChanged", "read": "persistentStoragePath", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setPersistentStoragePath"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "cachePath", "notify": "cachePathChanged", "read": "cachePath", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "httpUserAgent", "notify": "httpUserAgentChanged", "read": "httpUserAgent", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setHttpUserAgent"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "httpCacheType", "notify": "httpCacheTypeChanged", "read": "httpCacheType", "required": false, "scriptable": true, "stored": true, "type": "HttpCacheType", "user": false, "write": "setHttpCacheType"}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "httpAcceptLanguage", "notify": "httpAcceptLanguageChanged", "read": "httpAcceptLanguage", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setHttpAcceptLanguage"}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "persistentCookiesPolicy", "notify": "persistentCookiesPolicyChanged", "read": "persistentCookiesPolicy", "required": false, "scriptable": true, "stored": true, "type": "PersistentCookiesPolicy", "user": false, "write": "setPersistentCookiesPolicy"}, {"constant": false, "designable": true, "final": true, "index": 8, "name": "persistentPermissionsPolicy", "notify": "persistentPermissionsPolicyChanged", "read": "persistentPermissionsPolicy", "required": false, "revision": 1544, "scriptable": true, "stored": true, "type": "PersistentPermissionsPolicy", "user": false, "write": "setPersistentPermissionsPolicy"}, {"constant": false, "designable": true, "final": true, "index": 9, "name": "httpCacheMaximumSize", "notify": "httpCacheMaximumSizeChanged", "read": "httpCacheMaximumSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHttpCacheMaximumSize"}, {"constant": false, "designable": true, "final": true, "index": 10, "name": "spellCheckLanguages", "notify": "spellCheckLanguagesChanged", "read": "spellCheckLanguages", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setSpellCheckLanguages"}, {"constant": false, "designable": true, "final": true, "index": 11, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>nabled", "notify": "<PERSON><PERSON><PERSON>ckEnabledC<PERSON>ed", "read": "isSpellCheckEnabled", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSpellCheckEnabled"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "userScripts", "read": "userScripts", "required": false, "scriptable": true, "stored": true, "type": "QQuickWebEngineScriptCollection*", "user": false}, {"constant": false, "designable": true, "final": true, "index": 13, "name": "downloadPath", "notify": "downloadPathChanged", "read": "downloadPath", "required": false, "revision": 261, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setDownloadPath"}, {"constant": false, "designable": true, "final": true, "index": 14, "name": "isPushServiceEnabled", "notify": "pushServiceEnabledChanged", "read": "isPushServiceEnabled", "required": false, "revision": 1541, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPushServiceEnabled"}, {"constant": false, "designable": true, "final": true, "index": 15, "name": "clientHints", "read": "clientHints", "required": false, "revision": 1544, "scriptable": true, "stored": true, "type": "QWebEngineClientHints*", "user": false}], "qualifiedClassName": "QQuickWebEngineProfile", "signals": [{"access": "public", "index": 0, "name": "storageNameChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "offTheRecordChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "persistentStoragePathChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "cachePathChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "httpUserAgentChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "httpCacheTypeChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "persistentCookiesPolicyChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "httpCacheMaximumSizeChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "httpAcceptLanguageChanged", "returnType": "void", "revision": 257}, {"access": "public", "index": 9, "name": "spellCheckLanguagesChanged", "returnType": "void", "revision": 259}, {"access": "public", "index": 10, "name": "<PERSON><PERSON><PERSON>ckEnabledC<PERSON>ed", "returnType": "void", "revision": 259}, {"access": "public", "index": 11, "name": "downloadPathChanged", "returnType": "void", "revision": 261}, {"access": "public", "index": 12, "name": "pushServiceEnabledChanged", "returnType": "void", "revision": 1541}, {"access": "public", "index": 13, "name": "clearHttpCacheCompleted", "returnType": "void", "revision": 1543}, {"access": "public", "index": 14, "name": "persistentPermissionsPolicyChanged", "returnType": "void", "revision": 1544}, {"access": "public", "arguments": [{"name": "download", "type": "QQuickWebEngineDownloadRequest*"}], "index": 15, "name": "downloadRequested", "returnType": "void"}, {"access": "public", "arguments": [{"name": "download", "type": "QQuickWebEngineDownloadRequest*"}], "index": 16, "name": "downloadFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "notification", "type": "QWebEngineNotification*"}], "index": 17, "name": "presentNotification", "returnType": "void", "revision": 261}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickwebengineprofile.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "WebEngineProfilePrototype"}, {"name": "QML.AddedInVersion", "value": "1545"}], "className": "QQuickWebEngineProfilePrototype", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 27, "methods": [{"access": "public", "index": 0, "name": "instance", "returnType": "QQuickWebEngineProfile*"}], "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "storageName", "read": "storageName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setStorageName"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "persistentStoragePath", "read": "persistentStoragePath", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setPersistentStoragePath"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "cachePath", "read": "cachePath", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "httpCacheType", "read": "httpCacheType", "required": false, "scriptable": true, "stored": true, "type": "QQuickWebEngineProfile::HttpCacheType", "user": false, "write": "setHttpCacheType"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "persistentCookiesPolicy", "read": "persistentCookiesPolicy", "required": false, "scriptable": true, "stored": true, "type": "QQuickWebEngineProfile::PersistentCookiesPolicy", "user": false, "write": "setPersistentCookiesPolicy"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "httpCacheMaximumSize", "read": "httpCacheMaximumSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHttpCacheMaximumSize"}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "persistentPermissionsPolicy", "read": "persistentPermissionsPolicy", "required": false, "scriptable": true, "stored": true, "type": "QQuickWebEngineProfile::PersistentPermissionsPolicy", "user": false, "write": "setPersistentPermissionsPolicy"}], "qualifiedClassName": "QQuickWebEngineProfilePrototype", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquickwebengineprofileprototype_p.h", "outputRevision": 69}, {"classes": [{"className": "QQuickWebEngineScriptCollection", "lineNumber": 29, "methods": [{"access": "public", "arguments": [{"name": "value", "type": "QWebEngineScript"}], "index": 1, "isConst": true, "name": "contains", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 2, "isConst": true, "name": "find", "returnType": "QList<QWebEngineScript>"}, {"access": "public", "arguments": [{"type": "QWebEngineScript"}], "index": 3, "name": "insert", "returnType": "void"}, {"access": "public", "arguments": [{"name": "list", "type": "QList<QWebEngineScript>"}], "index": 4, "name": "insert", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QWebEngineScript"}], "index": 5, "name": "remove", "returnType": "bool"}, {"access": "public", "index": 6, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "collection", "notify": "collectionChanged", "read": "collection", "required": false, "scriptable": true, "stored": true, "type": "QJSValue", "user": false, "write": "setCollection"}], "qualifiedClassName": "QQuickWebEngineScriptCollection", "signals": [{"access": "public", "index": 0, "name": "collectionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickwebenginescriptcollection_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "WebEngineSettings"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebEngineSettings", "enums": [{"isClass": false, "isFlag": false, "name": "UnknownUrlSchemePolicy", "values": ["DisallowUnknownUrlSchemes", "AllowUnknownUrlSchemesFromUserInteraction", "AllowAllUnknownUrlSchemes"]}, {"isClass": true, "isFlag": false, "name": "ImageAnimationPolicy", "type": "uint8_t", "values": ["Allow", "AnimateOnce", "Disallow"]}], "lineNumber": 26, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "autoLoadImages", "notify": "autoLoadImagesChanged", "read": "autoLoadImages", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoLoadImages"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "javascriptEnabled", "notify": "javascriptEnabledChanged", "read": "javascriptEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setJavascriptEnabled"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "javascriptCanOpenWindows", "notify": "javascriptCanOpenWindowsChanged", "read": "javascriptCanOpenWindows", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setJavascriptCanOpenWindows"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "javascriptCanAccessClipboard", "notify": "javascriptCanAccessClipboardChanged", "read": "javascriptCanAccessClipboard", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setJavascriptCanAccessClipboard"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "linksIncludedInFocusChain", "notify": "linksIncludedInFocusChainChanged", "read": "linksIncludedInFocusChain", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLinksIncludedInFocusChain"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "localStorageEnabled", "notify": "localStorageEnabledChanged", "read": "localStorageEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLocalStorageEnabled"}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "localContentCanAccessRemoteUrls", "notify": "localContentCanAccessRemoteUrlsChanged", "read": "localContentCanAccessRemoteUrls", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLocalContentCanAccessRemoteUrls"}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "spatialNavigationEnabled", "notify": "spatialNavigationEnabledChanged", "read": "spatialNavigationEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSpatialNavigationEnabled"}, {"constant": false, "designable": true, "final": true, "index": 8, "name": "localContentCanAccessFileUrls", "notify": "localContentCanAccessFileUrlsChanged", "read": "localContentCanAccessFileUrls", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLocalContentCanAccessFileUrls"}, {"constant": false, "designable": true, "final": true, "index": 9, "name": "hyperlinkAuditingEnabled", "notify": "hyperlinkAuditingEnabledChanged", "read": "hyperlinkAuditingEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHyperlinkAuditingEnabled"}, {"constant": false, "designable": true, "final": true, "index": 10, "name": "errorPageEnabled", "notify": "errorPageEnabledChanged", "read": "errorPageEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setErrorPageEnabled"}, {"constant": false, "designable": true, "final": true, "index": 11, "name": "pluginsEnabled", "notify": "pluginsEnabledChanged", "read": "pluginsEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPluginsEnabled"}, {"constant": false, "designable": true, "final": true, "index": 12, "name": "fullScreenSupportEnabled", "notify": "fullScreenSupportEnabledChanged", "read": "fullScreenSupportEnabled", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFullScreenSupportEnabled"}, {"constant": false, "designable": true, "final": true, "index": 13, "name": "defaultTextEncoding", "notify": "defaultTextEncodingChanged", "read": "defaultTextEncoding", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setDefaultTextEncoding"}, {"constant": false, "designable": true, "final": true, "index": 14, "name": "screenCaptureEnabled", "notify": "screenCaptureEnabledChanged", "read": "screenCaptureEnabled", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setScreenCaptureEnabled"}, {"constant": false, "designable": true, "final": true, "index": 15, "name": "webGLEnabled", "notify": "webGLEnabledChanged", "read": "webGLEnabled", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setWebGLEnabled"}, {"constant": false, "designable": true, "final": true, "index": 16, "name": "accelerated2dCanvasEnabled", "notify": "accelerated2dCanvasEnabledChanged", "read": "accelerated2dCanvasEnabled", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAccelerated2dCanvasEnabled"}, {"constant": false, "designable": true, "final": true, "index": 17, "name": "autoLoadIconsForPage", "notify": "autoLoadIconsForPageChanged", "read": "autoLoadIconsForPage", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoLoadIconsForPage"}, {"constant": false, "designable": true, "final": true, "index": 18, "name": "touchIconsEnabled", "notify": "touchIconsEnabledChanged", "read": "touchIconsEnabled", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setTouchIconsEnabled"}, {"constant": false, "designable": true, "final": true, "index": 19, "name": "focusOnNavigationEnabled", "notify": "focusOnNavigationEnabledChanged", "read": "focusOnNavigationEnabled", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFocusOnNavigationEnabled"}, {"constant": false, "designable": true, "final": true, "index": 20, "name": "printElementBackgrounds", "notify": "printElementBackgroundsChanged", "read": "printElementBackgrounds", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPrintElementBackgrounds"}, {"constant": false, "designable": true, "final": true, "index": 21, "name": "allowRunningInsecureContent", "notify": "allowRunningInsecureContentChanged", "read": "allowRunningInsecureContent", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAllowRunningInsecureContent"}, {"constant": false, "designable": true, "final": true, "index": 22, "name": "allowGeolocationOnInsecureOrigins", "notify": "allowGeolocationOnInsecureOriginsChanged", "read": "allowGeolocationOnInsecureOrigins", "required": false, "revision": 260, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAllowGeolocationOnInsecureOrigins"}, {"constant": false, "designable": true, "final": true, "index": 23, "name": "allowWindowActivationFromJavaScript", "notify": "allowWindowActivationFromJavaScriptChanged", "read": "allowWindowActivationFromJavaScript", "required": false, "revision": 261, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAllowWindowActivationFromJavaScript"}, {"constant": false, "designable": true, "final": true, "index": 24, "name": "showScrollBars", "notify": "showScrollBarsChanged", "read": "showScrollBars", "required": false, "revision": 261, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setShowScrollBars"}, {"constant": false, "designable": true, "final": true, "index": 25, "name": "unknownUrlSchemePolicy", "notify": "unknownUrlSchemePolicyChanged", "read": "unknownUrlSchemePolicy", "required": false, "revision": 262, "scriptable": true, "stored": true, "type": "UnknownUrlSchemePolicy", "user": false, "write": "setUnknownUrlSchemePolicy"}, {"constant": false, "designable": true, "final": true, "index": 26, "name": "playbackRequiresUserGesture", "notify": "playbackRequiresUserGestureChanged", "read": "playbackRequiresUserGesture", "required": false, "revision": 262, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPlaybackRequiresUserGesture"}, {"constant": false, "designable": true, "final": true, "index": 27, "name": "webRTCPublicInterfacesOnly", "notify": "webRTCPublicInterfacesOnlyChanged", "read": "webRTCPublicInterfacesOnly", "required": false, "revision": 262, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setWebRTCPublicInterfacesOnly"}, {"constant": false, "designable": true, "final": true, "index": 28, "name": "javascriptCanPaste", "notify": "javascriptCanPasteChanged", "read": "javascriptCanPaste", "required": false, "revision": 262, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setJavascriptCanPaste"}, {"constant": false, "designable": true, "final": true, "index": 29, "name": "dnsPrefetchEnabled", "notify": "dnsPrefetchEnabledChanged", "read": "dnsPrefetchEnabled", "required": false, "revision": 263, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setDnsPrefetchEnabled"}, {"constant": false, "designable": true, "final": true, "index": 30, "name": "pdfViewerEnabled", "notify": "pdfViewerEnabledChanged", "read": "pdfViewerEnabled", "required": false, "revision": 264, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPdfViewerEnabled"}, {"constant": false, "designable": true, "final": true, "index": 31, "name": "navigateOnDropEnabled", "notify": "navigateOnDropEnabledChanged", "read": "navigateOnDropEnabled", "required": false, "revision": 1540, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setNavigateOnDropEnabled"}, {"constant": false, "designable": true, "final": true, "index": 32, "name": "readingFromCanvasEnabled", "notify": "readingFromCanvasEnabledChanged", "read": "readingFromCanvasEnabled", "required": false, "revision": 1542, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReadingFromCanvasEnabled"}, {"constant": false, "designable": true, "final": true, "index": 33, "name": "forceDarkMode", "notify": "forceDarkModeChanged", "read": "forceDarkMode", "required": false, "revision": 1543, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setForceDarkMode"}, {"constant": false, "designable": true, "final": true, "index": 34, "name": "scrollAnimatorEnabled", "notify": "scrollAnimatorEnabledChanged", "read": "scrollAnimatorEnabled", "required": false, "revision": 1544, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setScrollAnimatorEnabled"}, {"constant": false, "designable": true, "final": true, "index": 35, "name": "imageAnimationPolicy", "notify": "imageAnimationPolicyChanged", "read": "imageAnimationPolicy", "required": false, "revision": 1544, "scriptable": true, "stored": true, "type": "ImageAnimationPolicy", "user": false, "write": "setImageAnimationPolicy"}, {"constant": false, "designable": true, "final": true, "index": 36, "name": "printHeaderAndFooter", "notify": "printHeaderAndFooterChanged", "read": "printHeaderAndFooter", "required": false, "revision": 1545, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPrintHeaderAndFooter"}, {"constant": false, "designable": true, "final": true, "index": 37, "name": "preferCSSMarginsForPrinting", "notify": "preferCSSMarginsForPrintingChanged", "read": "preferCSSMarginsForPrinting", "required": false, "revision": 1545, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPreferCSSMarginsForPrinting"}, {"constant": false, "designable": true, "final": true, "index": 38, "name": "touchEventsApiEnabled", "notify": "touchEventsApiEnabledChanged", "read": "touchEventsApiEnabled", "required": false, "revision": 1545, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setTouchEventsApiEnabled"}], "qualifiedClassName": "QQuickWebEngineSettings", "signals": [{"access": "public", "index": 0, "name": "autoLoadImagesChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "javascriptEnabledChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "javascriptCanOpenWindowsChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "javascriptCanAccessClipboardChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "linksIncludedInFocusChainChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "localStorageEnabledChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "localContentCanAccessRemoteUrlsChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "spatialNavigationEnabledChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "localContentCanAccessFileUrlsChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "hyperlinkAuditingEnabledChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "errorPageEnabledChanged", "returnType": "void"}, {"access": "public", "index": 11, "name": "pluginsEnabledChanged", "returnType": "void"}, {"access": "public", "index": 12, "name": "fullScreenSupportEnabledChanged", "returnType": "void", "revision": 257}, {"access": "public", "index": 13, "name": "defaultTextEncodingChanged", "returnType": "void"}, {"access": "public", "index": 14, "name": "screenCaptureEnabledChanged", "returnType": "void", "revision": 258}, {"access": "public", "index": 15, "name": "webGLEnabledChanged", "returnType": "void", "revision": 258}, {"access": "public", "index": 16, "name": "accelerated2dCanvasEnabledChanged", "returnType": "void", "revision": 258}, {"access": "public", "index": 17, "name": "autoLoadIconsForPageChanged", "returnType": "void", "revision": 258}, {"access": "public", "index": 18, "name": "touchIconsEnabledChanged", "returnType": "void", "revision": 258}, {"access": "public", "index": 19, "name": "focusOnNavigationEnabledChanged", "returnType": "void", "revision": 259}, {"access": "public", "index": 20, "name": "printElementBackgroundsChanged", "returnType": "void", "revision": 259}, {"access": "public", "index": 21, "name": "allowRunningInsecureContentChanged", "returnType": "void", "revision": 259}, {"access": "public", "index": 22, "name": "allowGeolocationOnInsecureOriginsChanged", "returnType": "void", "revision": 260}, {"access": "public", "index": 23, "name": "allowWindowActivationFromJavaScriptChanged", "returnType": "void", "revision": 261}, {"access": "public", "index": 24, "name": "showScrollBarsChanged", "returnType": "void", "revision": 261}, {"access": "public", "index": 25, "name": "unknownUrlSchemePolicyChanged", "returnType": "void", "revision": 262}, {"access": "public", "index": 26, "name": "playbackRequiresUserGestureChanged", "returnType": "void", "revision": 262}, {"access": "public", "index": 27, "name": "webRTCPublicInterfacesOnlyChanged", "returnType": "void", "revision": 262}, {"access": "public", "index": 28, "name": "javascriptCanPasteChanged", "returnType": "void", "revision": 262}, {"access": "public", "index": 29, "name": "dnsPrefetchEnabledChanged", "returnType": "void", "revision": 263}, {"access": "public", "index": 30, "name": "pdfViewerEnabledChanged", "returnType": "void", "revision": 264}, {"access": "public", "index": 31, "name": "navigateOnDropEnabledChanged", "returnType": "void", "revision": 1540}, {"access": "public", "index": 32, "name": "readingFromCanvasEnabledChanged", "returnType": "void", "revision": 1542}, {"access": "public", "index": 33, "name": "forceDarkModeChanged", "returnType": "void", "revision": 1543}, {"access": "public", "index": 34, "name": "scrollAnimatorEnabledChanged", "returnType": "void", "revision": 1544}, {"access": "public", "index": 35, "name": "imageAnimationPolicyChanged", "returnType": "void", "revision": 1544}, {"access": "public", "index": 36, "name": "printHeaderAndFooterChanged", "returnType": "void", "revision": 1545}, {"access": "public", "index": 37, "name": "preferCSSMarginsForPrintingChanged", "returnType": "void", "revision": 1545}, {"access": "public", "index": 38, "name": "touchEventsApiEnabledChanged", "returnType": "void", "revision": 1545}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickwebenginesettings_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Singleton", "value": "true"}, {"name": "QML.Element", "value": "WebEngine"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "QQuickWebEngineSingleton", "lineNumber": 28, "methods": [{"access": "public", "index": 0, "isConst": true, "name": "script", "returnType": "QWebEngineScript"}], "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "settings", "read": "settings", "required": false, "scriptable": true, "stored": true, "type": "QQuickWebEngineSettings*", "user": false}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "defaultProfile", "read": "defaultProfile", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QQuickWebEngineProfile*", "user": false}], "qualifiedClassName": "QQuickWebEngineSingleton", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickwebenginesingleton_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "TouchSelectionMenuRequest"}, {"name": "QML.AddedInVersion", "value": "1539"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": ""}], "className": "QQuickWebEngineTouchSelectionMenuRequest", "enums": [{"alias": "TouchSelectionCommandFlag", "isClass": false, "isFlag": true, "name": "TouchSelectionCommandFlags", "values": ["Cut", "Copy", "Paste"]}], "lineNumber": 31, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "accepted", "read": "isAccepted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAccepted"}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "selectionBounds", "read": "selectionBounds", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "QRect", "user": false}, {"constant": true, "designable": true, "final": true, "index": 2, "name": "touchSelectionCommandFlags", "read": "touchSelectionCommandFlags", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "TouchSelectionCommandFlags", "user": false}], "qualifiedClassName": "QQuickWebEngineTouchSelectionMenuRequest", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquickwebenginetouchselectionmenurequest_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "RegisterEnumClassesUnscoped", "value": "false"}, {"name": "QML.Element", "value": "WebEngineView"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "QQuickWebEngineView", "enums": [{"isClass": false, "isFlag": false, "name": "NavigationRequestAction", "values": ["AcceptRequest", "IgnoreRequest"]}, {"isClass": false, "isFlag": false, "name": "NavigationType", "values": ["LinkClickedNavigation", "TypedNavigation", "FormSubmittedNavigation", "BackForwardNavigation", "ReloadNavigation", "OtherNavigation", "RedirectNavigation"]}, {"isClass": false, "isFlag": false, "name": "LoadStatus", "values": ["LoadStartedStatus", "LoadStoppedStatus", "LoadSucceededStatus", "LoadFailedStatus"]}, {"isClass": false, "isFlag": false, "name": "ErrorDomain", "values": ["NoErrorDomain", "InternalErrorDomain", "ConnectionErrorDomain", "CertificateErrorDomain", "HttpErrorDomain", "FtpErrorDomain", "DnsErrorDomain"]}, {"isClass": false, "isFlag": false, "name": "NewViewDestination", "values": ["NewViewInWindow", "NewViewInTab", "NewViewInDialog", "NewViewInBackgroundTab"]}, {"isClass": false, "isFlag": false, "name": "Feature", "values": ["MediaAudioCapture", "MediaVideoCapture", "MediaAudioVideoCapture", "Geolocation", "DesktopVideoCapture", "DesktopAudioVideoCapture", "Notifications", "ClipboardReadWrite", "LocalFontsAccess"]}, {"isClass": false, "isFlag": false, "name": "WebAction", "values": ["NoWebAction", "Back", "Forward", "Stop", "Reload", "Cut", "Copy", "Paste", "Undo", "Redo", "SelectAll", "ReloadAndBypassCache", "PasteAndMatchStyle", "OpenLinkInThisWindow", "OpenLinkInNewWindow", "OpenLinkInNewTab", "CopyLinkToClipboard", "DownloadLinkToDisk", "CopyImageToClipboard", "CopyImageUrlToClipboard", "DownloadImageToDisk", "CopyMediaUrlToClipboard", "ToggleMediaControls", "ToggleMediaLoop", "ToggleMediaPlayPause", "ToggleMediaMute", "DownloadMediaToDisk", "InspectElement", "ExitFullScreen", "RequestClose", "Unselect", "SavePage", "OpenLinkInNewBackgroundTab", "ViewSource", "ToggleBold", "ToggleItalic", "ToggleUnderline", "ToggleStrikethrough", "AlignLeft", "AlignCenter", "AlignRight", "AlignJustified", "Indent", "Outdent", "InsertOrderedList", "InsertUnorderedList", "ChangeTextDirectionLTR", "ChangeTextDirectionRTL", "WebActionCount"]}, {"isClass": false, "isFlag": false, "name": "JavaScriptConsoleMessageLevel", "values": ["InfoMessageLevel", "WarningMessageLevel", "ErrorMessageLevel"]}, {"isClass": false, "isFlag": false, "name": "RenderProcessTerminationStatus", "values": ["NormalTerminationStatus", "AbnormalTerminationStatus", "CrashedTerminationStatus", "KilledTerminationStatus"]}, {"alias": "FindFlag", "isClass": false, "isFlag": true, "name": "FindFlags", "values": ["FindBackward", "FindCaseSensitively"]}, {"isClass": false, "isFlag": false, "name": "PrintedPageSizeId", "values": ["Letter", "Legal", "Executive", "A0", "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "A10", "B0", "B1", "B2", "B3", "B4", "B5", "B6", "B7", "B8", "B9", "B10", "C5E", "Comm10E", "DLE", "Folio", "Ledger", "Tabloid", "Custom", "A3Extra", "A4Extra", "A4Plus", "A4Small", "A5Extra", "B5Extra", "JisB0", "JisB1", "JisB2", "JisB3", "JisB4", "JisB5", "JisB6", "JisB7", "JisB8", "JisB9", "JisB10", "AnsiC", "AnsiD", "AnsiE", "LegalExtra", "LetterExtra", "LetterPlus", "LetterSmall", "TabloidExtra", "ArchA", "ArchB", "ArchC", "ArchD", "ArchE", "Imperial7x9", "Imperial8x10", "Imperial9x11", "Imperial9x12", "Imperial10x11", "Imperial10x13", "Imperial10x14", "Imperial12x11", "Imperial15x11", "ExecutiveStandard", "Note", "Quarto", "Statement", "SuperA", "SuperB", "Postcard", "DoublePostcard", "Prc16K", "Prc32K", "Prc32KBig", "FanFoldUS", "FanFoldGerman", "FanFoldGermanLegal", "EnvelopeB4", "EnvelopeB5", "EnvelopeB6", "EnvelopeC0", "EnvelopeC1", "EnvelopeC2", "EnvelopeC3", "EnvelopeC4", "EnvelopeC6", "EnvelopeC65", "EnvelopeC7", "Envelope9", "Envelope11", "Envelope12", "Envelope14", "EnvelopeMonarch", "EnvelopePersonal", "EnvelopeChou3", "EnvelopeChou4", "EnvelopeInvite", "EnvelopeItalian", "EnvelopeKaku2", "EnvelopeKaku3", "EnvelopePrc1", "EnvelopePrc2", "EnvelopePrc3", "EnvelopePrc4", "EnvelopePrc5", "EnvelopePrc6", "EnvelopePrc7", "EnvelopePrc8", "EnvelopePrc9", "EnvelopePrc10", "EnvelopeYou4", "LastPageSize", "AnsiA", "AnsiB", "EnvelopeC5", "EnvelopeDL", "Envelope10"]}, {"isClass": false, "isFlag": false, "name": "PrintedPageOrientation", "values": ["Portrait", "Landscape"]}, {"isClass": true, "isFlag": false, "name": "LifecycleState", "values": ["Active", "Frozen", "Discarded"]}], "lineNumber": 59, "methods": [{"access": "public", "arguments": [{"name": "action", "type": "WebAction"}], "index": 81, "name": "action", "returnType": "QQuickWebEngineAction*", "revision": 264}, {"access": "public", "arguments": [{"name": "request", "type": "QWebEngineNewWindowRequest*"}], "index": 82, "name": "acceptAsNewWindow", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 83, "name": "findFrameByName", "returnType": "QWebEngineFrame", "revision": 1544}], "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "url", "notify": "url<PERSON><PERSON><PERSON>", "read": "url", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setUrl"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "icon", "notify": "iconChanged", "read": "icon", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "loading", "notify": "loadingChanged", "read": "isLoading", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "loadProgress", "notify": "loadProgressChanged", "read": "loadProgress", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "title", "notify": "titleChanged", "read": "title", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "canGoBack", "notify": "canGoBackChanged", "read": "canGoBack", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "canGoForward", "notify": "canGoForwardChanged", "read": "canGoForward", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "isFullScreen", "notify": "isFullScreenChanged", "read": "isFullScreen", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": true, "index": 8, "name": "zoomFactor", "notify": "zoomFactorChanged", "read": "zoomFactor", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setZoomFactor"}, {"constant": false, "designable": true, "final": true, "index": 9, "name": "profile", "notify": "profileChanged", "read": "profile", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QQuickWebEngineProfile*", "user": false, "write": "setProfile"}, {"constant": true, "designable": true, "final": true, "index": 10, "name": "settings", "read": "settings", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QQuickWebEngineSettings*", "user": false}, {"constant": true, "designable": true, "final": true, "index": 11, "name": "history", "read": "history", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QWebEngineHistory*", "user": false}, {"constant": false, "designable": true, "final": true, "index": 12, "name": "webChannel", "notify": "webChannelChanged", "read": "webChannel", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QQmlWebChannel*", "user": false, "write": "setWebChannel"}, {"constant": false, "designable": true, "final": true, "index": 13, "name": "userScripts", "read": "userScripts", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QQuickWebEngineScriptCollection*", "user": false}, {"constant": false, "designable": true, "final": true, "index": 14, "name": "activeFocusOnPress", "notify": "activeFocusOnPressChanged", "read": "activeFocusOnPress", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setActiveFocusOnPress"}, {"constant": false, "designable": true, "final": true, "index": 15, "name": "backgroundColor", "notify": "backgroundColorChanged", "read": "backgroundColor", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBackgroundColor"}, {"constant": false, "designable": true, "final": true, "index": 16, "name": "contentsSize", "notify": "contentsSizeChanged", "read": "contentsSize", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "QSizeF", "user": false}, {"constant": false, "designable": true, "final": true, "index": 17, "name": "scrollPosition", "notify": "scrollPositionChanged", "read": "scrollPosition", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": false, "designable": true, "final": true, "index": 18, "name": "audioMuted", "notify": "audioMutedChanged", "read": "isAudioMuted", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAudioMuted"}, {"constant": false, "designable": true, "final": true, "index": 19, "name": "recentlyAudible", "notify": "recentlyAudibleChanged", "read": "recentlyAudible", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": true, "index": 20, "name": "webChannelWorld", "notify": "webChannelWorldChanged", "read": "webChannelWorld", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "uint", "user": false, "write": "setWebChannelWorld"}, {"constant": false, "designable": true, "final": true, "index": 21, "name": "inspectedView", "notify": "inspectedViewChanged", "read": "inspectedView", "required": false, "revision": 263, "scriptable": true, "stored": true, "type": "QQuickWebEngineView*", "user": false, "write": "setInspectedView"}, {"constant": false, "designable": true, "final": true, "index": 22, "name": "devToolsView", "notify": "devToolsViewChanged", "read": "devToolsView", "required": false, "revision": 263, "scriptable": true, "stored": true, "type": "QQuickWebEngineView*", "user": false, "write": "setDevToolsView"}, {"constant": true, "designable": true, "final": true, "index": 23, "name": "devToolsId", "read": "devToolsId", "required": false, "revision": 1542, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": true, "index": 24, "name": "lifecycleState", "notify": "lifecycleStateChanged", "read": "lifecycleState", "required": false, "revision": 266, "scriptable": true, "stored": true, "type": "LifecycleState", "user": false, "write": "setLifecycleState"}, {"constant": false, "designable": true, "final": true, "index": 25, "name": "recommendedState", "notify": "recommendedStateChanged", "read": "recommendedState", "required": false, "revision": 266, "scriptable": true, "stored": true, "type": "LifecycleState", "user": false}, {"constant": false, "designable": true, "final": true, "index": 26, "name": "renderProcessPid", "notify": "renderProcessPidChanged", "read": "renderProcessPid", "required": false, "revision": 267, "scriptable": true, "stored": true, "type": "qint64", "user": false}, {"constant": false, "designable": true, "final": true, "index": 27, "name": "touchHandleDelegate", "notify": "touchHandleDelegateChanged", "read": "touchHandleDelegate", "required": false, "revision": 65280, "scriptable": true, "stored": true, "type": "QQmlComponent*", "user": false, "write": "setTouchHandleDelegate"}, {"constant": false, "designable": true, "final": true, "index": 28, "name": "mainFrame", "read": "mainFrame", "required": false, "revision": 1544, "scriptable": true, "stored": true, "type": "QWebEngineFrame", "user": false}], "qualifiedClassName": "QQuickWebEngineView", "signals": [{"access": "public", "index": 0, "name": "titleChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "url<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 2, "name": "iconChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "loadingInfo", "type": "QWebEngineLoadingInfo"}], "index": 3, "name": "loadingChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "loadProgressChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hoveredUrl", "type": "QUrl"}], "index": 5, "name": "linkHovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "request", "type": "QWebEngineNavigationRequest*"}], "index": 6, "name": "navigationRequested", "returnType": "void"}, {"access": "public", "arguments": [{"name": "level", "type": "QQuickWebEngineView::JavaScriptConsoleMessageLevel"}, {"name": "message", "type": "QString"}, {"name": "lineNumber", "type": "int"}, {"name": "sourceID", "type": "QString"}], "index": 7, "name": "javaScriptConsoleMessage", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QWebEngineCertificateError"}], "index": 8, "name": "certificateError", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "request", "type": "QWebEngineFullScreenRequest"}], "index": 9, "name": "fullScreenRequested", "returnType": "void", "revision": 257}, {"access": "public", "index": 10, "name": "isFullScreenChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "QUrl"}, {"name": "feature", "type": "QQuickWebEngineView::Feature"}], "index": 11, "name": "featurePermissionRequested", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "arg", "type": "qreal"}], "index": 12, "name": "zoomFactorChanged", "returnType": "void", "revision": 257}, {"access": "public", "index": 13, "name": "profileChanged", "returnType": "void", "revision": 257}, {"access": "public", "index": 14, "name": "webChannelChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"type": "bool"}], "index": 15, "name": "activeFocusOnPressChanged", "returnType": "void", "revision": 258}, {"access": "public", "index": 16, "name": "backgroundColorChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "terminationStatus", "type": "QQuickWebEngineView::RenderProcessTerminationStatus"}, {"name": "exitCode", "type": "int"}], "index": 17, "name": "renderProcessTerminated", "returnType": "void", "revision": 258}, {"access": "public", "index": 18, "name": "windowCloseRequested", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "size", "type": "QSizeF"}], "index": 19, "name": "contentsSizeChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "position", "type": "QPointF"}], "index": 20, "name": "scrollPositionChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "muted", "type": "bool"}], "index": 21, "name": "audioMutedChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "recentlyAudible", "type": "bool"}], "index": 22, "name": "recentlyAudibleChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"type": "uint"}], "index": 23, "name": "webChannelWorldChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "request", "type": "QWebEngineContextMenuRequest*"}], "index": 24, "name": "contextMenuRequested", "returnType": "void", "revision": 260}, {"access": "public", "arguments": [{"name": "request", "type": "QQuickWebEngineAuthenticationDialogRequest*"}], "index": 25, "name": "authenticationDialogRequested", "returnType": "void", "revision": 260}, {"access": "public", "arguments": [{"name": "request", "type": "QQuickWebEngineJavaScriptDialogRequest*"}], "index": 26, "name": "javaScriptDialogRequested", "returnType": "void", "revision": 260}, {"access": "public", "arguments": [{"name": "request", "type": "QQuickWebEngineColorDialogRequest*"}], "index": 27, "name": "colorDialogRequested", "returnType": "void", "revision": 260}, {"access": "public", "arguments": [{"name": "request", "type": "QQuickWebEngineFileDialogRequest*"}], "index": 28, "name": "fileDialogRequested", "returnType": "void", "revision": 260}, {"access": "public", "arguments": [{"name": "filePath", "type": "QString"}, {"name": "success", "type": "bool"}], "index": 29, "name": "pdfPrintingFinished", "returnType": "void", "revision": 261}, {"access": "public", "arguments": [{"name": "request", "type": "QWebEngineQuotaRequest"}], "index": 30, "name": "quotaRequested", "returnType": "void", "revision": 263}, {"access": "public", "arguments": [{"name": "geometry", "type": "QRect"}, {"name": "frameGeometry", "type": "QRect"}], "index": 31, "name": "geometryChangeRequested", "returnType": "void", "revision": 263}, {"access": "public", "index": 32, "name": "inspectedViewChanged", "returnType": "void", "revision": 263}, {"access": "public", "index": 33, "name": "devToolsViewChanged", "returnType": "void", "revision": 263}, {"access": "public", "arguments": [{"name": "request", "type": "QWebEngineRegisterProtocolHandlerRequest"}], "index": 34, "name": "registerProtocolHandlerRequested", "returnType": "void", "revision": 263}, {"access": "public", "index": 35, "name": "printRequested", "returnType": "void", "revision": 264}, {"access": "public", "arguments": [{"name": "clientCertSelection", "type": "QQuickWebEngineClientCertificateSelection*"}], "index": 36, "name": "selectClientCertificate", "returnType": "void", "revision": 265}, {"access": "public", "arguments": [{"name": "request", "type": "QQuickWebEngineTooltipRequest*"}], "index": 37, "name": "tooltipRequested", "returnType": "void", "revision": 266}, {"access": "public", "arguments": [{"name": "state", "type": "QQuickWebEngineView::LifecycleState"}], "index": 38, "name": "lifecycleStateChanged", "returnType": "void", "revision": 266}, {"access": "public", "arguments": [{"name": "state", "type": "QQuickWebEngineView::LifecycleState"}], "index": 39, "name": "recommendedStateChanged", "returnType": "void", "revision": 266}, {"access": "public", "arguments": [{"name": "result", "type": "QWebEngineFindTextResult"}], "index": 40, "name": "findTextFinished", "returnType": "void", "revision": 266}, {"access": "public", "arguments": [{"name": "pid", "type": "qint64"}], "index": 41, "name": "renderProcessPidChanged", "returnType": "void", "revision": 267}, {"access": "public", "index": 42, "name": "canGoBackChanged", "returnType": "void", "revision": 267}, {"access": "public", "index": 43, "name": "canGoForwardChanged", "returnType": "void", "revision": 267}, {"access": "public", "arguments": [{"name": "request", "type": "QQuickWebEngineNewWindowRequest*"}], "index": 44, "name": "newWindowRequested", "returnType": "void", "revision": 268}, {"access": "public", "arguments": [{"name": "request", "type": "QQuickWebEngineTouchSelectionMenuRequest*"}], "index": 45, "name": "touchSelectionMenuRequested", "returnType": "void", "revision": 1539}, {"access": "public", "index": 46, "name": "touchHandleDelegateChanged", "returnType": "void", "revision": 1540}, {"access": "public", "arguments": [{"name": "request", "type": "QWebEngineFileSystemAccessRequest"}], "index": 47, "name": "fileSystemAccessRequested", "returnType": "void", "revision": 1540}, {"access": "public", "arguments": [{"name": "request", "type": "QWebEngineWebAuthUxRequest*"}], "index": 48, "name": "webAuthUxRequested", "returnType": "void", "revision": 1543}, {"access": "public", "arguments": [{"name": "request", "type": "QWebEngineDesktopMediaRequest"}], "index": 49, "name": "desktopMediaRequested", "returnType": "void", "revision": 1543}, {"access": "public", "arguments": [{"name": "frame", "type": "QWebEngineFrame"}], "index": 50, "name": "printRequestedByFrame", "returnType": "void", "revision": 1544}, {"access": "public", "arguments": [{"name": "permissionRequest", "type": "QWebEnginePermission"}], "index": 51, "name": "permissionRequested", "returnType": "void", "revision": 1544}], "slots": [{"access": "public", "arguments": [{"type": "QString"}, {"type": "QJSValue"}], "index": 52, "name": "runJavaScript", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QString"}], "index": 53, "isCloned": true, "name": "runJavaScript", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QString"}, {"name": "worldId", "type": "quint32"}, {"type": "QJSValue"}], "index": 54, "name": "runJavaScript", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"type": "QString"}, {"name": "worldId", "type": "quint32"}], "index": 55, "isCloned": true, "name": "runJavaScript", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "html", "type": "QString"}, {"name": "baseUrl", "type": "QUrl"}], "index": 56, "name": "loadHtml", "returnType": "void"}, {"access": "public", "arguments": [{"name": "html", "type": "QString"}], "index": 57, "isCloned": true, "name": "loadHtml", "returnType": "void"}, {"access": "public", "index": 58, "name": "goBack", "returnType": "void"}, {"access": "public", "index": 59, "name": "goForward", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 60, "name": "goBackOrForward", "returnType": "void", "revision": 257}, {"access": "public", "index": 61, "name": "reload", "returnType": "void"}, {"access": "public", "index": 62, "name": "reloadAndBypassCache", "returnType": "void", "revision": 257}, {"access": "public", "index": 63, "name": "stop", "returnType": "void"}, {"access": "public", "arguments": [{"name": "subString", "type": "QString"}, {"name": "options", "type": "FindFlags"}, {"name": "callback", "type": "QJSValue"}], "index": 64, "name": "findText", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "subString", "type": "QString"}, {"name": "options", "type": "FindFlags"}], "index": 65, "isCloned": true, "name": "findText", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "subString", "type": "QString"}], "index": 66, "isCloned": true, "name": "findText", "returnType": "void", "revision": 257}, {"access": "public", "index": 67, "name": "fullScreenCancelled", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "QUrl"}, {"type": "QQuickWebEngineView::Feature"}, {"name": "granted", "type": "bool"}], "index": 68, "name": "grantFeaturePermission", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "arg", "type": "bool"}], "index": 69, "name": "setActiveFocusOnPress", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "action", "type": "WebAction"}], "index": 70, "name": "triggerWebAction", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "filePath", "type": "QString"}, {"name": "pageSizeId", "type": "PrintedPageSizeId"}, {"name": "orientation", "type": "PrintedPageOrientation"}], "index": 71, "name": "printToPdf", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "filePath", "type": "QString"}, {"name": "pageSizeId", "type": "PrintedPageSizeId"}], "index": 72, "isCloned": true, "name": "printToPdf", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "filePath", "type": "QString"}], "index": 73, "isCloned": true, "name": "printToPdf", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "callback", "type": "QJSValue"}, {"name": "pageSizeId", "type": "PrintedPageSizeId"}, {"name": "orientation", "type": "PrintedPageOrientation"}], "index": 74, "name": "printToPdf", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "callback", "type": "QJSValue"}, {"name": "pageSizeId", "type": "PrintedPageSizeId"}], "index": 75, "isCloned": true, "name": "printToPdf", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "callback", "type": "QJSValue"}], "index": 76, "isCloned": true, "name": "printToPdf", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "replacement", "type": "QString"}], "index": 77, "name": "replaceMisspelledWord", "returnType": "void", "revision": 260}, {"access": "public", "arguments": [{"name": "filePath", "type": "QString"}, {"name": "format", "type": "QWebEngineDownloadRequest::SavePageFormat"}], "index": 78, "isConst": true, "name": "save", "returnType": "void", "revision": 1542}, {"access": "public", "arguments": [{"name": "filePath", "type": "QString"}], "index": 79, "isCloned": true, "isConst": true, "name": "save", "returnType": "void", "revision": 1542}, {"access": "private", "index": 80, "name": "lazyInitialize", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qquickwebengineview_p.h", "outputRevision": 69}]