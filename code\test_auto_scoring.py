#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for auto scoring functionality
"""

import sys
import time
from PySide6.QtWidgets import QApplication
from gui_main import MainWindow

def test_auto_scoring():
    """Test auto scoring functionality"""
    app = QApplication(sys.argv)

    # Create main window
    window = MainWindow()
    window.show()

    print("Testing auto scoring functionality...")
    print("1. Window created successfully")

    # Test checkbox state
    checkbox = window.auto_scoring_checkbox
    status_label = window.auto_scoring_status

    print(f"2. Initial checkbox state: {checkbox.isChecked()}")
    print(f"3. Initial status text: '{status_label.text()}'")
    print(f"4. Initial VideoThread auto_scoring_enabled: {window.video_thread.auto_scoring_enabled}")

    # Test direct method call first
    print("\n5. Testing direct method call...")
    from PySide6.QtCore import Qt
    window.on_auto_scoring_toggled(Qt.Checked)
    print(f"   - status text after direct call: '{status_label.text()}'")
    print(f"   - VideoThread auto_scoring_enabled: {window.video_thread.auto_scoring_enabled}")

    # Reset
    window.on_auto_scoring_toggled(Qt.Unchecked)
    print(f"   - status text after reset: '{status_label.text()}'")
    print(f"   - VideoThread auto_scoring_enabled: {window.video_thread.auto_scoring_enabled}")

    # Test checkbox signal
    print("\n6. Testing checkbox signal...")
    print("   - Setting checkbox to True...")
    checkbox.setChecked(True)
    app.processEvents()  # 處理事件
    print(f"   - checkbox state: {checkbox.isChecked()}")
    print(f"   - status text: '{status_label.text()}'")
    print(f"   - VideoThread auto_scoring_enabled: {window.video_thread.auto_scoring_enabled}")

    print("\n7. Testing checkbox signal (disable)...")
    print("   - Setting checkbox to False...")
    checkbox.setChecked(False)
    app.processEvents()  # 處理事件
    print(f"   - checkbox state: {checkbox.isChecked()}")
    print(f"   - status text: '{status_label.text()}'")
    print(f"   - VideoThread auto_scoring_enabled: {window.video_thread.auto_scoring_enabled}")

    print("\nTest completed successfully!")

    # Keep window open for manual testing
    return app.exec()

if __name__ == "__main__":
    test_auto_scoring()
