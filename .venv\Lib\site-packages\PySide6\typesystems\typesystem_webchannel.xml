<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtWebChannel"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
  <load-typesystem name="typesystem_core.xml" generate="no"/>

  <object-type name="QWebChannel"/>
  <object-type name="QWebChannelAbstractTransport">
    <extra-includes>
      <include file-name="QJsonObject" location="global"/>
    </extra-includes>
  </object-type>
  <!-- Not sure if this will be useful, but commented out for now because
       the QML module is not yet wrapped.
  <object-type name="QQmlWebChannel"/> -->

  <!-- QtQml/QtNetwork are pulled in via QtQtWebChannelDepends. -->
  <suppress-warning text="^Scoped enum 'Q(Ocsp)|(Dtls)|(Qml).*' does not have a type entry.*$"/>

</typesystem>
