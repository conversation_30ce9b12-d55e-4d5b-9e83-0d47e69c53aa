<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtSvgWidgets" doc-package="PySide6.QtSvg"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
  <load-typesystem name="typesystem_svg.xml" generate="no"/>
  <load-typesystem name="typesystem_widgets.xml" generate="no"/>

  <object-type name="QSvgWidget"/>

  <object-type name="QGraphicsSvgItem">
    <modify-function signature="QGraphicsSvgItem(QGraphicsItem*)">
      <modify-argument index="this">
          <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="QGraphicsSvgItem(const QString&amp;,QGraphicsItem*)">
      <modify-argument index="this">
          <parent index="2" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
</typesystem>
