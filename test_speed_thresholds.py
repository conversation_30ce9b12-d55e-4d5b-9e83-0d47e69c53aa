#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for new speed threshold standards
"""

import sys
import os

# Add the code directory to the path
sys.path.append('code')

from gait_analyzer import Gait<PERSON>naly<PERSON>

def test_speed_evaluation():
    """Test the speed evaluation with new thresholds"""

    print("=== 測試新的雙速度閾值系統 ===")
    print()

    # Test with pixel_to_meter = None (relative speed)
    print("1. 測試相對速度模式 (pixel_to_meter = null)")
    analyzer_relative = GaitAnalyzer()

    print(f"當前速度單位: {analyzer_relative.get_speed_unit()}")
    print(f"當前速度閾值: {analyzer_relative.get_speed_thresholds()}")
    print()

    # Test with pixel_to_meter set (actual speed)
    print("2. 測試實際速度模式 (pixel_to_meter = 0.01)")
    # Temporarily modify config for testing
    analyzer_actual = GaitAnalyzer()
    analyzer_actual.pixel_to_meter = 0.01  # 1 pixel = 0.01 meter
    analyzer_actual._update_speed_thresholds()

    print(f"當前速度單位: {analyzer_actual.get_speed_unit()}")
    print(f"當前速度閾值: {analyzer_actual.get_speed_thresholds()}")
    print()
    
    # Test different speeds for both modes
    print("3. 相對速度評分測試 (pixels/s):")
    test_speeds_relative = [0, 5, 10, 15, 20, 30, 40, 50, 60, 80, 100]
    print("速度(pixels/s) | 評分 | 預期等級")
    print("-" * 40)

    for speed in test_speeds_relative:
        score = analyzer_relative._evaluate_speed(speed)
        thresholds = analyzer_relative.get_speed_thresholds()

        if speed >= thresholds['excellent']:
            expected_level = "優良"
        elif speed >= thresholds['good']:
            expected_level = "一般"
        else:
            expected_level = "不佳"

        print(f"{speed:13.1f} | {score:4.0f} | {expected_level}")

    print()
    print("4. 實際速度評分測試 (m/s):")
    test_speeds_actual = [0.0, 0.3, 0.5, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.5, 2.0]
    print("速度(m/s) | 評分 | 預期等級")
    print("-" * 35)

    for speed in test_speeds_actual:
        score = analyzer_actual._evaluate_speed(speed)
        thresholds = analyzer_actual.get_speed_thresholds()

        if speed >= thresholds['excellent']:
            expected_level = "優良"
        elif speed >= thresholds['good']:
            expected_level = "一般"
        else:
            expected_level = "不佳"

        print(f"{speed:8.1f} | {score:4.0f} | {expected_level}")
    
    print()
    print("=== 綜合評分測試（使用實際速度）===")

    # Test comprehensive scoring with different angle and speed combinations
    test_cases = [
        {"angles": {"left_knee": 90, "right_knee": 90}, "speed": 0.5, "desc": "正常角度，慢速"},
        {"angles": {"left_knee": 90, "right_knee": 90}, "speed": 0.9, "desc": "正常角度，一般速度"},
        {"angles": {"left_knee": 90, "right_knee": 90}, "speed": 1.2, "desc": "正常角度，快速"},
        {"angles": {"left_knee": 140, "right_knee": 140}, "speed": 0.9, "desc": "異常角度，一般速度"},
        {"angles": {"left_knee": 140, "right_knee": 140}, "speed": 1.2, "desc": "異常角度，快速"},
    ]

    print("測試案例 | 角度分 | 速度分 | 總分 | 等級")
    print("-" * 50)

    for case in test_cases:
        angles = case["angles"]
        speed = case["speed"]
        desc = case["desc"]

        level, total_score, reason, angle_score, speed_score = analyzer_actual.evaluate_gait(angles, speed)

        print(f"{desc:12} | {angle_score:6.0f} | {speed_score:6.0f} | {total_score:4.0f} | {level}")

    print()
    print("=== 權重配置測試 ===")
    print(f"角度權重: {analyzer_actual.scoring['angle_weight']}")
    print(f"速度權重: {analyzer_actual.scoring['speed_weight']}")
    print(f"權重總和: {analyzer_actual.scoring['angle_weight'] + analyzer_actual.scoring['speed_weight']}")

    if abs(analyzer_actual.scoring['angle_weight'] + analyzer_actual.scoring['speed_weight'] - 1.0) > 0.001:
        print("⚠️  警告：權重總和不等於1.0")
    else:
        print("✓ 權重配置正確")

def test_settings_loading():
    """Test if settings are loaded correctly"""
    
    print("\n=== 測試配置載入 ===")
    
    analyzer = GaitAnalyzer()
    
    # Check if settings.yaml exists
    if os.path.exists('settings.yaml'):
        print("✓ settings.yaml 文件存在")
    else:
        print("⚠️  settings.yaml 文件不存在，使用預設配置")
    
    # Print all loaded configurations
    print("\n載入的配置：")
    print("速度閾值：")
    for key, value in analyzer.speed_thresholds.items():
        print(f"  {key}: {value}")
    
    print("\n角度閾值：")
    for key, value in analyzer.angle_thresholds.items():
        print(f"  {key}: {value}")
    
    print("\n評分權重：")
    for key, value in analyzer.scoring.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    test_speed_evaluation()
    test_settings_loading()
