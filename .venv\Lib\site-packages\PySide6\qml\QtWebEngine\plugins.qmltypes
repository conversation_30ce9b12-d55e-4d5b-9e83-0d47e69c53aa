import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineFileSystemAccessRequest"
        accessSemantics: "value"
        exports: ["QtWebEngine/webEngineFileSystemAccessRequest 6.4"]
        isCreatable: false
        exportMetaObjectRevisions: [1540]
        Enum {
            name: "HandleType"
            values: ["File", "Directory"]
        }
        Enum {
            name: "AccessFlags"
            alias: "AccessFlag"
            isFlag: true
            values: ["Read", "Write"]
        }
        Property {
            name: "origin"
            type: "QUrl"
            read: "origin"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "filePath"
            type: "QUrl"
            read: "filePath"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "handleType"
            type: "HandleType"
            read: "handleType"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "accessFlags"
            type: "AccessFlags"
            read: "accessFlags"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Method { name: "accept" }
        Method { name: "reject" }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineFileSystemAccessRequestDerived"
        accessSemantics: "none"
        prototype: "QWebEngineFileSystemAccessRequest"
        exports: ["QtWebEngine/WebEngineFileSystemAccessRequest 6.4"]
        isCreatable: false
        exportMetaObjectRevisions: [1540]
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineWebAuthPinRequest"
        accessSemantics: "value"
        exports: ["QtWebEngine/webEngineWebAuthPinRequest 6.8"]
        isCreatable: false
        exportMetaObjectRevisions: [1544]
        Property {
            name: "reason"
            type: "QWebEngineWebAuthUxRequest::PinEntryReason"
            index: 0
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "error"
            type: "QWebEngineWebAuthUxRequest::PinEntryError"
            index: 1
            isFinal: true
            isPropertyConstant: true
        }
        Property { name: "minPinLength"; type: "int"; index: 2; isFinal: true; isPropertyConstant: true }
        Property {
            name: "remainingAttempts"
            type: "int"
            index: 3
            isFinal: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineCertificateError"
        accessSemantics: "value"
        exports: [
            "QtWebEngine/webEngineCertificateError 1.1",
            "QtWebEngine/webEngineCertificateError 6.0",
            "QtWebEngine/webEngineCertificateError 6.8"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536, 1544]
        Enum {
            name: "Type"
            values: [
                "Ok",
                "SslPinnedKeyNotInCertificateChain",
                "CertificateCommonNameInvalid",
                "CertificateDateInvalid",
                "CertificateAuthorityInvalid",
                "CertificateContainsErrors",
                "CertificateNoRevocationMechanism",
                "CertificateUnableToCheckRevocation",
                "CertificateRevoked",
                "CertificateInvalid",
                "CertificateWeakSignatureAlgorithm",
                "CertificateNonUniqueName",
                "CertificateWeakKey",
                "CertificateNameConstraintViolation",
                "CertificateValidityTooLong",
                "CertificateTransparencyRequired",
                "CertificateSymantecLegacy",
                "CertificateKnownInterceptionBlocked",
                "SslObsoleteVersion"
            ]
        }
        Property {
            name: "url"
            type: "QUrl"
            read: "url"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "type"
            type: "Type"
            read: "type"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "description"
            type: "QString"
            read: "description"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "overridable"
            type: "bool"
            read: "isOverridable"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "isMainFrame"
            revision: 1544
            type: "bool"
            read: "isMainFrame"
            index: 4
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Method { name: "defer" }
        Method { name: "rejectCertificate" }
        Method { name: "acceptCertificate" }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineCertificateErrorDerived"
        accessSemantics: "none"
        prototype: "QWebEngineCertificateError"
        exports: [
            "QtWebEngine/WebEngineCertificateError 1.1",
            "QtWebEngine/WebEngineCertificateError 6.0",
            "QtWebEngine/WebEngineCertificateError 6.8"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536, 1544]
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineContextMenuRequest"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/ContextMenuRequest 1.7",
            "QtWebEngine/ContextMenuRequest 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [263, 1536]
        Enum {
            name: "MediaType"
            values: [
                "MediaTypeNone",
                "MediaTypeImage",
                "MediaTypeVideo",
                "MediaTypeAudio",
                "MediaTypeCanvas",
                "MediaTypeFile",
                "MediaTypePlugin"
            ]
        }
        Enum {
            name: "MediaFlags"
            alias: "MediaFlag"
            isFlag: true
            values: [
                "MediaInError",
                "MediaPaused",
                "MediaMuted",
                "MediaLoop",
                "MediaCanSave",
                "MediaHasAudio",
                "MediaCanToggleControls",
                "MediaControls",
                "MediaCanPrint",
                "MediaCanRotate"
            ]
        }
        Enum {
            name: "EditFlags"
            alias: "EditFlag"
            isFlag: true
            values: [
                "CanUndo",
                "CanRedo",
                "CanCut",
                "CanCopy",
                "CanPaste",
                "CanDelete",
                "CanSelectAll",
                "CanTranslate",
                "CanEditRichly"
            ]
        }
        Property {
            name: "position"
            type: "QPoint"
            read: "position"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "selectedText"
            type: "QString"
            read: "selectedText"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "linkText"
            type: "QString"
            read: "linkText"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "linkUrl"
            type: "QUrl"
            read: "linkUrl"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "mediaUrl"
            type: "QUrl"
            read: "mediaUrl"
            index: 4
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "mediaType"
            type: "MediaType"
            read: "mediaType"
            index: 5
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "isContentEditable"
            type: "bool"
            read: "isContentEditable"
            index: 6
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "misspelledWord"
            type: "QString"
            read: "misspelledWord"
            index: 7
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "spellCheckerSuggestions"
            type: "QStringList"
            read: "spellCheckerSuggestions"
            index: 8
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "accepted"
            type: "bool"
            read: "isAccepted"
            write: "setAccepted"
            index: 9
            isFinal: true
        }
        Property {
            name: "mediaFlags"
            revision: 257
            type: "MediaFlags"
            read: "mediaFlags"
            index: 10
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "editFlags"
            revision: 257
            type: "EditFlags"
            read: "editFlags"
            index: 11
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineFindTextResult"
        accessSemantics: "value"
        exports: [
            "QtWebEngine/findTextResult 1.10",
            "QtWebEngine/findTextResult 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [266, 1536]
        Property {
            name: "numberOfMatches"
            type: "int"
            read: "numberOfMatches"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "activeMatch"
            type: "int"
            read: "activeMatch"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineFullScreenRequest"
        accessSemantics: "value"
        exports: [
            "QtWebEngine/fullScreenRequest 1.1",
            "QtWebEngine/fullScreenRequest 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536]
        Property {
            name: "toggleOn"
            type: "bool"
            read: "toggleOn"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "origin"
            type: "QUrl"
            read: "origin"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Method { name: "reject" }
        Method { name: "accept" }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineHistory"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/WebEngineHistory 1.1",
            "QtWebEngine/WebEngineHistory 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536]
        Property {
            name: "items"
            type: "QWebEngineHistoryModel"
            isPointer: true
            read: "itemsModel"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "backItems"
            type: "QWebEngineHistoryModel"
            isPointer: true
            read: "backItemsModel"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "forwardItems"
            type: "QWebEngineHistoryModel"
            isPointer: true
            read: "forwardItemsModel"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Method { name: "clear"; revision: 65281 }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineHistoryModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        exports: [
            "QtWebEngine/WebEngineHistoryModel 1.1",
            "QtWebEngine/WebEngineHistoryModel 6.0",
            "QtWebEngine/WebEngineHistoryModel 6.4"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536, 1540]
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineLoadingInfo"
        accessSemantics: "value"
        exports: [
            "QtWebEngine/webEngineLoadingInfo 1.1",
            "QtWebEngine/webEngineLoadingInfo 6.0",
            "QtWebEngine/webEngineLoadingInfo 6.6",
            "QtWebEngine/webEngineLoadingInfo 6.9"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536, 1542, 1545]
        Enum {
            name: "LoadStatus"
            values: [
                "LoadStartedStatus",
                "LoadStoppedStatus",
                "LoadSucceededStatus",
                "LoadFailedStatus"
            ]
        }
        Enum {
            name: "ErrorDomain"
            values: [
                "NoErrorDomain",
                "InternalErrorDomain",
                "ConnectionErrorDomain",
                "CertificateErrorDomain",
                "HttpErrorDomain",
                "FtpErrorDomain",
                "DnsErrorDomain",
                "HttpStatusCodeDomain"
            ]
        }
        Property {
            name: "url"
            type: "QUrl"
            read: "url"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "isErrorPage"
            type: "bool"
            read: "isErrorPage"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "status"
            type: "LoadStatus"
            read: "status"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "errorString"
            type: "QString"
            read: "errorString"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "errorDomain"
            type: "ErrorDomain"
            read: "errorDomain"
            index: 4
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "errorCode"
            type: "int"
            read: "errorCode"
            index: 5
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "responseHeaders"
            revision: 1542
            type: "QMultiMap<QByteArray,QByteArray>"
            read: "responseHeaders"
            index: 6
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "isDownload"
            revision: 1545
            type: "bool"
            read: "isDownload"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineLoadingInfoDerived"
        accessSemantics: "none"
        prototype: "QWebEngineLoadingInfo"
        exports: [
            "QtWebEngine/WebEngineLoadingInfo 1.1",
            "QtWebEngine/WebEngineLoadingInfo 6.0",
            "QtWebEngine/WebEngineLoadingInfo 6.6",
            "QtWebEngine/WebEngineLoadingInfo 6.9"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536, 1542, 1545]
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineNavigationRequest"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/WebEngineNavigationRequest 1.0",
            "QtWebEngine/WebEngineNavigationRequest 6.0",
            "QtWebEngine/WebEngineNavigationRequest 6.8"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536, 1544]
        Enum {
            name: "NavigationType"
            values: [
                "LinkClickedNavigation",
                "TypedNavigation",
                "FormSubmittedNavigation",
                "BackForwardNavigation",
                "ReloadNavigation",
                "OtherNavigation",
                "RedirectNavigation"
            ]
        }
        Enum {
            name: "NavigationRequestAction"
            values: ["AcceptRequest", "IgnoreRequest"]
        }
        Property {
            name: "url"
            type: "QUrl"
            read: "url"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "isMainFrame"
            type: "bool"
            read: "isMainFrame"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "hasFormData"
            revision: 1544
            type: "bool"
            read: "hasFormData"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "navigationType"
            type: "NavigationType"
            read: "navigationType"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "action"
            type: "NavigationRequestAction"
            read: "action"
            write: "setAction"
            notify: "actionChanged"
            index: 4
            isFinal: true
        }
        Signal { name: "actionChanged" }
        Method { name: "accept" }
        Method { name: "reject" }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineNotification"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/WebEngineNotification 1.9",
            "QtWebEngine/WebEngineNotification 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [265, 1536]
        Property {
            name: "origin"
            type: "QUrl"
            read: "origin"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "message"
            type: "QString"
            read: "message"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "tag"
            type: "QString"
            read: "tag"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "language"
            type: "QString"
            read: "language"
            index: 4
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "direction"
            type: "Qt::LayoutDirection"
            read: "direction"
            index: 5
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Signal { name: "closed" }
        Method { name: "show"; isMethodConstant: true }
        Method { name: "click"; isMethodConstant: true }
        Method { name: "close"; isMethodConstant: true }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEnginePermission"
        accessSemantics: "value"
        exports: ["QtWebEngine/webEnginePermission 6.8"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1544]
        Enum {
            name: "PermissionType"
            isScoped: true
            type: "quint8"
            values: [
                "Unsupported",
                "MediaAudioCapture",
                "MediaVideoCapture",
                "MediaAudioVideoCapture",
                "DesktopVideoCapture",
                "DesktopAudioVideoCapture",
                "MouseLock",
                "Notifications",
                "Geolocation",
                "ClipboardReadWrite",
                "LocalFontsAccess"
            ]
        }
        Enum {
            name: "State"
            isScoped: true
            type: "quint8"
            values: ["Invalid", "Ask", "Granted", "Denied"]
        }
        Property {
            name: "origin"
            type: "QUrl"
            read: "origin"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "permissionType"
            type: "PermissionType"
            read: "permissionType"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "state"
            type: "State"
            read: "state"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "isValid"
            type: "bool"
            read: "isValid"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Method { name: "grant"; isMethodConstant: true }
        Method { name: "deny"; isMethodConstant: true }
        Method { name: "reset"; isMethodConstant: true }
        Method {
            name: "isPersistent"
            type: "bool"
            Parameter { name: "permissionType"; type: "QWebEnginePermission::PermissionType" }
        }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEnginePermissionDerived"
        accessSemantics: "none"
        prototype: "QWebEnginePermission"
        exports: ["QtWebEngine/WebEnginePermission 6.8"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1544]
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineQuotaRequest"
        accessSemantics: "value"
        exports: [
            "QtWebEngine/webEngineQuotaRequest 1.7",
            "QtWebEngine/webEngineQuotaRequest 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [263, 1536]
        Property {
            name: "origin"
            type: "QUrl"
            read: "origin"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "requestedSize"
            type: "qlonglong"
            read: "requestedSize"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Method { name: "accept" }
        Method { name: "reject" }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineRegisterProtocolHandlerRequest"
        accessSemantics: "value"
        exports: [
            "QtWebEngine/registerProtocolHandlerRequest 1.7",
            "QtWebEngine/registerProtocolHandlerRequest 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [263, 1536]
        Property {
            name: "origin"
            type: "QUrl"
            read: "origin"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "scheme"
            type: "QString"
            read: "scheme"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Method { name: "accept" }
        Method { name: "reject" }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineScript"
        accessSemantics: "value"
        exports: [
            "QtWebEngine/webEngineScript 1.1",
            "QtWebEngine/webEngineScript 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536]
        Enum {
            name: "InjectionPoint"
            values: ["Deferred", "DocumentReady", "DocumentCreation"]
        }
        Enum {
            name: "ScriptWorldId"
            values: ["MainWorld", "ApplicationWorld", "UserWorld"]
        }
        Property { name: "name"; type: "QString"; read: "name"; write: "setName"; index: 0; isFinal: true }
        Property {
            name: "sourceUrl"
            type: "QUrl"
            read: "sourceUrl"
            write: "setSourceUrl"
            index: 1
            isFinal: true
        }
        Property {
            name: "sourceCode"
            type: "QString"
            read: "sourceCode"
            write: "setSourceCode"
            index: 2
            isFinal: true
        }
        Property {
            name: "injectionPoint"
            type: "InjectionPoint"
            read: "injectionPoint"
            write: "setInjectionPoint"
            index: 3
            isFinal: true
        }
        Property {
            name: "worldId"
            type: "uint"
            read: "worldId"
            write: "setWorldId"
            index: 4
            isFinal: true
        }
        Property {
            name: "runsOnSubFrames"
            type: "bool"
            read: "runsOnSubFrames"
            write: "setRunsOnSubFrames"
            index: 5
            isFinal: true
        }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineScript"
        accessSemantics: "none"
        exports: [
            "QtWebEngine/WebEngineScript 1.1",
            "QtWebEngine/WebEngineScript 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536]
        Enum {
            name: "InjectionPoint"
            values: ["Deferred", "DocumentReady", "DocumentCreation"]
        }
        Enum {
            name: "ScriptWorldId"
            values: ["MainWorld", "ApplicationWorld", "UserWorld"]
        }
        Property { name: "name"; type: "QString"; read: "name"; write: "setName"; index: 0; isFinal: true }
        Property {
            name: "sourceUrl"
            type: "QUrl"
            read: "sourceUrl"
            write: "setSourceUrl"
            index: 1
            isFinal: true
        }
        Property {
            name: "sourceCode"
            type: "QString"
            read: "sourceCode"
            write: "setSourceCode"
            index: 2
            isFinal: true
        }
        Property {
            name: "injectionPoint"
            type: "InjectionPoint"
            read: "injectionPoint"
            write: "setInjectionPoint"
            index: 3
            isFinal: true
        }
        Property {
            name: "worldId"
            type: "uint"
            read: "worldId"
            write: "setWorldId"
            index: 4
            isFinal: true
        }
        Property {
            name: "runsOnSubFrames"
            type: "bool"
            read: "runsOnSubFrames"
            write: "setRunsOnSubFrames"
            index: 5
            isFinal: true
        }
    }
    Component {
        file: "private/qquickwebengineforeigntypes_p.h"
        name: "QWebEngineWebAuthUxRequest"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtWebEngine/WebEngineWebAuthUxRequest 6.7"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1543]
        Enum {
            name: "WebAuthUxState"
            isScoped: true
            values: [
                "NotStarted",
                "SelectAccount",
                "CollectPin",
                "FinishTokenCollection",
                "RequestFailed",
                "Cancelled",
                "Completed"
            ]
        }
        Enum {
            name: "PinEntryReason"
            isScoped: true
            values: ["Set", "Change", "Challenge"]
        }
        Enum {
            name: "PinEntryError"
            isScoped: true
            values: [
                "NoError",
                "InternalUvLocked",
                "WrongPin",
                "TooShort",
                "InvalidCharacters",
                "SameAsCurrentPin"
            ]
        }
        Enum {
            name: "RequestFailureReason"
            isScoped: true
            values: [
                "Timeout",
                "KeyNotRegistered",
                "KeyAlreadyRegistered",
                "SoftPinBlock",
                "HardPinBlock",
                "AuthenticatorRemovedDuringPinEntry",
                "AuthenticatorMissingResidentKeys",
                "AuthenticatorMissingUserVerification",
                "AuthenticatorMissingLargeBlob",
                "NoCommonAlgorithms",
                "StorageFull",
                "UserConsentDenied",
                "WinUserCancelled"
            ]
        }
        Property {
            name: "userNames"
            type: "QStringList"
            read: "userNames"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "state"
            type: "WebAuthUxState"
            read: "state"
            notify: "stateChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "relyingPartyId"
            type: "QString"
            read: "relyingPartyId"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "pinRequest"
            type: "QWebEngineWebAuthPinRequest"
            read: "pinRequest"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "requestFailureReason"
            type: "RequestFailureReason"
            read: "requestFailureReason"
            index: 4
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Signal {
            name: "stateChanged"
            Parameter { name: "state"; type: "QWebEngineWebAuthUxRequest::WebAuthUxState" }
        }
        Method { name: "cancel" }
        Method { name: "retry" }
        Method {
            name: "setSelectedAccount"
            Parameter { name: "selectedAccount"; type: "QString" }
        }
        Method {
            name: "setPin"
            Parameter { name: "pin"; type: "QString" }
        }
    }
    Component {
        file: "private/qquickwebengineaction_p.h"
        name: "QQuickWebEngineAction"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/WebEngineAction 1.8",
            "QtWebEngine/WebEngineAction 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [264, 1536]
        Property {
            name: "text"
            type: "QString"
            read: "text"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "iconName"
            type: "QString"
            read: "iconName"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            notify: "enabledChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Signal { name: "triggered" }
        Signal { name: "enabledChanged" }
        Method { name: "trigger" }
    }
    Component {
        file: "private/qquickwebenginedialogrequests_p.h"
        name: "QQuickWebEngineAuthenticationDialogRequest"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/AuthenticationDialogRequest 1.4",
            "QtWebEngine/AuthenticationDialogRequest 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [260, 1536]
        Enum {
            name: "AuthenticationType"
            values: ["AuthenticationTypeHTTP", "AuthenticationTypeProxy"]
        }
        Property {
            name: "url"
            type: "QUrl"
            read: "url"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "realm"
            type: "QString"
            read: "realm"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "proxyHost"
            type: "QString"
            read: "proxyHost"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "type"
            type: "AuthenticationType"
            read: "type"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "accepted"
            type: "bool"
            read: "isAccepted"
            write: "setAccepted"
            index: 4
            isFinal: true
        }
        Method {
            name: "dialogAccept"
            Parameter { name: "user"; type: "QString" }
            Parameter { name: "password"; type: "QString" }
        }
        Method { name: "dialogReject" }
    }
    Component {
        file: "private/qquickwebengineclientcertificateselection_p.h"
        name: "QQuickWebEngineClientCertificateOption"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/WebEngineClientCertificateOption 1.9",
            "QtWebEngine/WebEngineClientCertificateOption 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [265, 1536]
        Property {
            name: "issuer"
            type: "QString"
            read: "issuer"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "subject"
            type: "QString"
            read: "subject"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "effectiveDate"
            type: "QDateTime"
            read: "effectiveDate"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "expiryDate"
            type: "QDateTime"
            read: "expiryDate"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "isSelfSigned"
            type: "bool"
            read: "isSelfSigned"
            index: 4
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Method { name: "select" }
    }
    Component {
        file: "private/qquickwebengineclientcertificateselection_p.h"
        name: "QQuickWebEngineClientCertificateSelection"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/WebEngineClientCertificateSelection 1.9",
            "QtWebEngine/WebEngineClientCertificateSelection 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [265, 1536]
        Property {
            name: "host"
            type: "QUrl"
            read: "host"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "certificates"
            type: "QQuickWebEngineClientCertificateOption"
            isList: true
            read: "certificates"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Method {
            name: "select"
            Parameter { name: "idx"; type: "int" }
        }
        Method {
            name: "select"
            Parameter {
                name: "certificate"
                type: "QQuickWebEngineClientCertificateOption"
                isPointer: true
                isTypeConstant: true
            }
        }
        Method { name: "selectNone" }
    }
    Component {
        file: "private/qquickwebenginedialogrequests_p.h"
        name: "QQuickWebEngineColorDialogRequest"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/ColorDialogRequest 1.4",
            "QtWebEngine/ColorDialogRequest 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [260, 1536]
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "accepted"
            type: "bool"
            read: "isAccepted"
            write: "setAccepted"
            index: 1
            isFinal: true
        }
        Method {
            name: "dialogAccept"
            Parameter { name: "color"; type: "QColor" }
        }
        Method { name: "dialogReject" }
    }
    Component {
        file: "qquickwebenginedownloadrequest.h"
        name: "QQuickWebEngineDownloadRequest"
        accessSemantics: "reference"
        prototype: "QWebEngineDownloadRequest"
        exports: [
            "QtWebEngine/WebEngineDownloadRequest 1.1",
            "QtWebEngine/WebEngineDownloadRequest 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536]
        Property {
            name: "view"
            type: "QQuickWebEngineView"
            isPointer: true
            read: "view"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qquickwebenginedialogrequests_p.h"
        name: "QQuickWebEngineFileDialogRequest"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/FileDialogRequest 1.4",
            "QtWebEngine/FileDialogRequest 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [260, 1536]
        Enum {
            name: "FileMode"
            values: [
                "FileModeOpen",
                "FileModeOpenMultiple",
                "FileModeUploadFolder",
                "FileModeSave"
            ]
        }
        Property {
            name: "defaultFileName"
            type: "QString"
            read: "defaultFileName"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "acceptedMimeTypes"
            type: "QStringList"
            read: "acceptedMimeTypes"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "mode"
            type: "FileMode"
            read: "mode"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "accepted"
            type: "bool"
            read: "isAccepted"
            write: "setAccepted"
            index: 3
            isFinal: true
        }
        Method {
            name: "dialogAccept"
            Parameter { name: "files"; type: "QStringList" }
        }
        Method { name: "dialogReject" }
    }
    Component {
        file: "private/qquickwebenginedialogrequests_p.h"
        name: "QQuickWebEngineJavaScriptDialogRequest"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/JavaScriptDialogRequest 1.4",
            "QtWebEngine/JavaScriptDialogRequest 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [260, 1536]
        Enum {
            name: "DialogType"
            values: [
                "DialogTypeAlert",
                "DialogTypeConfirm",
                "DialogTypePrompt",
                "DialogTypeBeforeUnload"
            ]
        }
        Property {
            name: "message"
            type: "QString"
            read: "message"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "defaultText"
            type: "QString"
            read: "defaultText"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "type"
            type: "DialogType"
            read: "type"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "securityOrigin"
            type: "QUrl"
            read: "securityOrigin"
            index: 4
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "accepted"
            type: "bool"
            read: "isAccepted"
            write: "setAccepted"
            index: 5
            isFinal: true
        }
        Method {
            name: "dialogAccept"
            Parameter { name: "text"; type: "QString" }
        }
        Method { name: "dialogAccept"; isCloned: true }
        Method { name: "dialogReject" }
    }
    Component {
        file: "private/qquickwebenginenewwindowrequest_p.h"
        name: "QQuickWebEngineNewWindowRequest"
        accessSemantics: "reference"
        prototype: "QWebEngineNewWindowRequest"
        exports: [
            "QtWebEngine/WebEngineNewWindowRequest 1.1",
            "QtWebEngine/WebEngineNewWindowRequest 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [257, 1536]
        Method {
            name: "openIn"
            Parameter { type: "QQuickWebEngineView"; isPointer: true }
        }
    }
    Component {
        file: "qquickwebengineprofile.h"
        name: "QQuickWebEngineProfile"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/WebEngineProfile 1.1",
            "QtWebEngine/WebEngineProfile 1.2",
            "QtWebEngine/WebEngineProfile 1.3",
            "QtWebEngine/WebEngineProfile 1.5",
            "QtWebEngine/WebEngineProfile 6.0",
            "QtWebEngine/WebEngineProfile 6.5",
            "QtWebEngine/WebEngineProfile 6.7",
            "QtWebEngine/WebEngineProfile 6.8"
        ]
        exportMetaObjectRevisions: [
            257,
            258,
            259,
            261,
            1536,
            1541,
            1543,
            1544
        ]
        Enum {
            name: "HttpCacheType"
            values: ["MemoryHttpCache", "DiskHttpCache", "NoCache"]
        }
        Enum {
            name: "PersistentCookiesPolicy"
            values: [
                "NoPersistentCookies",
                "AllowPersistentCookies",
                "ForcePersistentCookies"
            ]
        }
        Enum {
            name: "PersistentPermissionsPolicy"
            isScoped: true
            type: "quint8"
            values: ["AskEveryTime", "StoreInMemory", "StoreOnDisk"]
        }
        Property {
            name: "storageName"
            type: "QString"
            read: "storageName"
            write: "setStorageName"
            notify: "storageNameChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "offTheRecord"
            type: "bool"
            read: "isOffTheRecord"
            write: "setOffTheRecord"
            notify: "offTheRecordChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "persistentStoragePath"
            type: "QString"
            read: "persistentStoragePath"
            write: "setPersistentStoragePath"
            notify: "persistentStoragePathChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "cachePath"
            type: "QString"
            read: "cachePath"
            write: "setCachePath"
            notify: "cachePathChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "httpUserAgent"
            type: "QString"
            read: "httpUserAgent"
            write: "setHttpUserAgent"
            notify: "httpUserAgentChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "httpCacheType"
            type: "HttpCacheType"
            read: "httpCacheType"
            write: "setHttpCacheType"
            notify: "httpCacheTypeChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "httpAcceptLanguage"
            revision: 257
            type: "QString"
            read: "httpAcceptLanguage"
            write: "setHttpAcceptLanguage"
            notify: "httpAcceptLanguageChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "persistentCookiesPolicy"
            type: "PersistentCookiesPolicy"
            read: "persistentCookiesPolicy"
            write: "setPersistentCookiesPolicy"
            notify: "persistentCookiesPolicyChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "persistentPermissionsPolicy"
            revision: 1544
            type: "PersistentPermissionsPolicy"
            read: "persistentPermissionsPolicy"
            write: "setPersistentPermissionsPolicy"
            notify: "persistentPermissionsPolicyChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "httpCacheMaximumSize"
            type: "int"
            read: "httpCacheMaximumSize"
            write: "setHttpCacheMaximumSize"
            notify: "httpCacheMaximumSizeChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "spellCheckLanguages"
            revision: 259
            type: "QStringList"
            read: "spellCheckLanguages"
            write: "setSpellCheckLanguages"
            notify: "spellCheckLanguagesChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "spellCheckEnabled"
            revision: 259
            type: "bool"
            read: "isSpellCheckEnabled"
            write: "setSpellCheckEnabled"
            notify: "spellCheckEnabledChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "userScripts"
            type: "QQuickWebEngineScriptCollection"
            isPointer: true
            read: "userScripts"
            index: 12
            isReadonly: true
        }
        Property {
            name: "downloadPath"
            revision: 261
            type: "QString"
            read: "downloadPath"
            write: "setDownloadPath"
            notify: "downloadPathChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "isPushServiceEnabled"
            revision: 1541
            type: "bool"
            read: "isPushServiceEnabled"
            write: "setPushServiceEnabled"
            notify: "pushServiceEnabledChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "clientHints"
            revision: 1544
            type: "QWebEngineClientHints"
            isPointer: true
            read: "clientHints"
            index: 15
            isReadonly: true
            isFinal: true
        }
        Signal { name: "storageNameChanged" }
        Signal { name: "offTheRecordChanged" }
        Signal { name: "persistentStoragePathChanged" }
        Signal { name: "cachePathChanged" }
        Signal { name: "httpUserAgentChanged" }
        Signal { name: "httpCacheTypeChanged" }
        Signal { name: "persistentCookiesPolicyChanged" }
        Signal { name: "httpCacheMaximumSizeChanged" }
        Signal { name: "httpAcceptLanguageChanged"; revision: 257 }
        Signal { name: "spellCheckLanguagesChanged"; revision: 259 }
        Signal { name: "spellCheckEnabledChanged"; revision: 259 }
        Signal { name: "downloadPathChanged"; revision: 261 }
        Signal { name: "pushServiceEnabledChanged"; revision: 1541 }
        Signal { name: "clearHttpCacheCompleted"; revision: 1543 }
        Signal { name: "persistentPermissionsPolicyChanged"; revision: 1544 }
        Signal {
            name: "downloadRequested"
            Parameter { name: "download"; type: "QQuickWebEngineDownloadRequest"; isPointer: true }
        }
        Signal {
            name: "downloadFinished"
            Parameter { name: "download"; type: "QQuickWebEngineDownloadRequest"; isPointer: true }
        }
        Signal {
            name: "presentNotification"
            revision: 261
            Parameter { name: "notification"; type: "QWebEngineNotification"; isPointer: true }
        }
        Method { name: "clearHttpCache"; revision: 258 }
        Method {
            name: "queryPermission"
            revision: 1544
            type: "QWebEnginePermission"
            isMethodConstant: true
            Parameter { name: "securityOrigin"; type: "QUrl" }
            Parameter { name: "permissionType"; type: "QWebEnginePermission::PermissionType" }
        }
        Method {
            name: "listAllPermissions"
            revision: 1544
            type: "QWebEnginePermission"
            isList: true
            isMethodConstant: true
        }
        Method {
            name: "listPermissionsForOrigin"
            revision: 1544
            type: "QWebEnginePermission"
            isList: true
            isMethodConstant: true
            Parameter { name: "securityOrigin"; type: "QUrl" }
        }
        Method {
            name: "listPermissionsForPermissionType"
            revision: 1544
            type: "QWebEnginePermission"
            isList: true
            isMethodConstant: true
            Parameter { name: "permissionType"; type: "QWebEnginePermission::PermissionType" }
        }
    }
    Component {
        file: "private/qquickwebengineprofileprototype_p.h"
        name: "QQuickWebEngineProfilePrototype"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtWebEngine/WebEngineProfilePrototype 6.9"]
        exportMetaObjectRevisions: [1545]
        Property {
            name: "storageName"
            type: "QString"
            read: "storageName"
            write: "setStorageName"
            index: 0
            isFinal: true
        }
        Property {
            name: "persistentStoragePath"
            type: "QString"
            read: "persistentStoragePath"
            write: "setPersistentStoragePath"
            index: 1
            isFinal: true
        }
        Property {
            name: "cachePath"
            type: "QString"
            read: "cachePath"
            write: "setCachePath"
            index: 2
            isFinal: true
        }
        Property {
            name: "httpCacheType"
            type: "QQuickWebEngineProfile::HttpCacheType"
            read: "httpCacheType"
            write: "setHttpCacheType"
            index: 3
            isFinal: true
        }
        Property {
            name: "persistentCookiesPolicy"
            type: "QQuickWebEngineProfile::PersistentCookiesPolicy"
            read: "persistentCookiesPolicy"
            write: "setPersistentCookiesPolicy"
            index: 4
            isFinal: true
        }
        Property {
            name: "httpCacheMaximumSize"
            type: "int"
            read: "httpCacheMaximumSize"
            write: "setHttpCacheMaximumSize"
            index: 5
            isFinal: true
        }
        Property {
            name: "persistentPermissionsPolicy"
            type: "QQuickWebEngineProfile::PersistentPermissionsPolicy"
            read: "persistentPermissionsPolicy"
            write: "setPersistentPermissionsPolicy"
            index: 6
            isFinal: true
        }
        Method { name: "instance"; type: "QQuickWebEngineProfile"; isPointer: true }
    }
    Component {
        file: "private/qquickwebenginesettings_p.h"
        name: "QQuickWebEngineSettings"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/WebEngineSettings 1.1",
            "QtWebEngine/WebEngineSettings 1.2",
            "QtWebEngine/WebEngineSettings 1.3",
            "QtWebEngine/WebEngineSettings 1.4",
            "QtWebEngine/WebEngineSettings 1.5",
            "QtWebEngine/WebEngineSettings 1.6",
            "QtWebEngine/WebEngineSettings 1.7",
            "QtWebEngine/WebEngineSettings 1.8",
            "QtWebEngine/WebEngineSettings 6.0",
            "QtWebEngine/WebEngineSettings 6.4",
            "QtWebEngine/WebEngineSettings 6.6",
            "QtWebEngine/WebEngineSettings 6.7",
            "QtWebEngine/WebEngineSettings 6.8",
            "QtWebEngine/WebEngineSettings 6.9"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [
            257,
            258,
            259,
            260,
            261,
            262,
            263,
            264,
            1536,
            1540,
            1542,
            1543,
            1544,
            1545
        ]
        Enum {
            name: "UnknownUrlSchemePolicy"
            values: [
                "DisallowUnknownUrlSchemes",
                "AllowUnknownUrlSchemesFromUserInteraction",
                "AllowAllUnknownUrlSchemes"
            ]
        }
        Enum {
            name: "ImageAnimationPolicy"
            isScoped: true
            type: "quint8"
            values: ["Allow", "AnimateOnce", "Disallow"]
        }
        Property {
            name: "autoLoadImages"
            type: "bool"
            read: "autoLoadImages"
            write: "setAutoLoadImages"
            notify: "autoLoadImagesChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "javascriptEnabled"
            type: "bool"
            read: "javascriptEnabled"
            write: "setJavascriptEnabled"
            notify: "javascriptEnabledChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "javascriptCanOpenWindows"
            type: "bool"
            read: "javascriptCanOpenWindows"
            write: "setJavascriptCanOpenWindows"
            notify: "javascriptCanOpenWindowsChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "javascriptCanAccessClipboard"
            type: "bool"
            read: "javascriptCanAccessClipboard"
            write: "setJavascriptCanAccessClipboard"
            notify: "javascriptCanAccessClipboardChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "linksIncludedInFocusChain"
            type: "bool"
            read: "linksIncludedInFocusChain"
            write: "setLinksIncludedInFocusChain"
            notify: "linksIncludedInFocusChainChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "localStorageEnabled"
            type: "bool"
            read: "localStorageEnabled"
            write: "setLocalStorageEnabled"
            notify: "localStorageEnabledChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "localContentCanAccessRemoteUrls"
            type: "bool"
            read: "localContentCanAccessRemoteUrls"
            write: "setLocalContentCanAccessRemoteUrls"
            notify: "localContentCanAccessRemoteUrlsChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "spatialNavigationEnabled"
            type: "bool"
            read: "spatialNavigationEnabled"
            write: "setSpatialNavigationEnabled"
            notify: "spatialNavigationEnabledChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "localContentCanAccessFileUrls"
            type: "bool"
            read: "localContentCanAccessFileUrls"
            write: "setLocalContentCanAccessFileUrls"
            notify: "localContentCanAccessFileUrlsChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "hyperlinkAuditingEnabled"
            type: "bool"
            read: "hyperlinkAuditingEnabled"
            write: "setHyperlinkAuditingEnabled"
            notify: "hyperlinkAuditingEnabledChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "errorPageEnabled"
            type: "bool"
            read: "errorPageEnabled"
            write: "setErrorPageEnabled"
            notify: "errorPageEnabledChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "pluginsEnabled"
            type: "bool"
            read: "pluginsEnabled"
            write: "setPluginsEnabled"
            notify: "pluginsEnabledChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "fullScreenSupportEnabled"
            revision: 257
            type: "bool"
            read: "fullScreenSupportEnabled"
            write: "setFullScreenSupportEnabled"
            notify: "fullScreenSupportEnabledChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "defaultTextEncoding"
            type: "QString"
            read: "defaultTextEncoding"
            write: "setDefaultTextEncoding"
            notify: "defaultTextEncodingChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "screenCaptureEnabled"
            revision: 258
            type: "bool"
            read: "screenCaptureEnabled"
            write: "setScreenCaptureEnabled"
            notify: "screenCaptureEnabledChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "webGLEnabled"
            revision: 258
            type: "bool"
            read: "webGLEnabled"
            write: "setWebGLEnabled"
            notify: "webGLEnabledChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "accelerated2dCanvasEnabled"
            revision: 258
            type: "bool"
            read: "accelerated2dCanvasEnabled"
            write: "setAccelerated2dCanvasEnabled"
            notify: "accelerated2dCanvasEnabledChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "autoLoadIconsForPage"
            revision: 258
            type: "bool"
            read: "autoLoadIconsForPage"
            write: "setAutoLoadIconsForPage"
            notify: "autoLoadIconsForPageChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "touchIconsEnabled"
            revision: 258
            type: "bool"
            read: "touchIconsEnabled"
            write: "setTouchIconsEnabled"
            notify: "touchIconsEnabledChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "focusOnNavigationEnabled"
            revision: 259
            type: "bool"
            read: "focusOnNavigationEnabled"
            write: "setFocusOnNavigationEnabled"
            notify: "focusOnNavigationEnabledChanged"
            index: 19
            isFinal: true
        }
        Property {
            name: "printElementBackgrounds"
            revision: 259
            type: "bool"
            read: "printElementBackgrounds"
            write: "setPrintElementBackgrounds"
            notify: "printElementBackgroundsChanged"
            index: 20
            isFinal: true
        }
        Property {
            name: "allowRunningInsecureContent"
            revision: 259
            type: "bool"
            read: "allowRunningInsecureContent"
            write: "setAllowRunningInsecureContent"
            notify: "allowRunningInsecureContentChanged"
            index: 21
            isFinal: true
        }
        Property {
            name: "allowGeolocationOnInsecureOrigins"
            revision: 260
            type: "bool"
            read: "allowGeolocationOnInsecureOrigins"
            write: "setAllowGeolocationOnInsecureOrigins"
            notify: "allowGeolocationOnInsecureOriginsChanged"
            index: 22
            isFinal: true
        }
        Property {
            name: "allowWindowActivationFromJavaScript"
            revision: 261
            type: "bool"
            read: "allowWindowActivationFromJavaScript"
            write: "setAllowWindowActivationFromJavaScript"
            notify: "allowWindowActivationFromJavaScriptChanged"
            index: 23
            isFinal: true
        }
        Property {
            name: "showScrollBars"
            revision: 261
            type: "bool"
            read: "showScrollBars"
            write: "setShowScrollBars"
            notify: "showScrollBarsChanged"
            index: 24
            isFinal: true
        }
        Property {
            name: "unknownUrlSchemePolicy"
            revision: 262
            type: "UnknownUrlSchemePolicy"
            read: "unknownUrlSchemePolicy"
            write: "setUnknownUrlSchemePolicy"
            notify: "unknownUrlSchemePolicyChanged"
            index: 25
            isFinal: true
        }
        Property {
            name: "playbackRequiresUserGesture"
            revision: 262
            type: "bool"
            read: "playbackRequiresUserGesture"
            write: "setPlaybackRequiresUserGesture"
            notify: "playbackRequiresUserGestureChanged"
            index: 26
            isFinal: true
        }
        Property {
            name: "webRTCPublicInterfacesOnly"
            revision: 262
            type: "bool"
            read: "webRTCPublicInterfacesOnly"
            write: "setWebRTCPublicInterfacesOnly"
            notify: "webRTCPublicInterfacesOnlyChanged"
            index: 27
            isFinal: true
        }
        Property {
            name: "javascriptCanPaste"
            revision: 262
            type: "bool"
            read: "javascriptCanPaste"
            write: "setJavascriptCanPaste"
            notify: "javascriptCanPasteChanged"
            index: 28
            isFinal: true
        }
        Property {
            name: "dnsPrefetchEnabled"
            revision: 263
            type: "bool"
            read: "dnsPrefetchEnabled"
            write: "setDnsPrefetchEnabled"
            notify: "dnsPrefetchEnabledChanged"
            index: 29
            isFinal: true
        }
        Property {
            name: "pdfViewerEnabled"
            revision: 264
            type: "bool"
            read: "pdfViewerEnabled"
            write: "setPdfViewerEnabled"
            notify: "pdfViewerEnabledChanged"
            index: 30
            isFinal: true
        }
        Property {
            name: "navigateOnDropEnabled"
            revision: 1540
            type: "bool"
            read: "navigateOnDropEnabled"
            write: "setNavigateOnDropEnabled"
            notify: "navigateOnDropEnabledChanged"
            index: 31
            isFinal: true
        }
        Property {
            name: "readingFromCanvasEnabled"
            revision: 1542
            type: "bool"
            read: "readingFromCanvasEnabled"
            write: "setReadingFromCanvasEnabled"
            notify: "readingFromCanvasEnabledChanged"
            index: 32
            isFinal: true
        }
        Property {
            name: "forceDarkMode"
            revision: 1543
            type: "bool"
            read: "forceDarkMode"
            write: "setForceDarkMode"
            notify: "forceDarkModeChanged"
            index: 33
            isFinal: true
        }
        Property {
            name: "scrollAnimatorEnabled"
            revision: 1544
            type: "bool"
            read: "scrollAnimatorEnabled"
            write: "setScrollAnimatorEnabled"
            notify: "scrollAnimatorEnabledChanged"
            index: 34
            isFinal: true
        }
        Property {
            name: "imageAnimationPolicy"
            revision: 1544
            type: "ImageAnimationPolicy"
            read: "imageAnimationPolicy"
            write: "setImageAnimationPolicy"
            notify: "imageAnimationPolicyChanged"
            index: 35
            isFinal: true
        }
        Property {
            name: "printHeaderAndFooter"
            revision: 1545
            type: "bool"
            read: "printHeaderAndFooter"
            write: "setPrintHeaderAndFooter"
            notify: "printHeaderAndFooterChanged"
            index: 36
            isFinal: true
        }
        Property {
            name: "preferCSSMarginsForPrinting"
            revision: 1545
            type: "bool"
            read: "preferCSSMarginsForPrinting"
            write: "setPreferCSSMarginsForPrinting"
            notify: "preferCSSMarginsForPrintingChanged"
            index: 37
            isFinal: true
        }
        Property {
            name: "touchEventsApiEnabled"
            revision: 1545
            type: "bool"
            read: "touchEventsApiEnabled"
            write: "setTouchEventsApiEnabled"
            notify: "touchEventsApiEnabledChanged"
            index: 38
            isFinal: true
        }
        Signal { name: "autoLoadImagesChanged" }
        Signal { name: "javascriptEnabledChanged" }
        Signal { name: "javascriptCanOpenWindowsChanged" }
        Signal { name: "javascriptCanAccessClipboardChanged" }
        Signal { name: "linksIncludedInFocusChainChanged" }
        Signal { name: "localStorageEnabledChanged" }
        Signal { name: "localContentCanAccessRemoteUrlsChanged" }
        Signal { name: "spatialNavigationEnabledChanged" }
        Signal { name: "localContentCanAccessFileUrlsChanged" }
        Signal { name: "hyperlinkAuditingEnabledChanged" }
        Signal { name: "errorPageEnabledChanged" }
        Signal { name: "pluginsEnabledChanged" }
        Signal { name: "fullScreenSupportEnabledChanged"; revision: 257 }
        Signal { name: "defaultTextEncodingChanged" }
        Signal { name: "screenCaptureEnabledChanged"; revision: 258 }
        Signal { name: "webGLEnabledChanged"; revision: 258 }
        Signal { name: "accelerated2dCanvasEnabledChanged"; revision: 258 }
        Signal { name: "autoLoadIconsForPageChanged"; revision: 258 }
        Signal { name: "touchIconsEnabledChanged"; revision: 258 }
        Signal { name: "focusOnNavigationEnabledChanged"; revision: 259 }
        Signal { name: "printElementBackgroundsChanged"; revision: 259 }
        Signal { name: "allowRunningInsecureContentChanged"; revision: 259 }
        Signal { name: "allowGeolocationOnInsecureOriginsChanged"; revision: 260 }
        Signal { name: "allowWindowActivationFromJavaScriptChanged"; revision: 261 }
        Signal { name: "showScrollBarsChanged"; revision: 261 }
        Signal { name: "unknownUrlSchemePolicyChanged"; revision: 262 }
        Signal { name: "playbackRequiresUserGestureChanged"; revision: 262 }
        Signal { name: "webRTCPublicInterfacesOnlyChanged"; revision: 262 }
        Signal { name: "javascriptCanPasteChanged"; revision: 262 }
        Signal { name: "dnsPrefetchEnabledChanged"; revision: 263 }
        Signal { name: "pdfViewerEnabledChanged"; revision: 264 }
        Signal { name: "navigateOnDropEnabledChanged"; revision: 1540 }
        Signal { name: "readingFromCanvasEnabledChanged"; revision: 1542 }
        Signal { name: "forceDarkModeChanged"; revision: 1543 }
        Signal { name: "scrollAnimatorEnabledChanged"; revision: 1544 }
        Signal { name: "imageAnimationPolicyChanged"; revision: 1544 }
        Signal { name: "printHeaderAndFooterChanged"; revision: 1545 }
        Signal { name: "preferCSSMarginsForPrintingChanged"; revision: 1545 }
        Signal { name: "touchEventsApiEnabledChanged"; revision: 1545 }
    }
    Component {
        file: "private/qquickwebenginesingleton_p.h"
        name: "QQuickWebEngineSingleton"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtWebEngine/WebEngine 1.1", "QtWebEngine/WebEngine 6.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [257, 1536]
        Property {
            name: "settings"
            type: "QQuickWebEngineSettings"
            isPointer: true
            read: "settings"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "defaultProfile"
            revision: 257
            type: "QQuickWebEngineProfile"
            isPointer: true
            read: "defaultProfile"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Method { name: "script"; type: "QWebEngineScript"; isMethodConstant: true }
    }
    Component {
        file: "private/qquickwebenginedialogrequests_p.h"
        name: "QQuickWebEngineTooltipRequest"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtWebEngine/TooltipRequest 1.10",
            "QtWebEngine/TooltipRequest 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [266, 1536]
        Enum {
            name: "RequestType"
            values: ["Show", "Hide"]
        }
        Property {
            name: "x"
            type: "int"
            read: "x"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "y"
            type: "int"
            read: "y"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "type"
            type: "RequestType"
            read: "type"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "accepted"
            type: "bool"
            read: "isAccepted"
            write: "setAccepted"
            index: 4
            isFinal: true
        }
    }
    Component {
        file: "private/qquickwebenginetouchselectionmenurequest_p.h"
        name: "QQuickWebEngineTouchSelectionMenuRequest"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtWebEngine/TouchSelectionMenuRequest 6.3"]
        isCreatable: false
        exportMetaObjectRevisions: [1539]
        Enum {
            name: "TouchSelectionCommandFlags"
            alias: "TouchSelectionCommandFlag"
            isFlag: true
            values: ["Cut", "Copy", "Paste"]
        }
        Property {
            name: "accepted"
            type: "bool"
            read: "isAccepted"
            write: "setAccepted"
            index: 0
            isFinal: true
        }
        Property {
            name: "selectionBounds"
            revision: 65281
            type: "QRect"
            read: "selectionBounds"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "touchSelectionCommandFlags"
            revision: 65281
            type: "TouchSelectionCommandFlags"
            read: "touchSelectionCommandFlags"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qquickwebengineview_p.h"
        name: "QQuickWebEngineView"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtWebEngine/WebEngineView 1.0",
            "QtWebEngine/WebEngineView 1.1",
            "QtWebEngine/WebEngineView 1.2",
            "QtWebEngine/WebEngineView 1.3",
            "QtWebEngine/WebEngineView 1.4",
            "QtWebEngine/WebEngineView 1.5",
            "QtWebEngine/WebEngineView 1.7",
            "QtWebEngine/WebEngineView 1.8",
            "QtWebEngine/WebEngineView 1.9",
            "QtWebEngine/WebEngineView 1.10",
            "QtWebEngine/WebEngineView 1.11",
            "QtWebEngine/WebEngineView 1.12",
            "QtWebEngine/WebEngineView 2.0",
            "QtWebEngine/WebEngineView 2.1",
            "QtWebEngine/WebEngineView 2.4",
            "QtWebEngine/WebEngineView 2.7",
            "QtWebEngine/WebEngineView 2.11",
            "QtWebEngine/WebEngineView 6.0",
            "QtWebEngine/WebEngineView 6.3",
            "QtWebEngine/WebEngineView 6.4",
            "QtWebEngine/WebEngineView 6.6",
            "QtWebEngine/WebEngineView 6.7",
            "QtWebEngine/WebEngineView 6.8"
        ]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [
            256,
            257,
            258,
            259,
            260,
            261,
            263,
            264,
            265,
            266,
            267,
            268,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539,
            1540,
            1542,
            1543,
            1544
        ]
        Enum {
            name: "NavigationRequestAction"
            values: ["AcceptRequest", "IgnoreRequest"]
        }
        Enum {
            name: "NavigationType"
            values: [
                "LinkClickedNavigation",
                "TypedNavigation",
                "FormSubmittedNavigation",
                "BackForwardNavigation",
                "ReloadNavigation",
                "OtherNavigation",
                "RedirectNavigation"
            ]
        }
        Enum {
            name: "LoadStatus"
            values: [
                "LoadStartedStatus",
                "LoadStoppedStatus",
                "LoadSucceededStatus",
                "LoadFailedStatus"
            ]
        }
        Enum {
            name: "ErrorDomain"
            values: [
                "NoErrorDomain",
                "InternalErrorDomain",
                "ConnectionErrorDomain",
                "CertificateErrorDomain",
                "HttpErrorDomain",
                "FtpErrorDomain",
                "DnsErrorDomain"
            ]
        }
        Enum {
            name: "NewViewDestination"
            values: [
                "NewViewInWindow",
                "NewViewInTab",
                "NewViewInDialog",
                "NewViewInBackgroundTab"
            ]
        }
        Enum {
            name: "Feature"
            values: [
                "MediaAudioCapture",
                "MediaVideoCapture",
                "MediaAudioVideoCapture",
                "Geolocation",
                "DesktopVideoCapture",
                "DesktopAudioVideoCapture",
                "Notifications",
                "ClipboardReadWrite",
                "LocalFontsAccess"
            ]
        }
        Enum {
            name: "WebAction"
            values: [
                "NoWebAction",
                "Back",
                "Forward",
                "Stop",
                "Reload",
                "Cut",
                "Copy",
                "Paste",
                "Undo",
                "Redo",
                "SelectAll",
                "ReloadAndBypassCache",
                "PasteAndMatchStyle",
                "OpenLinkInThisWindow",
                "OpenLinkInNewWindow",
                "OpenLinkInNewTab",
                "CopyLinkToClipboard",
                "DownloadLinkToDisk",
                "CopyImageToClipboard",
                "CopyImageUrlToClipboard",
                "DownloadImageToDisk",
                "CopyMediaUrlToClipboard",
                "ToggleMediaControls",
                "ToggleMediaLoop",
                "ToggleMediaPlayPause",
                "ToggleMediaMute",
                "DownloadMediaToDisk",
                "InspectElement",
                "ExitFullScreen",
                "RequestClose",
                "Unselect",
                "SavePage",
                "OpenLinkInNewBackgroundTab",
                "ViewSource",
                "ToggleBold",
                "ToggleItalic",
                "ToggleUnderline",
                "ToggleStrikethrough",
                "AlignLeft",
                "AlignCenter",
                "AlignRight",
                "AlignJustified",
                "Indent",
                "Outdent",
                "InsertOrderedList",
                "InsertUnorderedList",
                "ChangeTextDirectionLTR",
                "ChangeTextDirectionRTL",
                "WebActionCount"
            ]
        }
        Enum {
            name: "JavaScriptConsoleMessageLevel"
            values: [
                "InfoMessageLevel",
                "WarningMessageLevel",
                "ErrorMessageLevel"
            ]
        }
        Enum {
            name: "RenderProcessTerminationStatus"
            values: [
                "NormalTerminationStatus",
                "AbnormalTerminationStatus",
                "CrashedTerminationStatus",
                "KilledTerminationStatus"
            ]
        }
        Enum {
            name: "FindFlags"
            alias: "FindFlag"
            isFlag: true
            values: ["FindBackward", "FindCaseSensitively"]
        }
        Enum {
            name: "PrintedPageSizeId"
            values: [
                "Letter",
                "Legal",
                "Executive",
                "A0",
                "A1",
                "A2",
                "A3",
                "A4",
                "A5",
                "A6",
                "A7",
                "A8",
                "A9",
                "A10",
                "B0",
                "B1",
                "B2",
                "B3",
                "B4",
                "B5",
                "B6",
                "B7",
                "B8",
                "B9",
                "B10",
                "C5E",
                "Comm10E",
                "DLE",
                "Folio",
                "Ledger",
                "Tabloid",
                "Custom",
                "A3Extra",
                "A4Extra",
                "A4Plus",
                "A4Small",
                "A5Extra",
                "B5Extra",
                "JisB0",
                "JisB1",
                "JisB2",
                "JisB3",
                "JisB4",
                "JisB5",
                "JisB6",
                "JisB7",
                "JisB8",
                "JisB9",
                "JisB10",
                "AnsiC",
                "AnsiD",
                "AnsiE",
                "LegalExtra",
                "LetterExtra",
                "LetterPlus",
                "LetterSmall",
                "TabloidExtra",
                "ArchA",
                "ArchB",
                "ArchC",
                "ArchD",
                "ArchE",
                "Imperial7x9",
                "Imperial8x10",
                "Imperial9x11",
                "Imperial9x12",
                "Imperial10x11",
                "Imperial10x13",
                "Imperial10x14",
                "Imperial12x11",
                "Imperial15x11",
                "ExecutiveStandard",
                "Note",
                "Quarto",
                "Statement",
                "SuperA",
                "SuperB",
                "Postcard",
                "DoublePostcard",
                "Prc16K",
                "Prc32K",
                "Prc32KBig",
                "FanFoldUS",
                "FanFoldGerman",
                "FanFoldGermanLegal",
                "EnvelopeB4",
                "EnvelopeB5",
                "EnvelopeB6",
                "EnvelopeC0",
                "EnvelopeC1",
                "EnvelopeC2",
                "EnvelopeC3",
                "EnvelopeC4",
                "EnvelopeC6",
                "EnvelopeC65",
                "EnvelopeC7",
                "Envelope9",
                "Envelope11",
                "Envelope12",
                "Envelope14",
                "EnvelopeMonarch",
                "EnvelopePersonal",
                "EnvelopeChou3",
                "EnvelopeChou4",
                "EnvelopeInvite",
                "EnvelopeItalian",
                "EnvelopeKaku2",
                "EnvelopeKaku3",
                "EnvelopePrc1",
                "EnvelopePrc2",
                "EnvelopePrc3",
                "EnvelopePrc4",
                "EnvelopePrc5",
                "EnvelopePrc6",
                "EnvelopePrc7",
                "EnvelopePrc8",
                "EnvelopePrc9",
                "EnvelopePrc10",
                "EnvelopeYou4",
                "LastPageSize",
                "AnsiA",
                "AnsiB",
                "EnvelopeC5",
                "EnvelopeDL",
                "Envelope10"
            ]
        }
        Enum {
            name: "PrintedPageOrientation"
            values: ["Portrait", "Landscape"]
        }
        Enum {
            name: "LifecycleState"
            isScoped: true
            values: ["Active", "Frozen", "Discarded"]
        }
        Property {
            name: "url"
            type: "QUrl"
            read: "url"
            write: "setUrl"
            notify: "urlChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "icon"
            type: "QUrl"
            read: "icon"
            notify: "iconChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "loading"
            type: "bool"
            read: "isLoading"
            notify: "loadingChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "loadProgress"
            type: "int"
            read: "loadProgress"
            notify: "loadProgressChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            notify: "titleChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "canGoBack"
            type: "bool"
            read: "canGoBack"
            notify: "canGoBackChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "canGoForward"
            type: "bool"
            read: "canGoForward"
            notify: "canGoForwardChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "isFullScreen"
            revision: 257
            type: "bool"
            read: "isFullScreen"
            notify: "isFullScreenChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "zoomFactor"
            revision: 257
            type: "double"
            read: "zoomFactor"
            write: "setZoomFactor"
            notify: "zoomFactorChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "profile"
            revision: 257
            type: "QQuickWebEngineProfile"
            isPointer: true
            read: "profile"
            write: "setProfile"
            notify: "profileChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "settings"
            revision: 257
            type: "QQuickWebEngineSettings"
            isPointer: true
            read: "settings"
            index: 10
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "history"
            revision: 257
            type: "QWebEngineHistory"
            isPointer: true
            read: "history"
            index: 11
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "webChannel"
            revision: 257
            type: "QQmlWebChannel"
            isPointer: true
            read: "webChannel"
            write: "setWebChannel"
            notify: "webChannelChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "userScripts"
            revision: 257
            type: "QQuickWebEngineScriptCollection"
            isPointer: true
            read: "userScripts"
            index: 13
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "activeFocusOnPress"
            revision: 258
            type: "bool"
            read: "activeFocusOnPress"
            write: "setActiveFocusOnPress"
            notify: "activeFocusOnPressChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "backgroundColor"
            revision: 258
            type: "QColor"
            read: "backgroundColor"
            write: "setBackgroundColor"
            notify: "backgroundColorChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "contentsSize"
            revision: 259
            type: "QSizeF"
            read: "contentsSize"
            notify: "contentsSizeChanged"
            index: 16
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "scrollPosition"
            revision: 259
            type: "QPointF"
            read: "scrollPosition"
            notify: "scrollPositionChanged"
            index: 17
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "audioMuted"
            revision: 259
            type: "bool"
            read: "isAudioMuted"
            write: "setAudioMuted"
            notify: "audioMutedChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "recentlyAudible"
            revision: 259
            type: "bool"
            read: "recentlyAudible"
            notify: "recentlyAudibleChanged"
            index: 19
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "webChannelWorld"
            revision: 259
            type: "uint"
            read: "webChannelWorld"
            write: "setWebChannelWorld"
            notify: "webChannelWorldChanged"
            index: 20
            isFinal: true
        }
        Property {
            name: "inspectedView"
            revision: 263
            type: "QQuickWebEngineView"
            isPointer: true
            read: "inspectedView"
            write: "setInspectedView"
            notify: "inspectedViewChanged"
            index: 21
            isFinal: true
        }
        Property {
            name: "devToolsView"
            revision: 263
            type: "QQuickWebEngineView"
            isPointer: true
            read: "devToolsView"
            write: "setDevToolsView"
            notify: "devToolsViewChanged"
            index: 22
            isFinal: true
        }
        Property {
            name: "devToolsId"
            revision: 1542
            type: "QString"
            read: "devToolsId"
            index: 23
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "lifecycleState"
            revision: 266
            type: "LifecycleState"
            read: "lifecycleState"
            write: "setLifecycleState"
            notify: "lifecycleStateChanged"
            index: 24
            isFinal: true
        }
        Property {
            name: "recommendedState"
            revision: 266
            type: "LifecycleState"
            read: "recommendedState"
            notify: "recommendedStateChanged"
            index: 25
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "renderProcessPid"
            revision: 267
            type: "qlonglong"
            read: "renderProcessPid"
            notify: "renderProcessPidChanged"
            index: 26
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "touchHandleDelegate"
            revision: 65280
            type: "QQmlComponent"
            isPointer: true
            read: "touchHandleDelegate"
            write: "setTouchHandleDelegate"
            notify: "touchHandleDelegateChanged"
            index: 27
            isFinal: true
        }
        Property {
            name: "mainFrame"
            revision: 1544
            type: "QWebEngineFrame"
            read: "mainFrame"
            index: 28
            isReadonly: true
            isFinal: true
        }
        Signal { name: "titleChanged" }
        Signal { name: "urlChanged" }
        Signal { name: "iconChanged" }
        Signal {
            name: "loadingChanged"
            Parameter { name: "loadingInfo"; type: "QWebEngineLoadingInfo" }
        }
        Signal { name: "loadProgressChanged" }
        Signal {
            name: "linkHovered"
            Parameter { name: "hoveredUrl"; type: "QUrl" }
        }
        Signal {
            name: "navigationRequested"
            Parameter { name: "request"; type: "QWebEngineNavigationRequest"; isPointer: true }
        }
        Signal {
            name: "javaScriptConsoleMessage"
            Parameter { name: "level"; type: "QQuickWebEngineView::JavaScriptConsoleMessageLevel" }
            Parameter { name: "message"; type: "QString" }
            Parameter { name: "lineNumber"; type: "int" }
            Parameter { name: "sourceID"; type: "QString" }
        }
        Signal {
            name: "certificateError"
            revision: 257
            Parameter { name: "error"; type: "QWebEngineCertificateError" }
        }
        Signal {
            name: "fullScreenRequested"
            revision: 257
            Parameter { name: "request"; type: "QWebEngineFullScreenRequest" }
        }
        Signal { name: "isFullScreenChanged"; revision: 257 }
        Signal {
            name: "featurePermissionRequested"
            revision: 257
            Parameter { name: "securityOrigin"; type: "QUrl" }
            Parameter { name: "feature"; type: "QQuickWebEngineView::Feature" }
        }
        Signal {
            name: "zoomFactorChanged"
            revision: 257
            Parameter { name: "arg"; type: "double" }
        }
        Signal { name: "profileChanged"; revision: 257 }
        Signal { name: "webChannelChanged"; revision: 257 }
        Signal {
            name: "activeFocusOnPressChanged"
            revision: 258
            Parameter { type: "bool" }
        }
        Signal { name: "backgroundColorChanged"; revision: 258 }
        Signal {
            name: "renderProcessTerminated"
            revision: 258
            Parameter {
                name: "terminationStatus"
                type: "QQuickWebEngineView::RenderProcessTerminationStatus"
            }
            Parameter { name: "exitCode"; type: "int" }
        }
        Signal { name: "windowCloseRequested"; revision: 258 }
        Signal {
            name: "contentsSizeChanged"
            revision: 259
            Parameter { name: "size"; type: "QSizeF" }
        }
        Signal {
            name: "scrollPositionChanged"
            revision: 259
            Parameter { name: "position"; type: "QPointF" }
        }
        Signal {
            name: "audioMutedChanged"
            revision: 259
            Parameter { name: "muted"; type: "bool" }
        }
        Signal {
            name: "recentlyAudibleChanged"
            revision: 259
            Parameter { name: "recentlyAudible"; type: "bool" }
        }
        Signal {
            name: "webChannelWorldChanged"
            revision: 259
            Parameter { type: "uint" }
        }
        Signal {
            name: "contextMenuRequested"
            revision: 260
            Parameter { name: "request"; type: "QWebEngineContextMenuRequest"; isPointer: true }
        }
        Signal {
            name: "authenticationDialogRequested"
            revision: 260
            Parameter {
                name: "request"
                type: "QQuickWebEngineAuthenticationDialogRequest"
                isPointer: true
            }
        }
        Signal {
            name: "javaScriptDialogRequested"
            revision: 260
            Parameter { name: "request"; type: "QQuickWebEngineJavaScriptDialogRequest"; isPointer: true }
        }
        Signal {
            name: "colorDialogRequested"
            revision: 260
            Parameter { name: "request"; type: "QQuickWebEngineColorDialogRequest"; isPointer: true }
        }
        Signal {
            name: "fileDialogRequested"
            revision: 260
            Parameter { name: "request"; type: "QQuickWebEngineFileDialogRequest"; isPointer: true }
        }
        Signal {
            name: "pdfPrintingFinished"
            revision: 261
            Parameter { name: "filePath"; type: "QString" }
            Parameter { name: "success"; type: "bool" }
        }
        Signal {
            name: "quotaRequested"
            revision: 263
            Parameter { name: "request"; type: "QWebEngineQuotaRequest" }
        }
        Signal {
            name: "geometryChangeRequested"
            revision: 263
            Parameter { name: "geometry"; type: "QRect" }
            Parameter { name: "frameGeometry"; type: "QRect" }
        }
        Signal { name: "inspectedViewChanged"; revision: 263 }
        Signal { name: "devToolsViewChanged"; revision: 263 }
        Signal {
            name: "registerProtocolHandlerRequested"
            revision: 263
            Parameter { name: "request"; type: "QWebEngineRegisterProtocolHandlerRequest" }
        }
        Signal { name: "printRequested"; revision: 264 }
        Signal {
            name: "selectClientCertificate"
            revision: 265
            Parameter {
                name: "clientCertSelection"
                type: "QQuickWebEngineClientCertificateSelection"
                isPointer: true
            }
        }
        Signal {
            name: "tooltipRequested"
            revision: 266
            Parameter { name: "request"; type: "QQuickWebEngineTooltipRequest"; isPointer: true }
        }
        Signal {
            name: "lifecycleStateChanged"
            revision: 266
            Parameter { name: "state"; type: "QQuickWebEngineView::LifecycleState" }
        }
        Signal {
            name: "recommendedStateChanged"
            revision: 266
            Parameter { name: "state"; type: "QQuickWebEngineView::LifecycleState" }
        }
        Signal {
            name: "findTextFinished"
            revision: 266
            Parameter { name: "result"; type: "QWebEngineFindTextResult" }
        }
        Signal {
            name: "renderProcessPidChanged"
            revision: 267
            Parameter { name: "pid"; type: "qlonglong" }
        }
        Signal { name: "canGoBackChanged"; revision: 267 }
        Signal { name: "canGoForwardChanged"; revision: 267 }
        Signal {
            name: "newWindowRequested"
            revision: 268
            Parameter { name: "request"; type: "QQuickWebEngineNewWindowRequest"; isPointer: true }
        }
        Signal {
            name: "touchSelectionMenuRequested"
            revision: 1539
            Parameter { name: "request"; type: "QQuickWebEngineTouchSelectionMenuRequest"; isPointer: true }
        }
        Signal { name: "touchHandleDelegateChanged"; revision: 1540 }
        Signal {
            name: "fileSystemAccessRequested"
            revision: 1540
            Parameter { name: "request"; type: "QWebEngineFileSystemAccessRequest" }
        }
        Signal {
            name: "webAuthUxRequested"
            revision: 1543
            Parameter { name: "request"; type: "QWebEngineWebAuthUxRequest"; isPointer: true }
        }
        Signal {
            name: "desktopMediaRequested"
            revision: 1543
            Parameter { name: "request"; type: "QWebEngineDesktopMediaRequest" }
        }
        Signal {
            name: "printRequestedByFrame"
            revision: 1544
            Parameter { name: "frame"; type: "QWebEngineFrame" }
        }
        Signal {
            name: "permissionRequested"
            revision: 1544
            Parameter { name: "permissionRequest"; type: "QWebEnginePermission" }
        }
        Method {
            name: "runJavaScript"
            Parameter { type: "QString" }
            Parameter { type: "QJSValue" }
        }
        Method {
            name: "runJavaScript"
            isCloned: true
            Parameter { type: "QString" }
        }
        Method {
            name: "runJavaScript"
            revision: 259
            Parameter { type: "QString" }
            Parameter { name: "worldId"; type: "uint" }
            Parameter { type: "QJSValue" }
        }
        Method {
            name: "runJavaScript"
            revision: 259
            isCloned: true
            Parameter { type: "QString" }
            Parameter { name: "worldId"; type: "uint" }
        }
        Method {
            name: "loadHtml"
            Parameter { name: "html"; type: "QString" }
            Parameter { name: "baseUrl"; type: "QUrl" }
        }
        Method {
            name: "loadHtml"
            isCloned: true
            Parameter { name: "html"; type: "QString" }
        }
        Method { name: "goBack" }
        Method { name: "goForward" }
        Method {
            name: "goBackOrForward"
            revision: 257
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "reload" }
        Method { name: "reloadAndBypassCache"; revision: 257 }
        Method { name: "stop" }
        Method {
            name: "findText"
            revision: 257
            Parameter { name: "subString"; type: "QString" }
            Parameter { name: "options"; type: "FindFlags" }
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "findText"
            revision: 257
            isCloned: true
            Parameter { name: "subString"; type: "QString" }
            Parameter { name: "options"; type: "FindFlags" }
        }
        Method {
            name: "findText"
            revision: 257
            isCloned: true
            Parameter { name: "subString"; type: "QString" }
        }
        Method { name: "fullScreenCancelled"; revision: 257 }
        Method {
            name: "grantFeaturePermission"
            revision: 257
            Parameter { name: "securityOrigin"; type: "QUrl" }
            Parameter { type: "QQuickWebEngineView::Feature" }
            Parameter { name: "granted"; type: "bool" }
        }
        Method {
            name: "setActiveFocusOnPress"
            revision: 258
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "triggerWebAction"
            revision: 258
            Parameter { name: "action"; type: "WebAction" }
        }
        Method {
            name: "printToPdf"
            revision: 259
            Parameter { name: "filePath"; type: "QString" }
            Parameter { name: "pageSizeId"; type: "PrintedPageSizeId" }
            Parameter { name: "orientation"; type: "PrintedPageOrientation" }
        }
        Method {
            name: "printToPdf"
            revision: 259
            isCloned: true
            Parameter { name: "filePath"; type: "QString" }
            Parameter { name: "pageSizeId"; type: "PrintedPageSizeId" }
        }
        Method {
            name: "printToPdf"
            revision: 259
            isCloned: true
            Parameter { name: "filePath"; type: "QString" }
        }
        Method {
            name: "printToPdf"
            revision: 259
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "pageSizeId"; type: "PrintedPageSizeId" }
            Parameter { name: "orientation"; type: "PrintedPageOrientation" }
        }
        Method {
            name: "printToPdf"
            revision: 259
            isCloned: true
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "pageSizeId"; type: "PrintedPageSizeId" }
        }
        Method {
            name: "printToPdf"
            revision: 259
            isCloned: true
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "replaceMisspelledWord"
            revision: 260
            Parameter { name: "replacement"; type: "QString" }
        }
        Method {
            name: "save"
            revision: 1542
            isMethodConstant: true
            Parameter { name: "filePath"; type: "QString" }
            Parameter { name: "format"; type: "QWebEngineDownloadRequest::SavePageFormat" }
        }
        Method {
            name: "save"
            revision: 1542
            isCloned: true
            isMethodConstant: true
            Parameter { name: "filePath"; type: "QString" }
        }
        Method { name: "lazyInitialize" }
        Method {
            name: "action"
            revision: 264
            type: "QQuickWebEngineAction"
            isPointer: true
            Parameter { name: "action"; type: "WebAction" }
        }
        Method {
            name: "acceptAsNewWindow"
            Parameter { name: "request"; type: "QWebEngineNewWindowRequest"; isPointer: true }
        }
        Method {
            name: "findFrameByName"
            revision: 1544
            type: "QWebEngineFrame"
            Parameter { name: "name"; type: "QString" }
        }
    }
    Component {
        file: "qwebenginedownloadrequest.h"
        name: "QWebEngineDownloadRequest"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "DownloadState"
            values: [
                "DownloadRequested",
                "DownloadInProgress",
                "DownloadCompleted",
                "DownloadCancelled",
                "DownloadInterrupted"
            ]
        }
        Enum {
            name: "SavePageFormat"
            values: [
                "UnknownSaveFormat",
                "SingleHtmlSaveFormat",
                "CompleteHtmlSaveFormat",
                "MimeHtmlSaveFormat"
            ]
        }
        Enum {
            name: "DownloadInterruptReason"
            values: [
                "NoReason",
                "FileFailed",
                "FileAccessDenied",
                "FileNoSpace",
                "FileNameTooLong",
                "FileTooLarge",
                "FileVirusInfected",
                "FileTransientError",
                "FileBlocked",
                "FileSecurityCheckFailed",
                "FileTooShort",
                "FileHashMismatch",
                "NetworkFailed",
                "NetworkTimeout",
                "NetworkDisconnected",
                "NetworkServerDown",
                "NetworkInvalidRequest",
                "ServerFailed",
                "ServerBadContent",
                "ServerUnauthorized",
                "ServerCertProblem",
                "ServerForbidden",
                "ServerUnreachable",
                "UserCanceled"
            ]
        }
        Property {
            name: "id"
            type: "uint"
            read: "id"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "state"
            type: "DownloadState"
            read: "state"
            notify: "stateChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "savePageFormat"
            type: "SavePageFormat"
            read: "savePageFormat"
            write: "setSavePageFormat"
            notify: "savePageFormatChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "totalBytes"
            type: "qlonglong"
            read: "totalBytes"
            notify: "totalBytesChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "receivedBytes"
            type: "qlonglong"
            read: "receivedBytes"
            notify: "receivedBytesChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "mimeType"
            type: "QString"
            read: "mimeType"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "interruptReason"
            type: "DownloadInterruptReason"
            read: "interruptReason"
            notify: "interruptReasonChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "interruptReasonString"
            type: "QString"
            read: "interruptReasonString"
            notify: "interruptReasonChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "isFinished"
            type: "bool"
            read: "isFinished"
            notify: "isFinishedChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "isPaused"
            type: "bool"
            read: "isPaused"
            notify: "isPausedChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "isSavePageDownload"
            type: "bool"
            read: "isSavePageDownload"
            index: 10
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "url"
            type: "QUrl"
            read: "url"
            index: 11
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "suggestedFileName"
            type: "QString"
            read: "suggestedFileName"
            index: 12
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "downloadDirectory"
            type: "QString"
            read: "downloadDirectory"
            write: "setDownloadDirectory"
            notify: "downloadDirectoryChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "downloadFileName"
            type: "QString"
            read: "downloadFileName"
            write: "setDownloadFileName"
            notify: "downloadFileNameChanged"
            index: 14
            isFinal: true
        }
        Signal {
            name: "stateChanged"
            Parameter { name: "state"; type: "QWebEngineDownloadRequest::DownloadState" }
        }
        Signal { name: "savePageFormatChanged" }
        Signal { name: "receivedBytesChanged" }
        Signal { name: "totalBytesChanged" }
        Signal { name: "interruptReasonChanged" }
        Signal { name: "isFinishedChanged" }
        Signal { name: "isPausedChanged" }
        Signal { name: "downloadDirectoryChanged" }
        Signal { name: "downloadFileNameChanged" }
        Method { name: "accept" }
        Method { name: "cancel" }
        Method { name: "pause" }
        Method { name: "resume" }
    }
    Component {
        file: "qwebenginenewwindowrequest.h"
        name: "QWebEngineNewWindowRequest"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "DestinationType"
            values: [
                "InNewWindow",
                "InNewTab",
                "InNewDialog",
                "InNewBackgroundTab"
            ]
        }
        Property {
            name: "destination"
            type: "DestinationType"
            read: "destination"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "requestedUrl"
            type: "QUrl"
            read: "requestedUrl"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "requestedGeometry"
            type: "QRect"
            read: "requestedGeometry"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "userInitiated"
            type: "bool"
            read: "isUserInitiated"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
    }
}
