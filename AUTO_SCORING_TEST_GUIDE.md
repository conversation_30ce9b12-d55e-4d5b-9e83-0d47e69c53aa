# 自動評分功能測試指南

## 測試步驟

### 1. 啟動程式
```bash
# 在虛擬環境中
.venv\Scripts\activate.ps1
cd code
python gui_main.py
```

### 2. 啟動攝影機
- 點擊攝影機畫面區域啟動攝影機
- 等待攝影機初始化完成

### 3. 啟用自動評分
- 在右側控制面板中找到"Auto Scoring"區塊
- 勾選"Enable Auto Scoring"選項
- 狀態應該從"Disabled"變為"Waiting for person..."

### 4. 測試自動評分流程
1. **進入畫面**: 走進攝影機畫面範圍
   - 系統會檢測到人物並開始收集數據
   - 控制台會顯示"Auto scoring: Starting session"

2. **保持在畫面中**: 在攝影機前走動至少3秒
   - 系統會持續收集角度和速度數據
   - 控制台會定期顯示數據收集進度

3. **離開畫面**: 走出攝影機畫面範圍
   - 系統會檢測到人物離開
   - 控制台會顯示"Auto scoring: Ending session"
   - 如果數據足夠，會彈出評分結果對話框

### 5. 查看評分結果
- 評分結果對話框會顯示：
  - 總得分（大字體）
  - 評分等級（優秀/良好/不佳）
  - 平均角度得分
  - 平均速度得分
  - 會話時間
- 對話框會在15秒後自動關閉
- 也可以點擊"Close Now"按鈕立即關閉

## 調試信息

程式會在控制台輸出調試信息，包括：
- 自動評分開關狀態變化
- 人物檢測和會話管理
- 數據收集進度
- 評分結果計算

## 常見問題

### 1. 狀態沒有更新
- 確保正確勾選了"Enable Auto Scoring"選項
- 檢查控制台是否有相關調試信息

### 2. 沒有檢測到人物
- 確保攝影機正常工作
- 確保人物在攝影機畫面中清晰可見
- 檢查姿態檢測是否正常工作

### 3. 沒有彈出評分結果
- 確保在畫面中停留至少3秒
- 確保收集了至少10個數據樣本
- 檢查控制台的調試信息

### 4. 評分結果不合理
- 檢查角度和速度計算是否正常
- 確保在測試期間保持正常的走路姿態
- 檢查settings.yaml中的評分權重設定

## 測試腳本

可以使用以下測試腳本進行自動化測試：

```bash
# 基本功能測試
python debug_auto_scoring.py

# 完整流程測試
python test_complete_flow.py
```

## 配置調整

可以在`settings.yaml`中調整自動評分參數：

```yaml
auto_scoring:
  detection:
    enter_threshold: 5      # 確認進入的幀數
    exit_threshold: 10      # 確認離開的幀數
    min_session_duration: 3.0  # 最小會話時間
  calculation:
    min_samples: 5          # 最小樣本數

# 評分權重在 gait_analysis 區塊中統一設定：
gait_analysis:
  scoring:
    angle_weight: 0.3       # 角度得分權重（30%）
    speed_weight: 0.7       # 速度得分權重（70%）
```
