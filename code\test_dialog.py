#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for auto scoring result dialog
"""

import sys
from PySide6.QtWidgets import QApplication
from gui_main import AutoScoringResultDialog

def test_dialog():
    """Test the auto scoring result dialog"""
    app = QApplication(sys.argv)
    
    # Create test result data
    test_result = {
        'final_score': 75.3,
        'avg_angle_score': 78.5,
        'avg_speed_score': 71.2,
        'level': 'good',
        'session_duration': 5.2,
        'sample_count': 156
    }
    
    print("Creating AutoScoringResultDialog...")
    print(f"Test result: {test_result}")
    
    # Create and show dialog
    dialog = AutoScoringResultDialog(test_result)
    dialog.show()
    
    print("Dialog created and shown")
    
    return app.exec()

if __name__ == "__main__":
    test_dialog()
