<!--
// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<modify-function signature="^glBindBuffersBase\(.*$">
    <modify-argument index="4"><array/></modify-argument>
</modify-function>
<modify-function signature="glBindBuffersRange(GLenum,GLuint,GLsizei,const GLuint*,const GLintptr*,const GLsizeiptr*)">
    <modify-argument index="4"><array/></modify-argument>
    <modify-argument index="5"><array/></modify-argument>
    <modify-argument index="6"><array/></modify-argument>
</modify-function>
<modify-function signature="glBindVertexBuffers(GLuint,GLsizei,const GLuint*,const GLintptr*,const GLsizei*)">
    <modify-argument index="3"><array/></modify-argument>
    <modify-argument index="4"><array/></modify-argument>
    <modify-argument index="5"><array/></modify-argument>
</modify-function>
<modify-function signature="^glBind(ImageTextures|Samplers|Textures)\(.*$">
    <modify-argument index="3"><array/></modify-argument>
</modify-function>
