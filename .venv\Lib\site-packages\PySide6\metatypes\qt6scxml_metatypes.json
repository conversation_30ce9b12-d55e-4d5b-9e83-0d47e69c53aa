[{"classes": [{"className": "QScxmlCppDataModel", "lineNumber": 20, "methods": [{"access": "public", "arguments": [{"name": "initialDataValues", "type": "QVariantMap"}], "index": 0, "name": "setup", "returnType": "bool"}], "object": true, "qualifiedClassName": "QScxmlCppDataModel", "superClasses": [{"access": "public", "name": "QScxmlDataModel"}]}], "inputFile": "qscxmlcppdatamodel.h", "outputRevision": 69}, {"classes": [{"className": "QScxmlDataModel", "lineNumber": 21, "methods": [{"access": "public", "arguments": [{"name": "initialDataValues", "type": "QVariantMap"}], "index": 1, "name": "setup", "returnType": "bool"}], "object": true, "properties": [{"bindable": "bindableStateMachine", "constant": false, "designable": true, "final": false, "index": 0, "name": "stateMachine", "notify": "stateMachineChanged", "read": "stateMachine", "required": false, "scriptable": true, "stored": true, "type": "QScxmlStateMachine*", "user": false, "write": "setStateMachine"}], "qualifiedClassName": "QScxmlDataModel", "signals": [{"access": "public", "arguments": [{"name": "stateMachine", "type": "QScxmlStateMachine*"}], "index": 0, "name": "stateMachineChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qscxmldatamodel.h", "outputRevision": 69}, {"classes": [{"className": "QScxmlDataModelPlugin", "lineNumber": 26, "object": true, "qualifiedClassName": "QScxmlDataModelPlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qscxmldatamodelplugin_p.h", "outputRevision": 69}, {"classes": [{"className": "QScxmlError", "gadget": true, "lineNumber": 14, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "valid", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "fileName", "read": "fileName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "line", "read": "line", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "column", "read": "column", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "description", "read": "description", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "QScxmlError"}], "inputFile": "qscxmlerror.h", "outputRevision": 69}, {"classes": [{"className": "QScxmlEvent", "enums": [{"isClass": false, "isFlag": false, "name": "EventType", "values": ["PlatformEvent", "InternalEvent", "ExternalEvent"]}], "gadget": true, "lineNumber": 16, "methods": [{"access": "public", "index": 0, "name": "clear", "returnType": "void"}], "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "eventType", "read": "eventType", "required": false, "scriptable": true, "stored": true, "type": "EventType", "user": false, "write": "setEventType"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "scxmlType", "read": "scxmlType", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "sendId", "read": "sendId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setSendId"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "origin", "read": "origin", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "originType", "read": "originType", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setOriginType"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "invokeId", "read": "invokeId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setInvokeId"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "delay", "read": "delay", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "data", "read": "data", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setData"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "errorEvent", "read": "isErrorEvent", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "errorMessage", "read": "errorMessage", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setErrorMessage"}], "qualifiedClassName": "QScxmlEvent"}], "inputFile": "qscxmlevent.h", "outputRevision": 69}, {"classes": [{"className": "QScxmlInvokableService", "lineNumber": 17, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "parentStateMachine", "read": "parentStateMachine", "required": false, "scriptable": true, "stored": true, "type": "QScxmlStateMachine*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "id", "read": "id", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "QScxmlInvokableService", "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QScxmlInvokableServiceFactory", "lineNumber": 38, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "invokeInfo", "read": "invokeInfo", "required": false, "scriptable": true, "stored": true, "type": "QScxmlExecutableContent::InvokeInfo", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "parameters", "read": "parameters", "required": false, "scriptable": true, "stored": true, "type": "QList<QScxmlExecutableContent::ParameterInfo>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "names", "read": "names", "required": false, "scriptable": true, "stored": true, "type": "QList<QScxmlExecutableContent::StringId>", "user": false}], "qualifiedClassName": "QScxmlInvokableServiceFactory", "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QScxmlStaticScxmlServiceFactory", "lineNumber": 63, "object": true, "qualifiedClassName": "QScxmlStaticScxmlServiceFactory", "superClasses": [{"access": "public", "name": "QScxmlInvokableServiceFactory"}]}, {"className": "QScxmlDynamicScxmlServiceFactory", "lineNumber": 78, "object": true, "qualifiedClassName": "QScxmlDynamicScxmlServiceFactory", "superClasses": [{"access": "public", "name": "QScxmlInvokableServiceFactory"}]}], "inputFile": "qscxmlinvokableservice.h", "outputRevision": 69}, {"classes": [{"className": "QScxmlScxmlService", "lineNumber": 51, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "stateMachine", "read": "stateMachine", "required": false, "scriptable": true, "stored": true, "type": "QScxmlStateMachine*", "user": false}], "qualifiedClassName": "QScxmlScxmlService", "superClasses": [{"access": "public", "name": "QScxmlInvokableService"}]}], "inputFile": "qscxmlinvokableservice_p.h", "outputRevision": 69}, {"classes": [{"className": "QScxmlNullDataModel", "lineNumber": 12, "methods": [{"access": "public", "arguments": [{"name": "initialDataValues", "type": "QVariantMap"}], "index": 0, "name": "setup", "returnType": "bool"}], "object": true, "qualifiedClassName": "QScxmlNullDataModel", "superClasses": [{"access": "public", "name": "QScxmlDataModel"}]}], "inputFile": "qscxmlnulldatamodel.h", "outputRevision": 69}, {"classes": [{"className": "QScxmlStateMachine", "lineNumber": 30, "methods": [{"access": "public", "arguments": [{"name": "compress", "type": "bool"}], "index": 13, "isConst": true, "name": "stateNames", "returnType": "QStringList"}, {"access": "public", "index": 14, "isCloned": true, "isConst": true, "name": "stateNames", "returnType": "QStringList"}, {"access": "public", "arguments": [{"name": "compress", "type": "bool"}], "index": 15, "isConst": true, "name": "activeStateNames", "returnType": "QStringList"}, {"access": "public", "index": 16, "isCloned": true, "isConst": true, "name": "activeStateNames", "returnType": "QStringList"}, {"access": "public", "arguments": [{"name": "scxmlStateName", "type": "QString"}], "index": 17, "isConst": true, "name": "isActive", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "event", "type": "QScxmlEvent*"}], "index": 18, "name": "submitEvent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "eventName", "type": "QString"}], "index": 19, "name": "submitEvent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "eventName", "type": "QString"}, {"name": "data", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 20, "name": "submitEvent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sendId", "type": "QString"}], "index": 21, "name": "cancelDelayedEvent", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "QString"}], "index": 22, "isConst": true, "name": "isDispatchableTarget", "returnType": "bool"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "running", "notify": "running<PERSON><PERSON>ed", "read": "isRunning", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRunning"}, {"bindable": "bindableInitialized", "constant": false, "designable": true, "final": false, "index": 1, "name": "initialized", "notify": "initializedChanged", "read": "isInitialized", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"bindable": "bindableDataModel", "constant": false, "designable": true, "final": false, "index": 2, "name": "dataModel", "notify": "dataModelChanged", "read": "dataModel", "required": false, "scriptable": true, "stored": true, "type": "QScxmlDataModel*", "user": false, "write": "setDataModel"}, {"bindable": "bindableInitialValues", "constant": false, "designable": true, "final": false, "index": 3, "name": "initialValues", "notify": "initialValuesChanged", "read": "initialValues", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false, "write": "setInitialValues"}, {"bindable": "bindableInvokedServices", "constant": false, "designable": true, "final": false, "index": 4, "name": "invokedServices", "notify": "invokedServicesChanged", "read": "invokedServices", "required": false, "scriptable": true, "stored": true, "type": "QList<QScxmlInvokableService*>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "sessionId", "read": "sessionId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "invoked", "read": "isInvoked", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "parseErrors", "read": "parseErrors", "required": false, "scriptable": true, "stored": true, "type": "QList<QScxmlError>", "user": false}, {"bindable": "bindableLoader", "constant": false, "designable": true, "final": false, "index": 9, "name": "loader", "notify": "loaderChanged", "read": "loader", "required": false, "scriptable": true, "stored": true, "type": "QScxmlCompiler::Loader*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"bindable": "bindableTableData", "constant": false, "designable": true, "final": false, "index": 10, "name": "tableData", "notify": "tableDataChanged", "read": "tableData", "required": false, "scriptable": true, "stored": true, "type": "QScxmlTableData*", "user": false, "write": "setTableData"}], "qualifiedClassName": "QScxmlStateMachine", "signals": [{"access": "public", "arguments": [{"name": "running", "type": "bool"}], "index": 0, "name": "running<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "invokedServices", "type": "QList<QScxmlInvokableService*>"}], "index": 1, "name": "invokedServicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}, {"name": "msg", "type": "QString"}], "index": 2, "name": "log", "returnType": "void"}, {"access": "public", "index": 3, "name": "reachedStableState", "returnType": "void"}, {"access": "public", "index": 4, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "model", "type": "QScxmlDataModel*"}], "index": 5, "name": "dataModelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "initialValues", "type": "QVariantMap"}], "index": 6, "name": "initialValuesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "initialized", "type": "bool"}], "index": 7, "name": "initializedChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "loader", "type": "QScxmlCompiler::Loader*"}], "index": 8, "name": "loaderChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "tableData", "type": "QScxmlTableData*"}], "index": 9, "name": "tableDataChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 10, "name": "start", "returnType": "void"}, {"access": "public", "index": 11, "name": "stop", "returnType": "void"}, {"access": "public", "index": 12, "name": "init", "returnType": "bool"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qscxmlstatemachine.h", "outputRevision": 69}, {"classes": [{"className": "EventLoopHook", "lineNumber": 33, "methods": [{"access": "public", "index": 0, "name": "doProcessEvents", "returnType": "void"}], "object": true, "qualifiedClassName": "QScxmlInternal::EventLoopHook", "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "ScxmlEventRouter", "lineNumber": 52, "object": true, "qualifiedClassName": "QScxmlInternal::ScxmlEventRouter", "signals": [{"access": "public", "arguments": [{"name": "event", "type": "QScxmlEvent"}], "index": 0, "name": "eventOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "StateMachineInfoProxy", "lineNumber": 75, "object": true, "qualifiedClassName": "QScxmlInternal::StateMachineInfoProxy", "signals": [{"access": "public", "arguments": [{"name": "states", "type": "QList<QScxmlStateMachineInfo::StateId>"}], "index": 0, "name": "statesEntered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "states", "type": "QList<QScxmlStateMachineInfo::StateId>"}], "index": 1, "name": "statesExited", "returnType": "void"}, {"access": "public", "arguments": [{"name": "transitions", "type": "QList<QScxmlStateMachineInfo::TransitionId>"}], "index": 2, "name": "transitionsTriggered", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qscxmlstatemachine_p.h", "outputRevision": 69}, {"classes": [{"className": "QScxmlStateMachineInfo", "lineNumber": 27, "object": true, "qualifiedClassName": "QScxmlStateMachineInfo", "signals": [{"access": "public", "arguments": [{"name": "states", "type": "QList<QScxmlStateMachineInfo::StateId>"}], "index": 0, "name": "statesEntered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "states", "type": "QList<QScxmlStateMachineInfo::StateId>"}], "index": 1, "name": "statesExited", "returnType": "void"}, {"access": "public", "arguments": [{"name": "transitions", "type": "QList<QScxmlStateMachineInfo::TransitionId>"}], "index": 2, "name": "transitionsTriggered", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qscxmlstatemachineinfo_p.h", "outputRevision": 69}, {"classes": [{"className": "InvokeDynamicScxmlFactory", "lineNumber": 433, "object": true, "qualifiedClassName": "InvokeDynamicScxmlFactory", "superClasses": [{"access": "public", "name": "QScxmlInvokableServiceFactory"}]}], "inputFile": "qscxmlcompiler.cpp", "outputRevision": 69}]